# 任务 #6: 仙友升星系统

**状态**: pending  
**优先级**: medium  
**依赖**: [5]

## 描述
实现仙友升星机制，提升属性和特殊功能

## 实施详情
开发仙友升星系统，设计升星材料消耗机制，实现升星后的属性提升和特殊功能增强。包括升星动画效果和成功率计算。

### 升星系统设计

#### 1. 星级划分
- 1星：基础形态 (初始状态)
- 2星：进阶形态 (+20%属性)
- 3星：精英形态 (+40%属性)
- 4星：传说形态 (+60%属性)
- 5星：神话形态 (+100%属性)

#### 2. 升星成功率
```javascript
const upgradeRates = {
  "1to2": { rate: 100, materials: { starStone: 1, gold: 1000 } },
  "2to3": { rate: 80, materials: { starStone: 3, gold: 5000 } },
  "3to4": { rate: 60, materials: { starStone: 5, gold: 10000 } },
  "4to5": { rate: 40, materials: { starStone: 10, gold: 20000 } }
};
```

### 升星材料系统

#### 1. 材料类型
- 升星石：主要升星材料
- 金币：升星消耗货币
- 特殊材料：高星级需要的稀有材料
- 同名仙友：可作为升星材料

#### 2. 材料获取途径
- 日常任务奖励
- 活动兑换
- 商店购买
- 分解多余仙友

#### 3. 材料管理
- 材料数量检查
- 消耗计算和扣除
- 材料不足提示
- 获取途径引导

### 升星功能实现

#### 1. StarUpgradeDialog组件
- 当前仙友信息显示
- 升星前后属性对比
- 材料消耗明细
- 成功率显示
- 升星确认按钮

#### 2. 升星流程
```javascript
async upgradeXianyouStar(xianyouId) {
  // 1. 验证材料是否足够
  if (!this.checkMaterials(xianyouId)) return false;
  
  // 2. 计算成功率
  const successRate = this.calculateSuccessRate(xianyouId);
  
  // 3. 执行升星判定
  const success = Math.random() < successRate;
  
  // 4. 消耗材料
  this.consumeMaterials(xianyouId);
  
  // 5. 更新仙友数据
  if (success) {
    this.updateXianyouStar(xianyouId);
  }
  
  // 6. 播放结果动画
  this.playUpgradeAnimation(success);
  
  return success;
}
```

### 属性提升计算

#### 1. 星级属性加成
```javascript
calculateStarBonus(xianyou) {
  const baseAttributes = xianyou.baseAttributes;
  const starMultiplier = [1.0, 1.2, 1.4, 1.6, 2.0][xianyou.currentStar - 1];
  
  return {
    attack: Math.floor(baseAttributes.attack * starMultiplier),
    defense: Math.floor(baseAttributes.defense * starMultiplier),
    hp: Math.floor(baseAttributes.hp * starMultiplier),
    speed: Math.floor(baseAttributes.speed * starMultiplier)
  };
}
```

#### 2. 特殊功能增强
- 洞府功能效果提升
- 好感度获取加成
- 特殊技能解锁
- 外观变化

### 升星动画效果

#### 1. 升星动画组件
- 星星闪烁效果
- 光芒粒子系统
- 仙友形象变化
- 属性数值变化动画

#### 2. 成功/失败反馈
- 成功：金光闪烁，星星点亮
- 失败：暗淡效果，材料消失提示
- 音效配合
- 震动反馈

### 数据库集成

#### 1. 升星数据存储
```javascript
// player_xianyou表字段
{
  currentStar: 3,
  upgradeAttempts: 5,
  lastUpgradeTime: timestamp,
  totalMaterialsUsed: {
    starStone: 15,
    gold: 50000
  }
}
```

#### 2. 云函数支持
- `upgradeXianyouStar` - 升星操作云函数
- `getMaterialInfo` - 获取材料信息
- `calculateUpgradeRate` - 计算升星成功率

### 保底机制

#### 1. 失败保护
- 连续失败后成功率提升
- 保底升星次数限制
- 材料返还机制
- VIP特权加成

#### 2. 数据记录
- 升星历史记录
- 失败次数统计
- 保底状态追踪
- 成功率调整

## 测试策略
测试升星概率和材料消耗，验证升星后属性变化，确保动画效果流畅。

### 测试要点
1. 升星成功率准确性测试
2. 材料消耗计算测试
3. 属性提升计算验证
4. 动画效果流畅性测试
5. 数据保存完整性测试

## 相关文件
- `js/managers/XianyouManager.js`
- `js/ui/StarUpgradeDialog.js` (新建)
- `js/scenes/XianyouDetailScene.js`
- `js/utils/AnimationUtils.js` (新建)
- `cloudfunctions/upgradeXianyouStar/` (新建)

## 完成标准
- [ ] 升星系统逻辑实现完成
- [ ] 升星材料管理正常
- [ ] 属性提升计算准确
- [ ] 升星动画效果流畅
- [ ] 保底机制运行正常
- [ ] 通过所有升星系统测试 