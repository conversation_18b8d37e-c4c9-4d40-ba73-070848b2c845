<execution>
  <constraint>
    ## 微信小游戏平台技术限制
    - **包体大小限制**：代码包不超过4MB，总体积不超过8MB
    - **内存限制**：运行内存建议不超过200MB，避免内存警告
    - **API约束**：只能使用微信小游戏支持的JavaScript API
    - **Canvas限制**：Canvas尺寸受设备性能和内存限制
    - **网络限制**：需要配置合法域名，支持HTTPS协议
    - **平台差异**：iOS和Android在性能和API实现上存在差异
    - **审核要求**：必须符合微信小游戏的内容和技术审核标准
  </constraint>

  <rule>
    ## 强制性开发规则
    - **原生开发原则**：优先使用微信小游戏原生API，避免第三方框架依赖
    - **性能优先**：所有功能实现必须考虑性能影响，确保流畅体验
    - **兼容性要求**：代码必须在主流设备和微信版本上正常运行
    - **资源管理强制**：必须实现完整的资源加载、使用、释放流程
    - **错误处理必需**：所有异步操作和资源加载必须有错误处理
    - **内存管理严格**：禁止内存泄漏，必须及时释放不用的资源
    - **代码规范统一**：遵循CommonJS模块规范，保持代码风格一致
  </rule>

  <guideline>
    ## 开发指导原则
    - **渐进增强**：先实现基础功能，再逐步优化和增强效果
    - **模块化设计**：将功能拆分为独立模块，便于维护和复用
    - **数据驱动**：使用配置文件和数据驱动界面和游戏逻辑
    - **用户体验优先**：优化加载时间、交互响应和视觉效果
    - **调试友好**：添加调试信息和性能监控，便于问题定位
    - **文档完整**：编写清晰的代码注释和使用文档
  </guideline>

  <process>
    ## 微信小游戏开发执行流程

    ### Phase 1: 项目初始化和环境搭建 (30分钟)
    ```javascript
    // 1. 项目结构创建
    创建标准目录结构：
    - js/core/           # 核心系统
    - js/render/         # 渲染模块  
    - js/ui/             # UI组件
    - js/scenes/         # 游戏场景
    - assets/images/     # 图片资源
    - assets/audio/      # 音频资源
    
    // 2. 基础配置文件
    - game.json         # 游戏配置
    - project.config.json # 项目配置
    
    // 3. 核心引擎初始化
    // AppContext.js - 全局上下文管理
    // ResourceLoader.js - 资源加载器
    // SceneManager.js - 场景管理器
    ```

    ### Phase 2: 渲染系统架构设计 (60分钟)
    ```javascript
    // 1. Canvas管理器设计
    class CanvasManager {
        constructor() {
            this.canvas = canvas;
            this.ctx = canvas.getContext('2d');
            this.devicePixelRatio = wx.getSystemInfoSync().pixelRatio;
            this.setupCanvas();
        }
        
        setupCanvas() {
            // 设备适配和高分辨率支持
            const { windowWidth, windowHeight } = wx.getSystemInfoSync();
            this.canvas.width = windowWidth * this.devicePixelRatio;
            this.canvas.height = windowHeight * this.devicePixelRatio;
            this.ctx.scale(this.devicePixelRatio, this.devicePixelRatio);
        }
    }
    
    // 2. 渲染层次管理
    class LayerManager {
        constructor() {
            this.layers = {
                background: new Layer('background', 0),
                game: new Layer('game', 1),
                ui: new Layer('ui', 2),
                overlay: new Layer('overlay', 3)
            };
        }
        
        render(deltaTime) {
            // 按层次顺序渲染
            Object.values(this.layers)
                .sort((a, b) => a.zIndex - b.zIndex)
                .forEach(layer => layer.render(deltaTime));
        }
    }
    ```

    ### Phase 3: UI组件系统开发 (90分钟)
    ```javascript
    // 1. 基础UI组件类
    class UIComponent {
        constructor(x, y, width, height) {
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
            this.visible = true;
            this.interactive = true;
        }
        
        render(ctx) {
            if (!this.visible) return;
            // 渲染逻辑由子类实现
        }
        
        hitTest(x, y) {
            return x >= this.x && x <= this.x + this.width &&
                   y >= this.y && y <= this.y + this.height;
        }
    }
    
    // 2. 按钮组件实现
    class Button extends UIComponent {
        constructor(config) {
            super(config.x, config.y, config.width, config.height);
            this.text = config.text;
            this.onClick = config.onClick;
            this.state = 'normal'; // normal, pressed, disabled
        }
        
        render(ctx) {
            // 根据状态渲染不同样式
            const style = this.getStateStyle();
            this.drawBackground(ctx, style);
            this.drawText(ctx, style);
        }
    }
    ```

    ### Phase 4: 场景管理系统 (60分钟)
    ```javascript
    // 1. 场景基类设计
    class BaseScene {
        constructor() {
            this.components = [];
            this.isLoaded = false;
            this.isActive = false;
        }
        
        async load() {
            // 加载场景资源
            await this.loadResources();
            this.setup();
            this.isLoaded = true;
        }
        
        update(deltaTime) {
            if (!this.isActive) return;
            this.components.forEach(comp => comp.update(deltaTime));
        }
        
        render(ctx) {
            if (!this.isActive) return;
            this.components.forEach(comp => comp.render(ctx));
        }
    }
    
    // 2. 场景切换管理
    class SceneManager {
        constructor() {
            this.scenes = new Map();
            this.currentScene = null;
            this.transitioning = false;
        }
        
        async switchScene(sceneName, transition = null) {
            if (this.transitioning) return;
            this.transitioning = true;
            
            if (transition) {
                await this.playTransition(transition);
            }
            
            if (this.currentScene) {
                this.currentScene.deactivate();
            }
            
            const scene = this.scenes.get(sceneName);
            if (!scene.isLoaded) {
                await scene.load();
            }
            
            this.currentScene = scene;
            scene.activate();
            this.transitioning = false;
        }
    }
    ```

    ### Phase 5: 性能优化实施 (90分钟)
    ```javascript
    // 1. 对象池管理
    class ObjectPool {
        constructor(createFn, resetFn, maxSize = 100) {
            this.createFn = createFn;
            this.resetFn = resetFn;
            this.pool = [];
            this.maxSize = maxSize;
        }
        
        get() {
            if (this.pool.length > 0) {
                return this.pool.pop();
            }
            return this.createFn();
        }
        
        release(obj) {
            if (this.pool.length < this.maxSize) {
                this.resetFn(obj);
                this.pool.push(obj);
            }
        }
    }
    
    // 2. 脏矩形更新
    class DirtyRectManager {
        constructor() {
            this.dirtyRects = [];
        }
        
        addDirtyRect(x, y, width, height) {
            this.dirtyRects.push({ x, y, width, height });
        }
        
        render(ctx) {
            // 只重绘脏矩形区域
            this.dirtyRects.forEach(rect => {
                ctx.clearRect(rect.x, rect.y, rect.width, rect.height);
                this.renderRect(ctx, rect);
            });
            this.dirtyRects.length = 0;
        }
    }
    
    // 3. 资源预加载和管理
    class ResourceManager {
        constructor() {
            this.resources = new Map();
            this.loadQueue = [];
            this.loadingPromises = new Map();
        }
        
        async preloadImages(imageList) {
            const promises = imageList.map(path => this.loadImage(path));
            return Promise.all(promises);
        }
        
        loadImage(path) {
            if (this.resources.has(path)) {
                return Promise.resolve(this.resources.get(path));
            }
            
            if (this.loadingPromises.has(path)) {
                return this.loadingPromises.get(path);
            }
            
            const promise = new Promise((resolve, reject) => {
                const image = wx.createImage();
                image.onload = () => {
                    this.resources.set(path, image);
                    this.loadingPromises.delete(path);
                    resolve(image);
                };
                image.onerror = reject;
                image.src = path;
            });
            
            this.loadingPromises.set(path, promise);
            return promise;
        }
    }
    ```

    ### Phase 6: 输入和交互处理 (45分钟)
    ```javascript
    // 1. 触摸输入管理
    class InputManager {
        constructor(canvas) {
            this.canvas = canvas;
            this.touches = new Map();
            this.setupEventListeners();
        }
        
        setupEventListeners() {
            this.canvas.addEventListener('touchstart', (e) => {
                e.touches.forEach((touch, index) => {
                    this.touches.set(touch.identifier, {
                        x: touch.clientX,
                        y: touch.clientY,
                        startTime: Date.now()
                    });
                });
                this.onTouchStart(e);
            });
            
            this.canvas.addEventListener('touchmove', (e) => {
                e.touches.forEach((touch, index) => {
                    if (this.touches.has(touch.identifier)) {
                        const touchData = this.touches.get(touch.identifier);
                        touchData.x = touch.clientX;
                        touchData.y = touch.clientY;
                    }
                });
                this.onTouchMove(e);
            });
            
            this.canvas.addEventListener('touchend', (e) => {
                e.changedTouches.forEach((touch, index) => {
                    this.touches.delete(touch.identifier);
                });
                this.onTouchEnd(e);
            });
        }
    }
    ```

    ### Phase 7: 调试和测试 (30分钟)
    ```javascript
    // 1. 性能监控
    class PerformanceMonitor {
        constructor() {
            this.frameCount = 0;
            this.lastTime = performance.now();
            this.fps = 0;
            this.memoryUsage = 0;
        }
        
        update() {
            this.frameCount++;
            const currentTime = performance.now();
            
            if (currentTime - this.lastTime >= 1000) {
                this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));
                this.frameCount = 0;
                this.lastTime = currentTime;
                
                // 检查内存使用
                if (wx.getPerformance) {
                    const perf = wx.getPerformance();
                    this.memoryUsage = perf.usedJSHeapSize / 1024 / 1024; // MB
                }
                
                this.reportMetrics();
            }
        }
        
        reportMetrics() {
            console.log(`FPS: ${this.fps}, Memory: ${this.memoryUsage.toFixed(2)}MB`);
            
            // 性能警告
            if (this.fps < 30) {
                console.warn('Performance warning: Low FPS detected');
            }
            if (this.memoryUsage > 150) {
                console.warn('Memory warning: High memory usage detected');
            }
        }
    }
    ```
  </process>

  <criteria>
    ## 开发质量评价标准

    ### 性能标准
    - ✅ 帧率保持在30FPS以上，理想情况60FPS
    - ✅ 内存使用控制在200MB以内，避免内存警告
    - ✅ 启动时间控制在3秒以内
    - ✅ 场景切换流畅，无明显卡顿
    - ✅ 资源加载优化，支持渐进式加载

    ### 兼容性标准
    - ✅ 支持主流Android和iOS设备
    - ✅ 兼容微信7.0以上版本
    - ✅ 适配不同屏幕尺寸和分辨率
    - ✅ 处理设备性能差异，提供降级方案
    - ✅ 网络环境适应性，支持弱网优化

    ### 代码质量标准
    - ✅ 代码结构清晰，模块化程度高
    - ✅ 错误处理完善，异常情况可控
    - ✅ 内存管理规范，无内存泄漏
    - ✅ 注释文档完整，便于维护
    - ✅ 性能监控完备，问题可追踪

    ### 用户体验标准
    - ✅ 界面响应迅速，交互自然流畅
    - ✅ 视觉效果精美，动画过渡自然
    - ✅ 加载提示友好，避免空白等待
    - ✅ 错误提示明确，用户理解容易
    - ✅ 操作反馈及时，增强交互感
  </criteria>
</execution> 