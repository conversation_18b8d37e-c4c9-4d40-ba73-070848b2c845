# 任务 #5: 仙友好感度培养系统

**状态**: pending  
**优先级**: high  
**依赖**: [4]

## 描述
实现仙友好感度培养机制和属性加成系统

## 实施详情
开发好感度培养机制，实现礼物赠送功能，设计好感度进度条和属性加成计算。仙友好感度提升会增加玩家属性，需要与角色系统集成。

### 好感度系统设计

#### 1. 好感度等级划分
- 1级：陌生 (0-99好感度)
- 2级：熟悉 (100-299好感度) 
- 3级：友好 (300-599好感度)
- 4级：亲密 (600-999好感度)
- 5级：挚友 (1000+好感度)

#### 2. 好感度获取方式
- 赠送礼物 (+10-50好感度)
- 每日互动 (+5好感度)
- 洞府陪伴 (+1好感度/小时)
- 完成仙友任务 (+20好感度)

### 礼物系统实现

#### 1. 礼物类型设计
```javascript
const giftTypes = {
  "flower": { name: "鲜花", favorability: 10, cost: 100 },
  "jewelry": { name: "首饰", favorability: 25, cost: 500 },
  "elixir": { name: "仙丹", favorability: 50, cost: 1000 }
};
```

#### 2. 礼物赠送功能
- 礼物选择界面
- 消耗资源验证
- 好感度增加动画
- 仙友反应展示

#### 3. 特殊礼物机制
- 仙友偏好礼物 (额外好感度)
- 节日特殊礼物
- 稀有礼物获取

### 属性加成系统

#### 1. 好感度属性加成公式
```javascript
// 每级好感度提供的属性加成
const favorabilityBonus = {
  1: { attack: 0, defense: 0, hp: 0 },
  2: { attack: 5, defense: 3, hp: 10 },
  3: { attack: 12, defense: 8, hp: 25 },
  4: { attack: 25, defense: 15, hp: 50 },
  5: { attack: 50, defense: 30, hp: 100 }
};
```

#### 2. 总属性加成计算
- 所有仙友好感度加成累加
- 稀有度系数影响 (SSR×1.5, SR×1.2, R×1.0)
- 星级系数影响 (每星+20%基础加成)

#### 3. 实时属性更新
- 好感度变化时实时更新角色属性
- 属性变化动画提示
- 属性来源详细说明

### UI界面开发

#### 1. FavorabilityBar组件
- 好感度进度条显示
- 等级标识和下级所需好感度
- 动画效果和颜色变化
- 触摸显示详细信息

#### 2. GiftDialog组件
- 礼物选择网格布局
- 资源消耗显示
- 确认赠送按钮
- 仙友反应动画

#### 3. 仙友互动界面
- 每日互动按钮
- 仙友心情状态显示
- 互动历史记录
- 特殊事件触发

### 数据管理

#### 1. 好感度数据存储
```javascript
// player_xianyou表扩展字段
{
  favorability: 150,
  favorabilityLevel: 2,
  lastInteractionTime: timestamp,
  totalGiftsReceived: 5,
  dailyInteractionCount: 1
}
```

#### 2. 数据同步机制
- 好感度变化实时保存
- 离线时间计算洞府陪伴加成
- 每日重置互动次数
- 数据一致性验证

### 集成角色系统

#### 1. 属性计算集成
- 在Character.js中添加仙友属性加成计算
- 属性面板显示仙友加成来源
- 实时更新机制
- 数据缓存优化

#### 2. 事件通知系统
- 好感度等级提升事件
- 属性变化通知事件
- UI更新触发机制
- 成就解锁检测

## 测试策略
测试好感度增长曲线，验证属性加成计算准确性，确保数据同步正常。

### 测试要点
1. 好感度增长机制测试
2. 礼物赠送功能测试
3. 属性加成计算准确性测试
4. 数据保存和同步测试
5. UI交互响应测试

## 相关文件
- `js/managers/XianyouManager.js`
- `js/ui/FavorabilityBar.js` (新建)
- `js/ui/GiftDialog.js` (新建)
- `js/scenes/XianyouDetailScene.js`
- `js/models/Character.js`
- `js/managers/PlayerDataManager.js`

## 完成标准
- [ ] 好感度培养机制实现完成
- [ ] 礼物赠送系统正常运行
- [ ] 属性加成计算准确
- [ ] UI界面交互流畅
- [ ] 数据同步机制稳定
- [ ] 通过所有好感度系统测试 