/**
 * 挂机游历场景类
 * 获取灵石、一阶药材和功法修炼点
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import game from '../../game';
import IdleConfig from '../config/IdleConfig';

class IdleScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager, resources) {
    super(ctx, screenWidth, screenHeight, sceneManager);

    // 场景资源
    this.resources = resources || {};

    // 当前选中的底部导航项
    this.selectedTabIndex = 0; // 主页对应的导航索引

    // 返回按钮
    this.backButton = null;

    // 上次领取时间
    this.lastClaimTime = 0;

    // 基础每小时收益（会根据挂机战斗结果更新）
    this.baseHourlyRewards = {
      lingshi: 100,    // 灵石产出
      herbs: 5,        // 一阶药材产出
      functionPoints: 2, // 功法修炼点产出
      beastMaterial1: 3, // 一阶兽材产出
      beastMaterial2: 0, // 二阶兽材产出，默认为0
      lianlidian: 5      // 历练点产出
    };

    // 从存储中恢复挂机收益
    this.loadIdleRewards();

    // 收益加成系数（基于主线关卡进度）
    this.progressMultiplier = 0.1; // 每通过一章增加10%收益

    // 地点选择相关
    this.selectedLocationId = 'bamboo_forest'; // 默认选择竹林小径
    this.availableLocations = [];
    this.locationButtons = [];
    this.showLocationSelector = false;
  }

  // 获取玩家数据
  getPlayerData() {
    if (game.gameStateManager) {
      return game.gameStateManager.getPlayer() || {};
    }
    return {};
  }

  // 保存玩家数据
  savePlayerData() {
    if (game.gameStateManager) {
      game.gameStateManager.saveGameState();
    }
  }

  // 初始化UI
  initUI() {
    // 清空UI元素
    this.clearUIElements();

    
  }

  // 创建返回按钮
  createBackButton() {
    const buttonWidth = 80;
    const buttonHeight = 40;
    const margin = 10;

    this.backButton = new Button(
      this.ctx,
      margin,
      margin,
      buttonWidth,
      buttonHeight,
      '返回',
      null,
      null,
      () => {
        console.log('点击返回按钮');
        // 返回到主页面，携带from参数
        this.sceneManager.showScene('main', { from: 'idle' });
      }
    );

    this.addUIElement(this.backButton);
  }

  // 创建领取按钮
  createClaimButton() {
    const buttonWidth = 160;
    const buttonHeight = 60;

    // 计算可领取的奖励
    const rewards = this.calculateRewards();
    const canClaim = rewards.hasRewards;

    this.claimButton = new Button(
      this.ctx,
      (this.screenWidth - buttonWidth) / 2,
      this.screenHeight - 200,
      buttonWidth,
      buttonHeight,
      '领取奖励',
      null,
      canClaim ? null : 'rgba(100, 100, 100, 0.7)',
      () => {
        if (canClaim) {
          this.claimRewards();
        } else {
          wx.showToast({
            title: '暂无可领取的奖励',
            icon: 'none',
            duration: 2000
          });
        }
      }
    );

    this.addUIElement(this.claimButton);
  }

  // 创建快速挂机按钮
  createQuickIdleButton() {
    const buttonWidth = 160;
    const buttonHeight = 60;

    this.quickIdleButton = new Button(
      this.ctx,
      (this.screenWidth - buttonWidth) / 2,
      this.screenHeight - 120,
      buttonWidth,
      buttonHeight,
      '快速游历(10仙玉)',
      null,
      null,
      () => {
        this.confirmQuickIdle();
      }
    );

    this.addUIElement(this.quickIdleButton);
  }

  // 创建挂机战斗按钮
  createIdleBattleButton() {
    const buttonWidth = 160;
    const buttonHeight = 40;

    this.idleBattleButton = new Button(
      this.ctx,
      (this.screenWidth - buttonWidth) / 2,
      this.screenHeight - 280,
      buttonWidth,
      buttonHeight,
      '挂机战斗计算',
      null,
      null,
      () => {
        this.startIdleBattle();
      }
    );

    this.addUIElement(this.idleBattleButton);
  }

  // 开始挂机战斗
  startIdleBattle() {
    console.log('开始挂机战斗计算');

    // 获取主角色
    const mainCharacter = game.gameStateManager.getCharacterById(1);
    if (!mainCharacter) {
      wx.showToast({
        title: '未找到角色',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 获取当前选择的地点配置
    const locationConfig = IdleConfig.getLocationById(this.selectedLocationId);
    if (!locationConfig) {
      wx.showToast({
        title: '地点配置错误',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 创建玩家角色数据
    const playerCharacter = this.createPlayerCharacterForBattle(mainCharacter);

    // 使用IdleBattleScene战斗系统
    this.sceneManager.showScene('idleBattle', {
      playerCharacter: playerCharacter,
      locationConfig: locationConfig,
      onComplete: (result) => {
        this.handleIdleBattleComplete(result, locationConfig);
      }
    });
  }

  /**
   * 为战斗创建玩家角色对象
   * @param {Object} character 角色对象
   * @returns {Object} 战斗角色对象
   */
  createPlayerCharacterForBattle(character) {
    const attributes = character.getAttributes ? character.getAttributes() : {
      hp: 1000,
      attack: 100,
      defense: 50
    };

    return {
      id: character.id,
      name: character.name || '修仙者',
      level: character.level || 1,
      
      // 战斗属性
      hp: attributes.hp || 1000,
      maxHp: attributes.hp || 1000,
      attack: attributes.attack || 100,
      defense: attributes.defense || 50,
      attackSpeed: 1.0,
      critical: 0.05,
      criticalDamage: 1.5,
      
      // 技能
      skills: this.getPlayerSkills(character),
      
      // 状态
      isAlive: true,
      type: 'player'
    };
  }

  /**
   * 为战斗创建敌人角色对象
   * @param {Object} locationConfig 地点配置
   * @returns {Object} 敌人角色对象
   */
  createEnemyCharacterForBattle(locationConfig) {
    const enemy = locationConfig.normalEnemy;
    
    return {
      id: `enemy_${locationConfig.id}`,
      name: enemy.name,
      level: enemy.level || 1,
      
      // 战斗属性
      hp: enemy.hpBase || 800,
      maxHp: enemy.hpBase || 800,
      attack: enemy.attackBase || 80,
      defense: enemy.defenseBase || 40,
      attackSpeed: enemy.attackSpeed || 1.0,
      critical: 0.05,
      criticalDamage: 1.5,
      
      // 技能
      skills: enemy.skills || [],
      
      // 状态
      isAlive: true,
      type: 'enemy'
    };
  }

  /**
   * 获取玩家技能列表
   * @param {Object} character 角色对象
   * @returns {Array} 技能列表
   */
  getPlayerSkills(character) {
    const skills = [];
    
    // 添加基础攻击技能
    skills.push({
      id: 'basic_attack',
      name: '基础攻击',
      type: 'damage',
      cooldown: 3000,
      castTime: 500,
      damage: 120,
      damageMultiplier: 1.2,
      description: '基础攻击技能'
    });

    // 添加治疗技能
    skills.push({
      id: 'heal',
      name: '治疗术',
      type: 'heal',
      cooldown: 8000,
      castTime: 1000,
      healAmount: 200,
      description: '恢复生命值'
    });

    return skills;
  }

  /**
   * 处理IdleBattleScene战斗完成
   * @param {Object} result 战斗结果
   * @param {Object} locationConfig 地点配置
   */
  handleIdleBattleComplete(result, locationConfig) {
    console.log('挂机战斗完成，结果：', result);

    // IdleBattleScene返回的结果格式：{ victory: boolean, enemyKilled: number, playerAlive: boolean }
    if (result && result.victory && result.playerAlive) {
      // 胜利时提高收益，根据击杀敌人数量调整收益
      const killMultiplier = Math.max(1, result.enemyKilled / 10); // 每击杀10个敌人增加1倍收益
      const newRewards = {
        lingshi: Math.floor(this.baseHourlyRewards.lingshi * (1.2 + killMultiplier * 0.1)),
        herbs: this.baseHourlyRewards.herbs + Math.floor(killMultiplier),
        functionPoints: this.baseHourlyRewards.functionPoints + Math.floor(killMultiplier),
        beastMaterial1: this.baseHourlyRewards.beastMaterial1 + Math.floor(killMultiplier),
        lianlidian: this.baseHourlyRewards.lianlidian + Math.floor(killMultiplier * 2)
      };

      this.updateIdleRewards(newRewards);

      wx.showToast({
        title: `战斗胜利！击杀${result.enemyKilled}个敌人，收益提升`,
        icon: 'success',
        duration: 2000
      });
    } else {
      // 失败时保持原有收益
      wx.showToast({
        title: '战斗失败，收益不变',
        icon: 'none',
        duration: 2000
      });
    }

    // 返回挂机场景
    this.sceneManager.showScene('idle');
  }

  /**
   * 处理战斗完成（旧版本，保留兼容性）
   * @param {Object} result 战斗结果
   * @param {Object} locationConfig 地点配置
   */
  handleBattleComplete(result, locationConfig) {
    console.log('挂机战斗完成，结果：', result);

    // 根据战斗结果更新收益
    if (result && (result.result === 'victory' || result.victory === true)) {
      // 胜利时提高收益
      const newRewards = {
        lingshi: Math.floor(this.baseHourlyRewards.lingshi * 1.2),
        herbs: this.baseHourlyRewards.herbs + 1,
        functionPoints: this.baseHourlyRewards.functionPoints + 1,
        beastMaterial1: this.baseHourlyRewards.beastMaterial1 + 1,
        lianlidian: this.baseHourlyRewards.lianlidian + 2
      };

      this.updateIdleRewards(newRewards);

      wx.showToast({
        title: '战斗胜利！收益提升',
        icon: 'success',
        duration: 2000
      });
    } else {
      // 失败时保持原有收益
      wx.showToast({
        title: '战斗失败，收益不变',
        icon: 'none',
        duration: 2000
      });
    }

    // 返回挂机场景
    this.sceneManager.showScene('idle');
  }

  // 创建地点选择按钮
  createLocationSelectorButton() {
    const buttonWidth = 160;
    const buttonHeight = 40;

    this.locationSelectorButton = new Button(
      this.ctx,
      (this.screenWidth - buttonWidth) / 2,
      this.screenHeight - 330,
      buttonWidth,
      buttonHeight,
      '选择地点',
      null,
      null,
      () => {
        this.toggleLocationSelector();
      }
    );

    this.addUIElement(this.locationSelectorButton);
  }

  // 切换地点选择器显示状态
  toggleLocationSelector() {
    this.showLocationSelector = !this.showLocationSelector;

    if (this.showLocationSelector) {
      this.createLocationButtons();
    } else {
      this.clearLocationButtons();
    }
  }

  // 更新可用地点
  updateAvailableLocations() {
    const mainCharacter = game.gameStateManager.getCharacterById(1);
    const characterLevel = mainCharacter ? mainCharacter.level : 1;

    this.availableLocations = IdleConfig.getAvailableLocations(characterLevel);

    console.log(`角色等级: ${characterLevel}, 可用地点数量: ${this.availableLocations.length}`);
    this.availableLocations.forEach(loc => {
      console.log(`可用地点: ${loc.name}, 需要等级: ${loc.requiredLevel}`);
    });

    // 如果当前选择的地点不可用，切换到第一个可用地点
    const currentLocationAvailable = this.availableLocations.some(loc => loc.id === this.selectedLocationId);
    if (!currentLocationAvailable && this.availableLocations.length > 0) {
      this.selectedLocationId = this.availableLocations[0].id;
    }

    // 更新地点选择按钮的显示文字
    const selectedLocation = IdleConfig.getLocationById(this.selectedLocationId);
    if (selectedLocation && this.locationSelectorButton) {
      this.locationSelectorButton.text = selectedLocation.name;
    }
  }

  // 创建地点选择按钮
  createLocationButtons() {
    this.clearLocationButtons();

    const buttonWidth = 200;
    const buttonHeight = 50;
    const buttonSpacing = 10;
    const startY = 150;

    this.availableLocations.forEach((location, index) => {
      const button = new Button(
        this.ctx,
        (this.screenWidth - buttonWidth) / 2,
        startY + index * (buttonHeight + buttonSpacing),
        buttonWidth,
        buttonHeight,
        location.name,
        null,
        this.selectedLocationId === location.id ? 'rgba(255, 215, 0, 0.3)' : null,
        () => {
          this.selectLocation(location.id);
        }
      );

      this.locationButtons.push(button);
      this.addUIElement(button);
    });
  }

  // 清除地点选择按钮
  clearLocationButtons() {
    this.locationButtons.forEach(button => {
      this.removeUIElement(button);
    });
    this.locationButtons = [];
  }

  // 选择地点
  selectLocation(locationId) {
    this.selectedLocationId = locationId;
    this.showLocationSelector = false;
    this.clearLocationButtons();

    // 更新地点选择按钮文字
    const location = IdleConfig.getLocationById(locationId);
    if (location && this.locationSelectorButton) {
      this.locationSelectorButton.text = location.name;
    }
  }

  // 加载挂机收益
  loadIdleRewards() {
    const playerData = this.getPlayerData();
    if (playerData.idleRewards) {
      this.baseHourlyRewards = { ...this.baseHourlyRewards, ...playerData.idleRewards };
      console.log('加载挂机收益:', this.baseHourlyRewards);
    }
  }

  // 保存挂机收益
  saveIdleRewards() {
    const playerData = this.getPlayerData();
    playerData.idleRewards = { ...this.baseHourlyRewards };

    game.gameStateManager.setPlayer(playerData);
    game.gameStateManager.saveGameState();

    console.log('保存挂机收益:', this.baseHourlyRewards);
  }

  // 更新挂机收益（由挂机战斗调用）
  updateIdleRewards(newRewards) {
    console.log('更新挂机收益:', newRewards);

    // 更新基础收益
    Object.keys(newRewards).forEach(key => {
      if (this.baseHourlyRewards.hasOwnProperty(key)) {
        this.baseHourlyRewards[key] = newRewards[key];
      }
    });

    // 保存到存储
    this.saveIdleRewards();

    // 重新创建领取按钮以更新显示
    this.createClaimButton();
  }

  // 确认快速挂机
  confirmQuickIdle() {
    const playerData = this.getPlayerData();
    const resources = playerData.resources || {};
    const xianyu = resources.xianyu || 0;

    if (xianyu < 10) {
      wx.showToast({
        title: '仙玉不足',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    wx.showModal({
      title: '快速游历',
      content: '花费10仙玉获得2小时的游历收益，是否确认？',
      success: (res) => {
        if (res.confirm) {
          this.doQuickIdle();
        }
      }
    });
  }

  // 执行快速挂机
  doQuickIdle() {
    const playerData = this.getPlayerData();
    const resources = playerData.resources || {};

    // 扣除仙玉
    resources.xianyu = (resources.xianyu || 0) - 10;

    // 更新玩家数据
    game.gameStateManager.setPlayer({
      ...playerData,
      resources: resources
    });

    // 计算2小时的游历奖励
    const storyProgress = this.getStoryProgress();
    let multiplier = 1 + (storyProgress * this.progressMultiplier);

    // 获取VIP加成
    const vipBonus = this.getVIPBonus(playerData.vipLevel);
    if (vipBonus > 0) {
      multiplier += vipBonus;
    }

    // 获取玩家境界决定兽材掉落
    const mainCharacter = game.gameStateManager.getCharacterById(1);
    const playerRealm = mainCharacter ? mainCharacter.cultivation : '练气期一层';

    // 根据玩家境界决定兽材掉落
    let beastMaterial1 = this.baseHourlyRewards.beastMaterial1;
    let beastMaterial2 = this.baseHourlyRewards.beastMaterial2;

    // 如果玩家境界达到筑基期，有机会掉落二阶兽材
    if (playerRealm.includes('筑基期')) {
      beastMaterial2 = 1; // 每小时基础产出1个二阶兽材
    }

    const rewards = {
      lingshi: Math.floor(this.baseHourlyRewards.lingshi * 2 * multiplier),
      herbs: Math.floor(this.baseHourlyRewards.herbs * 2 * multiplier),
      functionPoints: Math.floor(this.baseHourlyRewards.functionPoints * 2 * multiplier),
      beastMaterial1: Math.floor(beastMaterial1 * 2 * multiplier),
      beastMaterial2: Math.floor(beastMaterial2 * 2 * multiplier),
      lianlidian: Math.floor(this.baseHourlyRewards.lianlidian * 2 * multiplier)
    };

    // 增加灵石
    resources.lingshi = (resources.lingshi || 0) + rewards.lingshi;

    // 增加功法修炼点
    resources.functionPoints = (resources.functionPoints || 0) + rewards.functionPoints;

    // 添加一阶药材
    this.addHerbItem(rewards.herbs);

    // 添加兽材
    this.addBeastMaterialItem(1, rewards.beastMaterial1);
    this.addBeastMaterialItem(2, rewards.beastMaterial2);

    // 更新玩家数据
    game.gameStateManager.setPlayer({
      ...playerData,
      resources: resources
    });

    // 准备提示消息
    let toastMessage = `快速游历完成！获得${rewards.lingshi}灵石、${rewards.herbs}一阶药材、${rewards.beastMaterial1}一阶兽材`;

    // 如果有VIP加成，添加到提示中
    if (vipBonus > 0) {
      toastMessage += `\n包含VIP加成: +${(vipBonus * 100).toFixed(0)}%`;
    }

    // 显示领取成功消息
    wx.showToast({
      title: toastMessage,
      icon: 'success',
      duration: 2000
    });
  }

  // 底部导航栏选中回调
  onTabSelected(index) {
    // 如果点击的是当前选中的项，不做处理
    if (this.selectedTabIndex === index) {
      return;
    }

    // 更新选中的索引
    this.selectedTabIndex = index;

    // 根据索引切换场景
    switch (index) {
      case 0:
        // 主页
        this.sceneManager.showScene('main', { from: 'idle' });
        break;
      case 1:
        // 角色页面 - 直接进入女剑仙角色详情页
        this.sceneManager.showScene('characterDetail', { characterId: 1 });
        break;
      case 2:
        // 洞府页面
        this.sceneManager.showScene('dongfu');
        break;
      case 3:
        // 试炼页面
        this.sceneManager.showScene('trial');
        break;
      case 4:
        // 背包页面
        this.sceneManager.showScene('backpack');
        break;
    }
  }

  // 计算可领取的奖励
  calculateRewards() {
    const player = this.getPlayerData();

    // 获取上次领取时间
    const lastClaim = player.lastIdleClaim || 0;

    // 当前时间
    const now = Date.now();

    // 计算经过的小时数
    const hoursPassed = (now - lastClaim) / (60 * 60 * 1000);

    // 如果经过的时间不足15分钟，不提供奖励
    if (hoursPassed < 0.25) {
      return {
        hasRewards: false,
        rewards: {
          lingshi: 0,
          herbs: 0,
          functionPoints: 0,
          beastMaterial1: 0,
          beastMaterial2: 0,
          lianlidian: 0
        },
        hoursPassed: hoursPassed
      };
    }

    // 获取主线进度
    const storyProgress = this.getStoryProgress();

    // 计算加成倍率
    let multiplier = 1 + (storyProgress * this.progressMultiplier);

    // 获取VIP加成
    const vipBonus = this.getVIPBonus(player.vipLevel);
    if (vipBonus > 0) {
      multiplier += vipBonus;
    }

    // 计算最终奖励（最多累积24小时）
    const cappedHours = Math.min(hoursPassed, 24);

    // 获取玩家境界决定兽材掉落
    const mainCharacter = game.gameStateManager.getCharacterById(1);
    const playerRealm = mainCharacter ? mainCharacter.cultivation : '练气期一层';

    // 根据玩家境界决定兽材掉落
    let beastMaterial1 = this.baseHourlyRewards.beastMaterial1;
    let beastMaterial2 = this.baseHourlyRewards.beastMaterial2;

    // 如果玩家境界达到筑基期，有机会掉落二阶兽材
    if (playerRealm.includes('筑基期')) {
      beastMaterial2 = 1; // 每小时基础产出1个二阶兽材
    }

    // 计算最终奖励
    const rewards = {
      lingshi: Math.floor(this.baseHourlyRewards.lingshi * cappedHours * multiplier),
      herbs: Math.floor(this.baseHourlyRewards.herbs * cappedHours * multiplier),
      functionPoints: Math.floor(this.baseHourlyRewards.functionPoints * cappedHours * multiplier),
      beastMaterial1: Math.floor(beastMaterial1 * cappedHours * multiplier),
      beastMaterial2: Math.floor(beastMaterial2 * cappedHours * multiplier),
      lianlidian: Math.floor(this.baseHourlyRewards.lianlidian * cappedHours * multiplier)
    };

    return {
      hasRewards: rewards.lingshi > 0 || rewards.herbs > 0 || rewards.functionPoints > 0 || rewards.beastMaterial1 > 0 || rewards.beastMaterial2 > 0 || rewards.lianlidian > 0,
      rewards: rewards,
      hoursPassed: cappedHours,
      vipBonus: vipBonus > 0 ? vipBonus : 0
    };
  }

  // 获取VIP加成
  getVIPBonus(vipLevel) {
    if (!vipLevel || vipLevel <= 0) return 0;

    // 获取VIP系统
    if (!game.vipSystem) return 0;

    // 获取VIP等级信息
    const vipInfo = game.vipSystem.getVIPLevelInfo(vipLevel);
    if (!vipInfo || !vipInfo.benefits) return 0;

    // 返回VIP游历收益加成
    return vipInfo.benefits.idleRewardBonus || 0;
  }

  // 获取主线故事通关进度（已通关的章节数）
  getStoryProgress() {
    const playerData = this.getPlayerData();
    let maxChapter = 0;

    // 检查每个章节的进度
    for (let chapterId = 1; chapterId <= 10; chapterId++) {
      const chapterKey = `chapter_${chapterId}`;
      const chapterProgress = playerData[chapterKey] || 0;

      // 如果该章节有进度，则更新最大已通关章节
      if (chapterProgress > 0) {
        maxChapter = chapterId;
      }
    }

    return maxChapter;
  }

  // 领取奖励
  claimRewards() {
    const player = this.getPlayerData();
    const rewardsData = this.calculateRewards();

    if (!rewardsData.hasRewards) {
      wx.showToast({
        title: '暂无可领取的奖励',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 获取当前资源
    const resources = player.resources || {};

    // 更新灵石
    resources.lingshi = (resources.lingshi || 0) + rewardsData.rewards.lingshi;

    // 更新功法修炼点
    resources.functionPoints = (resources.functionPoints || 0) + rewardsData.rewards.functionPoints;

    // 更新历练点
    resources.lianlidian = (resources.lianlidian || 0) + rewardsData.rewards.lianlidian;

    // 更新上次领取时间
    player.lastIdleClaim = Date.now();

    // 添加一阶药材
    this.addHerbItem(rewardsData.rewards.herbs);

    // 添加兽材
    this.addBeastMaterialItem(1, rewardsData.rewards.beastMaterial1);
    this.addBeastMaterialItem(2, rewardsData.rewards.beastMaterial2);

    // 更新玩家数据
    game.gameStateManager.setPlayer({
      ...player,
      resources: resources
    });

    // 显示领取成功消息
    wx.showToast({
      title: `游历奖励领取成功！获得${rewardsData.rewards.lingshi}灵石、${rewardsData.rewards.herbs}一阶药材、${rewardsData.rewards.beastMaterial1}一阶兽材`,
      icon: 'success',
      duration: 2000
    });

    // 重新创建领取按钮
    this.createClaimButton();
  }

  // 添加一阶药材到背包
  addHerbItem(count) {
    if (!count || count <= 0) return;

    const items = game.gameStateManager.getItems();

    // 检查是否已有一阶药材
    const existingHerb = items.find(item => item.name === '一阶药材');

    if (existingHerb) {
      // 已有药材，增加数量
      existingHerb.count += count;
      game.gameStateManager.updateItem(existingHerb.id, existingHerb);
    } else {
      // 没有药材，创建新药材
      const newHerb = {
        id: Date.now(), // 生成唯一ID
        name: '一阶药材',
        type: 'material',
        quality: 1,
        count: count,
        description: '初级炼丹材料，可用于炼制一阶丹药'
      };

      game.gameStateManager.addItem(newHerb);
    }
  }

  // 添加兽材到背包
  addBeastMaterialItem(tier, count) {
    if (!count || count <= 0) return;

    const items = game.gameStateManager.getItems();
    const itemName = tier === 1 ? '一阶兽材' : '二阶兽材';
    const itemQuality = tier === 1 ? 1 : 2;
    const itemDescription = tier === 1 ?
      '初级锻造材料，可用于锻造一阶装备' :
      '中级锻造材料，可用于锻造二阶装备，需要筑基期以上境界才能使用';

    // 检查是否已有兽材
    const existingMaterial = items.find(item => item.name === itemName);

    if (existingMaterial) {
      // 已有兽材，增加数量
      existingMaterial.count += count;
      game.gameStateManager.updateItem(existingMaterial.id, existingMaterial);
    } else {
      // 没有兽材，创建新兽材
      const newMaterial = {
        id: `beast_material_${tier}_${Date.now()}`, // 生成唯一ID
        name: itemName,
        type: 'material',
        quality: itemQuality,
        count: count,
        tier: tier,
        description: itemDescription
      };

      game.gameStateManager.addItem(newMaterial);
    }
  }

  // 场景显示回调
  onShow(params) {
    // 清空UI元素
    this.clearUIElements();

    // 初始化UI
    this.initUI();

    // 更新选中的导航项
    this.selectedTabIndex = 0;
  }

  // 处理触摸结束事件
  handleTouchEnd(x, y) {
    // 检查底部导航栏点击
    const tabBarHeight = 80;
    const tabBarY = this.screenHeight - tabBarHeight;
    
    if (y >= tabBarY) {
      const tabWidth = this.screenWidth / 5;
      const tabIndex = Math.floor(x / tabWidth);
      
      if (tabIndex >= 0 && tabIndex < 5) {
        this.onTabSelected(tabIndex);
        return true; // 表示事件已处理
      }
    }

    // 调用父类的触摸处理
    return super.handleTouchEnd(x, y);
  }

  // 绘制场景
  drawScene() {
    // 绘制背景
    this.drawBackground();

    // 绘制顶部导航栏
    this.drawHeader();

    // 绘制闲置修炼信息
    this.drawIdleInfo();

    // 绘制底部导航栏
    this.drawTabBar();
  }

  // 绘制背景
  drawBackground() {
    // 如果有背景图资源，使用背景图
    if (this.resources && this.resources.mainBg) {
      try {
        // 确保背景图覆盖整个屏幕，包括底部导航栏区域
        const bgWidth = this.screenWidth;
        const bgHeight = this.screenHeight; // 使用屏幕高度确保完全覆盖

        this.ctx.drawImage(
          this.resources.mainBg,
          0,
          0,
          bgWidth,
          bgHeight
        );
      } catch (error) {
        console.error('绘制背景图失败', error);
        this.drawDefaultBackground();
      }
    } else {
      // 如果没有背景图资源，使用渐变色背景
      this.drawDefaultBackground();
    }
  }

  // 绘制默认背景
  drawDefaultBackground() {
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, '#1a2a6c');
    gradient.addColorStop(0.5, '#b21f1f');
    gradient.addColorStop(1, '#fdbb2d');

    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }

  // 绘制顶部导航栏
  drawHeader() {
    const headerHeight = 80;
    const safeAreaHeight = 40;

    // 绘制安全区域
    this.ctx.fillStyle = '#000000';
    this.ctx.fillRect(0, 0, this.screenWidth, safeAreaHeight);

    // 绘制顶部导航栏背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, safeAreaHeight, this.screenWidth, headerHeight - safeAreaHeight);

    // 绘制标题
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('闲置修炼', this.screenWidth / 2, safeAreaHeight + 30);

    // 绘制玩家信息
    const player = game.gameStateManager.getPlayer();
    if (player) {
      // 绘制玩家昵称和等级
      this.ctx.font = '16px Arial';
      this.ctx.textAlign = 'left';
      this.ctx.fillText(`${player.nickname} Lv.${player.level}`, 20, safeAreaHeight + 60);

      // 绘制资源信息
      this.ctx.textAlign = 'right';
      this.ctx.fillText(`灵石: ${player.resources.lingshi || 0}`, this.screenWidth - 20, safeAreaHeight + 60);
    }
  }

  // 绘制闲置修炼信息
  drawIdleInfo() {
    const headerHeight = 120; // 包含安全区域的总高度
    const infoY = headerHeight + 20;

    // 绘制闲置修炼说明
    this.ctx.font = '20px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('离线修炼', this.screenWidth / 2, infoY);

    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#cccccc';
    this.ctx.fillText('即使离线也能获得修炼奖励', this.screenWidth / 2, infoY + 30);

    // 显示当前选择的修炼地点
    if (this.selectedLocation) {
      this.ctx.font = '18px Arial';
      this.ctx.fillStyle = '#FFD700';
      this.ctx.fillText(`当前地点: ${this.selectedLocation.name}`, this.screenWidth / 2, infoY + 60);
      
      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = '#cccccc';
      this.ctx.fillText(this.selectedLocation.description, this.screenWidth / 2, infoY + 85);
    }

    // 显示奖励预览
    const rewards = this.calculateRewards();
    if (rewards) {
      this.ctx.font = '16px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('每小时可获得:', this.screenWidth / 2, infoY + 120);

      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = '#90EE90';
      this.ctx.fillText(`灵石: ${rewards.rewards.lingshi}`, this.screenWidth / 2, infoY + 145);
      this.ctx.fillText(`功法修炼点: ${rewards.rewards.functionPoints}`, this.screenWidth / 2, infoY + 165);
      this.ctx.fillText(`历练点: ${rewards.rewards.lianlidian}`, this.screenWidth / 2, infoY + 185);
    }
  }

  // 绘制底部导航栏
  drawTabBar() {
    const tabBarHeight = 80;
    const tabBarY = this.screenHeight - tabBarHeight;
    const tabWidth = this.screenWidth / 5;

    // 导航栏数据
    const tabData = [
      { icon: '🏠', text: '主页', index: 0 },
      { icon: '👤', text: '角色', index: 1 },
      { icon: '🏔️', text: '洞府', index: 2 },
      { icon: '⚔️', text: '试炼', index: 3 },
      { icon: '🎒', text: '背包', index: 4 }
    ];

    // 绘制每个导航按钮
    for (let i = 0; i < tabData.length; i++) {
      const tab = tabData[i];
      const tabX = i * tabWidth;
      const centerX = tabX + tabWidth / 2;
      const isSelected = this.selectedTabIndex === i;

      // 绘制按钮背景（圆角矩形）
      const buttonRadius = 25;
      const buttonY = tabBarY + 10;
      const buttonHeight = 60;

      // 使用BaseScene的drawRoundRect方法
      const fillStyle = isSelected ? 'rgba(255, 215, 0, 0.3)' : 'rgba(255, 255, 255, 0.1)';
      this.drawRoundRect(tabX + 5, buttonY, tabWidth - 10, buttonHeight, buttonRadius, fillStyle);

      // 绘制图标
      const iconSize = isSelected ? 32 : 28;
      this.ctx.font = `${iconSize}px Arial`;
      this.ctx.textAlign = 'center';
      this.ctx.fillText(tab.icon, centerX, tabBarY + 35);

      // 绘制文字
      this.ctx.font = '12px Arial';
      this.ctx.fillStyle = isSelected ? '#FFD700' : '#FFFFFF';
      this.ctx.fillText(tab.text, centerX, tabBarY + 55);
    }
  }
}

// 使用CommonJS格式导出，确保与项目导入方式兼容
module.exports = IdleScene;