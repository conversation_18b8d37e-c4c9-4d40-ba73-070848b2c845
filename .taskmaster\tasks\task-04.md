# 任务 #4: 仙友系统核心实现

**状态**: pending  
**优先级**: high  
**依赖**: [3]

## 描述
实现仙友系统的核心功能，包括抽取、管理和基础属性

## 实施详情
开发仙友抽取系统，设计仙友数据模型，实现仙友管理界面。包括仙友卡片展示、基础属性查看、抽取动画等核心功能。创建XianyouManager和相关场景。

### 仙友数据模型设计

#### 1. 仙友模板数据结构
```javascript
{
  id: "xianyou_001",
  name: "月华仙子",
  rarity: "SSR", // SSR, SR, R
  type: "炼丹", // 炼丹, 炼器, 通用
  baseAttributes: {
    attack: 100,
    defense: 80,
    hp: 150,
    speed: 90
  },
  specialAbility: "提升炼丹成功率20%",
  favorabilityLevels: [0, 100, 300, 600, 1000], // 好感度等级阈值
  starLevels: 5, // 最大星级
  description: "来自月宫的仙子，擅长炼制各种丹药"
}
```

#### 2. 玩家仙友数据结构
```javascript
{
  templateId: "xianyou_001",
  currentStar: 1,
  favorability: 0,
  favorabilityLevel: 1,
  isPlacedInDongfu: false,
  dongfuLocation: null, // "liandan", "lianqi", "juzhu"
  acquiredTime: timestamp
}
```

### 仙友管理器开发

#### 1. XianyouManager核心功能
- 仙友模板数据管理
- 玩家仙友数据管理
- 仙友抽取概率计算
- 好感度计算和管理
- 升星功能实现
- 属性加成计算

#### 2. 关键方法实现
```javascript
class XianyouManager {
  // 抽取仙友
  async drawXianyou(drawType)
  
  // 获取仙友列表
  getPlayerXianyouList()
  
  // 计算仙友属性加成
  calculateXianyouBonus()
  
  // 增加好感度
  addFavorability(xianyouId, amount)
  
  // 升星操作
  upgradeXianyouStar(xianyouId)
  
  // 洞府放置
  placeInDongfu(xianyouId, location)
}
```

### 仙友抽取系统

#### 1. 抽取概率设计
- SSR: 1% (顶级仙友)
- SR: 9% (高级仙友)  
- R: 90% (普通仙友)

#### 2. 抽取类型
- 单次抽取 (消耗仙缘石×1)
- 十连抽取 (消耗仙缘石×10，保底SR)
- 免费抽取 (每日一次)

#### 3. 抽取动画效果
- 抽取按钮动画
- 卡片翻转效果
- 稀有度光效展示
- 新仙友获得提示

### 仙友场景开发

#### 1. XianyouScene (仙友主界面)
- 仙友列表展示
- 抽取入口按钮
- 仙友筛选功能
- 快速操作菜单

#### 2. XianyouDrawScene (仙友抽取界面)
- 抽取动画展示
- 结果展示界面
- 再次抽取功能
- 返回主界面

#### 3. XianyouDetailScene (仙友详情界面)
- 仙友详细信息
- 好感度进度条
- 升星操作入口
- 洞府放置功能

### UI组件开发

#### 1. XianyouCard (仙友卡片组件)
- 仙友头像展示
- 星级显示
- 好感度显示
- 稀有度边框效果

#### 2. XianyouDrawAnimation (抽取动画组件)
- 卡片翻转动画
- 光效粒子系统
- 音效配合
- 触摸交互

### 数据库集成

#### 1. 云函数开发
- `drawXianyou` - 仙友抽取云函数
- `updateXianyouData` - 仙友数据更新
- `getXianyouTemplates` - 获取仙友模板

#### 2. 数据表设计
- `xianyou_templates` - 仙友模板表
- `player_xianyou` - 玩家仙友表

## 测试策略
测试仙友抽取概率准确性，验证仙友数据保存和加载，确保界面显示正常。

### 测试要点
1. 仙友抽取概率准确性测试
2. 仙友数据保存和加载测试
3. 界面显示和交互测试
4. 动画效果流畅性测试
5. 云函数调用稳定性测试

## 相关文件
- `js/managers/XianyouManager.js` (新建)
- `js/scenes/XianyouScene.js` (新建)
- `js/scenes/XianyouDrawScene.js` (新建)
- `js/scenes/XianyouDetailScene.js` (新建)
- `js/ui/XianyouCard.js` (新建)
- `game_data/xianyou_data.js` (新建)
- `cloudfunctions/drawXianyou/` (新建)

## 完成标准
- [ ] XianyouManager管理器开发完成
- [ ] 仙友抽取系统正常运行
- [ ] 仙友管理界面显示正常
- [ ] 仙友数据保存和加载正常
- [ ] 抽取动画效果流畅
- [ ] 通过所有仙友系统测试 