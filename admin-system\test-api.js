#!/usr/bin/env node

/**
 * API测试脚本
 * 验证后台管理系统的各个API接口
 */

const http = require('http');

const BASE_URL = 'http://localhost:3000';

// 测试API接口
async function testAPI(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            data: result
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// 运行测试
async function runTests() {
  console.log('🧪 开始API测试...\n');

  const tests = [
    {
      name: '统计数据API',
      path: '/api/stats',
      expected: 'success'
    },
    {
      name: '功法模板列表API',
      path: '/api/skill-templates',
      expected: 'success'
    },
    {
      name: '剑心模板列表API',
      path: '/api/sword-heart-templates',
      expected: 'success'
    },
    {
      name: '古宝模板列表API',
      path: '/api/treasure-templates',
      expected: 'success'
    },
    {
      name: '抽取池列表API',
      path: '/api/gacha-pools',
      expected: 'success'
    },
    {
      name: '玩家列表API',
      path: '/api/players',
      expected: 'success'
    }
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    try {
      console.log(`🔍 测试: ${test.name}`);
      const result = await testAPI(test.path);
      
      if (result.status === 200 && result.data.success) {
        console.log(`✅ 通过 - 状态码: ${result.status}`);
        if (test.path === '/api/stats') {
          console.log(`   📊 统计数据: 玩家${result.data.data.players}个, 功法模板${result.data.data.skill_templates}个`);
        } else if (result.data.data && Array.isArray(result.data.data)) {
          console.log(`   📋 返回数据: ${result.data.data.length}条记录`);
        }
        passedTests++;
      } else {
        console.log(`❌ 失败 - 状态码: ${result.status}`);
        console.log(`   错误信息: ${JSON.stringify(result.data, null, 2)}`);
      }
    } catch (error) {
      console.log(`❌ 失败 - 连接错误: ${error.message}`);
    }
    console.log('');
  }

  console.log('📋 测试结果汇总:');
  console.log(`✅ 通过: ${passedTests}/${totalTests}`);
  console.log(`❌ 失败: ${totalTests - passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 所有测试通过！后台管理系统运行正常。');
    console.log(`🌐 访问地址: ${BASE_URL}`);
  } else {
    console.log('\n⚠️  部分测试失败，请检查服务器状态。');
  }
}

// 等待服务器启动后开始测试
setTimeout(() => {
  runTests().catch(console.error);
}, 3000); 