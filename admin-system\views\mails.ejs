<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮件系统管理 - 修仙六道后台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6a11cb;
            --secondary-color: #2575fc;
        }
        body { background-color: #f4f7f6; }
        .sidebar {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            min-height: 100vh;
        }
        .sidebar .nav-link { color: rgba(255,255,255,0.8); }
        .sidebar .nav-link:hover, .sidebar .nav-link.active { color: #fff; }
        .main-content { padding: 2rem; }
        .card {
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            border-radius: 12px;
        }
        .form-control:focus, .form-select:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.25rem rgba(37, 117, 252, 0.25);
        }
        .attachment-item {
            display: flex;
            gap: 10px;
            align-items: center;
            background: #e9ecef;
            padding: 8px;
            border-radius: 6px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <h5 class="text-white text-center mb-4">修仙后台</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item"><a class="nav-link" href="/"><i class="bi bi-speedometer2 me-2"></i>数据总览</a></li>
                        <li class="nav-item"><a class="nav-link" href="/dashboard"><i class="bi bi-graph-up me-2"></i>数据统计</a></li>
                        <li class="nav-item"><a class="nav-link" href="/players"><i class="bi bi-people me-2"></i>玩家管理</a></li>
                        <li class="nav-item"><a class="nav-link" href="/player-resources"><i class="bi bi-gem me-2"></i>玩家资源</a></li>
                        <li class="nav-item"><a class="nav-link" href="/player-equipment"><i class="bi bi-shield-check me-2"></i>装备技能</a></li>
                        <li class="nav-item"><a class="nav-link active" href="/mails"><i class="bi bi-envelope-fill me-2"></i>邮件管理</a></li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 main-content">
                <h1 class="h2 mb-4">邮件系统管理</h1>

                <div class="row">
                    <!-- 邮件发送器 -->
                    <div class="col-lg-5">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-send me-2"></i>发送新邮件</h5>
                            </div>
                            <div class="card-body">
                                <form id="sendMailForm">
                                    <div class="mb-3">
                                        <label for="mailRecipients" class="form-label">收件人</label>
                                        <select class="form-select" id="mailRecipients">
                                            <option value="all">所有玩家</option>
                                            <option value="selected">指定玩家</option>
                                        </select>
                                        <textarea class="form-control mt-2" id="recipientIds" placeholder="输入玩家OpenID，用逗号分隔" style="display: none;"></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="mailTemplate" class="form-label">使用模板</label>
                                        <select class="form-select" id="mailTemplate">
                                            <option value="">不使用模板</option>
                                            <!-- 模板将动态加载 -->
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="mailTitle" class="form-label">邮件标题</label>
                                        <input type="text" class="form-control" id="mailTitle" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="mailContent" class="form-label">邮件内容</label>
                                        <textarea class="form-control" id="mailContent" rows="5" required></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">附件管理</label>
                                        <div id="attachmentsContainer"></div>
                                        <button type="button" class="btn btn-outline-primary btn-sm mt-2" onclick="addAttachmentField()">
                                            <i class="bi bi-plus-circle"></i> 添加附件
                                        </button>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="bi bi-send-fill me-1"></i> 发送邮件
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- 邮件模板列表 -->
                    <div class="col-lg-7">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="bi bi-journal-album me-2"></i>邮件模板</h5>
                                <button class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#templateModal" onclick="prepareNewTemplate()">
                                    <i class="bi bi-plus-lg"></i> 新建模板
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>标题</th>
                                                <th>创建时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="templatesTableBody">
                                            <!-- 模板将动态加载 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 邮件发送历史管理 -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="bi bi-clock-history me-2"></i>邮件发送历史</h5>
                                <button class="btn btn-sm btn-outline-primary" onclick="loadMailHistory()">
                                    <i class="bi bi-arrow-clockwise"></i> 刷新
                                </button>
                            </div>
                            <div class="card-body">
                                <!-- 搜索筛选 -->
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <input type="text" class="form-control" id="historySearch" placeholder="搜索邮件标题">
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select" id="historyStatus">
                                            <option value="">所有状态</option>
                                            <option value="active">正常</option>
                                            <option value="deleted">已删除</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <input type="date" class="form-control" id="historyDate">
                                    </div>
                                    <div class="col-md-2">
                                        <button class="btn btn-primary w-100" onclick="searchMailHistory()">
                                            <i class="bi bi-search"></i> 搜索
                                        </button>
                                    </div>
                                </div>

                                <!-- 邮件历史列表 -->
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>邮件标题</th>
                                                <th>发送时间</th>
                                                <th>接收人数</th>
                                                <th>阅读率</th>
                                                <th>领取率</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="mailHistoryTableBody">
                                            <!-- 历史记录将动态加载 -->
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 分页 -->
                                <nav>
                                    <ul class="pagination justify-content-center" id="mailHistoryPagination">
                                        <!-- 分页将动态生成 -->
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 模板编辑/创建模态框 -->
    <div class="modal fade" id="templateModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="templateModalLabel">邮件模板</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="templateForm">
                        <input type="hidden" id="templateId">
                        <div class="mb-3">
                            <label for="templateTitle" class="form-label">模板标题</label>
                            <input type="text" class="form-control" id="templateTitle" required>
                        </div>
                        <div class="mb-3">
                            <label for="templateContent" class="form-label">模板内容</label>
                            <textarea class="form-control" id="templateContent" rows="5" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">模板附件</label>
                            <div id="modalAttachmentsContainer"></div>
                            <button type="button" class="btn btn-outline-primary btn-sm mt-2" onclick="addAttachmentField('modal')">
                                <i class="bi bi-plus-circle"></i> 添加附件
                            </button>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="saveTemplate()">保存模板</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 邮件详情查看模态框 -->
    <div class="modal fade" id="mailDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">邮件详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">基本信息</h6>
                            <table class="table table-sm">
                                <tr><td><strong>标题:</strong></td><td id="detailTitle">-</td></tr>
                                <tr><td><strong>发送时间:</strong></td><td id="detailSentAt">-</td></tr>
                                <tr><td><strong>状态:</strong></td><td id="detailStatus">-</td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">统计信息</h6>
                            <table class="table table-sm">
                                <tr><td><strong>总接收人数:</strong></td><td id="detailTotalRecipients">-</td></tr>
                                <tr><td><strong>已读人数:</strong></td><td id="detailReadCount">-</td></tr>
                                <tr><td><strong>已领取人数:</strong></td><td id="detailClaimedCount">-</td></tr>
                            </table>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6 class="text-muted">邮件内容</h6>
                            <div class="border p-3 bg-light rounded" id="detailContent">-</div>
                        </div>
                    </div>
                    <div class="row mt-3" id="detailAttachmentsContainer" style="display: none;">
                        <div class="col-12">
                            <h6 class="text-muted">附件奖励</h6>
                            <div id="detailAttachments"></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-primary" onclick="viewMailRecipients()">
                        <i class="bi bi-people"></i> 查看接收者
                    </button>
                    <button type="button" class="btn btn-warning" onclick="resendMail()" id="resendMailBtn">
                        <i class="bi bi-arrow-repeat"></i> 重发未读
                    </button>
                    <button type="button" class="btn btn-danger" onclick="deleteMail()" id="deleteMailBtn">
                        <i class="bi bi-trash"></i> 删除邮件
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 邮件接收者列表模态框 -->
    <div class="modal fade" id="recipientsModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">邮件接收者状态</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- 筛选器 -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <select class="form-select" id="recipientStatusFilter">
                                <option value="">所有状态</option>
                                <option value="unread">未读</option>
                                <option value="read">已读</option>
                                <option value="claimed">已领取</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <input type="text" class="form-control" id="recipientSearchInput" placeholder="搜索玩家OpenID">
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-primary" onclick="filterRecipients()">
                                <i class="bi bi-funnel"></i> 筛选
                            </button>
                        </div>
                    </div>

                    <!-- 接收者列表 -->
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead>
                                <tr>
                                    <th>玩家OpenID</th>
                                    <th>阅读状态</th>
                                    <th>领取状态</th>
                                    <th>阅读时间</th>
                                    <th>领取时间</th>
                                </tr>
                            </thead>
                            <tbody id="recipientsTableBody">
                                <!-- 接收者数据将动态加载 -->
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <nav>
                        <ul class="pagination justify-content-center" id="recipientsPagination">
                            <!-- 分页将动态生成 -->
                        </ul>
                    </nav>
                </div>
                <div class="modal-footer">
                    <div class="me-auto">
                        <small class="text-muted" id="recipientsStats">统计信息</small>
                    </div>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // JavaScript 逻辑
    document.addEventListener('DOMContentLoaded', () => {
        loadMailTemplates();
        loadMailHistory(); // 页面加载时自动加载邮件历史

        document.getElementById('mailRecipients').addEventListener('change', (e) => {
            document.getElementById('recipientIds').style.display = e.target.value === 'selected' ? 'block' : 'none';
        });

        document.getElementById('mailTemplate').addEventListener('change', fillFormWithTemplate);
        document.getElementById('sendMailForm').addEventListener('submit', sendMail);
    });

    // 全局变量存储当前查看的邮件信息
    let currentMail = null;
    let currentMailHistory = [];
    let currentRecipients = [];

    // 加载邮件模板
    async function loadMailTemplates() {
        try {
            const response = await fetch('/api/mail-templates');
            const result = await response.json();
            if (result.success) {
                const templatesTableBody = document.getElementById('templatesTableBody');
                const mailTemplateSelect = document.getElementById('mailTemplate');
                
                templatesTableBody.innerHTML = '';
                mailTemplateSelect.innerHTML = '<option value="">不使用模板</option>';

                result.data.forEach(template => {
                    // 填充表格
                    const row = `
                        <tr>
                            <td>${template.title}</td>
                            <td>${new Date(template.created_at).toLocaleString()}</td>
                            <td>
                                <button class="btn btn-sm btn-primary" onclick="editTemplate('${template._id}')"><i class="bi bi-pencil"></i></button>
                                <button class="btn btn-sm btn-danger" onclick="deleteTemplate('${template._id}')"><i class="bi bi-trash"></i></button>
                            </td>
                        </tr>
                    `;
                    templatesTableBody.insertAdjacentHTML('beforeend', row);

                    // 填充下拉选择框
                    const option = new Option(template.title, template._id);
                    option.dataset.template = JSON.stringify(template);
                    mailTemplateSelect.add(option);
                });
            }
        } catch (error) {
            console.error("加载邮件模板失败:", error);
        }
    }

    // ========== 邮件历史管理功能 ==========

    // 加载邮件发送历史
    async function loadMailHistory(page = 1, search = '', status = '', date = '') {
        try {
            const params = new URLSearchParams({ 
                page: page, 
                limit: 10,
                search: search,
                status: status,
                date: date 
            });
            
            const response = await fetch(`/api/mails/history?${params}`);
            const result = await response.json();
            
            if (result.success) {
                currentMailHistory = result.data;
                renderMailHistoryTable(result.data);
                renderMailHistoryPagination(result.pagination);
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error("加载邮件历史失败:", error);
            document.getElementById('mailHistoryTableBody').innerHTML = 
                '<tr><td colspan="7" class="text-center text-muted">加载失败</td></tr>';
        }
    }

    // 渲染邮件历史表格
    function renderMailHistoryTable(mails) {
        const tbody = document.getElementById('mailHistoryTableBody');
        if (mails.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">暂无邮件记录</td></tr>';
            return;
        }

        tbody.innerHTML = mails.map(mail => {
            const sentAt = new Date(mail.created_at).toLocaleString();
            const readRate = mail.total_recipients > 0 ? ((mail.read_count / mail.total_recipients) * 100).toFixed(1) : '0.0';
            const claimRate = mail.total_recipients > 0 ? ((mail.claimed_count / mail.total_recipients) * 100).toFixed(1) : '0.0';
            const statusBadge = mail.deleted ? 
                '<span class="badge bg-secondary">已删除</span>' : 
                '<span class="badge bg-success">正常</span>';

            return `
                <tr style="${mail.deleted ? 'opacity: 0.6;' : ''}">
                    <td><strong>${mail.title}</strong></td>
                    <td>${sentAt}</td>
                    <td><span class="badge bg-info">${mail.total_recipients || 0}</span></td>
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="progress me-2" style="width: 60px; height: 16px;">
                                <div class="progress-bar bg-primary" style="width: ${readRate}%"></div>
                            </div>
                            <small>${readRate}%</small>
                        </div>
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="progress me-2" style="width: 60px; height: 16px;">
                                <div class="progress-bar bg-success" style="width: ${claimRate}%"></div>
                            </div>
                            <small>${claimRate}%</small>
                        </div>
                    </td>
                    <td>${statusBadge}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="viewMailDetail('${mail._id}')" title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        ${!mail.deleted ? `
                            <button class="btn btn-sm btn-outline-warning" onclick="resendMailFromHistory('${mail._id}')" title="重发未读">
                                <i class="bi bi-arrow-repeat"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteMailFromHistory('${mail._id}')" title="删除邮件">
                                <i class="bi bi-trash"></i>
                            </button>
                        ` : ''}
                    </td>
                </tr>
            `;
        }).join('');
    }

    // 渲染邮件历史分页
    function renderMailHistoryPagination(pagination) {
        const paginationContainer = document.getElementById('mailHistoryPagination');
        if (!pagination || pagination.totalPages <= 1) {
            paginationContainer.innerHTML = '';
            return;
        }

        let paginationHTML = '';
        
        // 上一页
        if (pagination.currentPage > 1) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="loadMailHistory(${pagination.currentPage - 1})">上一页</a>
                </li>
            `;
        }

        // 页码
        for (let i = Math.max(1, pagination.currentPage - 2); i <= Math.min(pagination.totalPages, pagination.currentPage + 2); i++) {
            paginationHTML += `
                <li class="page-item ${i === pagination.currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="loadMailHistory(${i})">${i}</a>
                </li>
            `;
        }

        // 下一页
        if (pagination.currentPage < pagination.totalPages) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="loadMailHistory(${pagination.currentPage + 1})">下一页</a>
                </li>
            `;
        }

        paginationContainer.innerHTML = paginationHTML;
    }

    // 搜索邮件历史
    function searchMailHistory() {
        const search = document.getElementById('historySearch').value.trim();
        const status = document.getElementById('historyStatus').value;
        const date = document.getElementById('historyDate').value;
        loadMailHistory(1, search, status, date);
    }

    // 查看邮件详情
    async function viewMailDetail(mailId) {
        try {
            // 从当前历史列表中找到邮件信息
            const mail = currentMailHistory.find(m => m._id === mailId);
            if (!mail) {
                alert('邮件信息不存在');
                return;
            }

            currentMail = mail;

            // 填充邮件详情模态框
            document.getElementById('detailTitle').textContent = mail.title;
            document.getElementById('detailSentAt').textContent = new Date(mail.created_at).toLocaleString();
            document.getElementById('detailStatus').innerHTML = mail.deleted ? 
                '<span class="badge bg-secondary">已删除</span>' : 
                '<span class="badge bg-success">正常</span>';
            document.getElementById('detailTotalRecipients').textContent = mail.total_recipients || 0;
            document.getElementById('detailReadCount').textContent = mail.read_count || 0;
            document.getElementById('detailClaimedCount').textContent = mail.claimed_count || 0;
            document.getElementById('detailContent').textContent = mail.content;

            // 显示附件
            if (mail.rewards && mail.rewards.length > 0) {
                document.getElementById('detailAttachmentsContainer').style.display = 'block';
                document.getElementById('detailAttachments').innerHTML = mail.rewards.map(reward => `
                    <span class="badge bg-warning me-2">${reward.type}: ${reward.id} x${reward.count}</span>
                `).join('');
            } else {
                document.getElementById('detailAttachmentsContainer').style.display = 'none';
            }

            // 根据邮件状态控制按钮
            document.getElementById('resendMailBtn').style.display = mail.deleted ? 'none' : 'inline-block';
            document.getElementById('deleteMailBtn').style.display = mail.deleted ? 'none' : 'inline-block';

            // 显示模态框
            new bootstrap.Modal(document.getElementById('mailDetailModal')).show();
        } catch (error) {
            console.error("查看邮件详情失败:", error);
            alert('查看邮件详情失败');
        }
    }

    // 查看邮件接收者列表
    async function viewMailRecipients(page = 1, status = '', search = '') {
        if (!currentMail) {
            alert('请先选择一个邮件');
            return;
        }

        try {
            const params = new URLSearchParams({ 
                page: page, 
                limit: 20,
                status: status,
                search: search 
            });
            
            const response = await fetch(`/api/mails/${currentMail._id}/recipients?${params}`);
            const result = await response.json();
            
            if (result.success) {
                currentRecipients = result.data;
                renderRecipientsTable(result.data);
                renderRecipientsPagination(result.pagination);
                updateRecipientsStats(result.stats);
                
                // 隐藏邮件详情模态框，显示接收者模态框
                bootstrap.Modal.getInstance(document.getElementById('mailDetailModal')).hide();
                new bootstrap.Modal(document.getElementById('recipientsModal')).show();
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error("加载接收者列表失败:", error);
            alert('加载接收者列表失败');
        }
    }

    // 渲染接收者表格
    function renderRecipientsTable(recipients) {
        const tbody = document.getElementById('recipientsTableBody');
        if (recipients.length === 0) {
            tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">暂无接收者记录</td></tr>';
            return;
        }

        tbody.innerHTML = recipients.map(recipient => {
            const readStatus = recipient.is_read ? 
                '<span class="badge bg-success">已读</span>' : 
                '<span class="badge bg-secondary">未读</span>';
            const claimStatus = recipient.is_claimed ? 
                '<span class="badge bg-success">已领取</span>' : 
                '<span class="badge bg-secondary">未领取</span>';
            const readTime = recipient.read_at ? new Date(recipient.read_at).toLocaleString() : '-';
            const claimTime = recipient.claimed_at ? new Date(recipient.claimed_at).toLocaleString() : '-';

            return `
                <tr>
                    <td><code>${recipient._openid}</code></td>
                    <td>${readStatus}</td>
                    <td>${claimStatus}</td>
                    <td><small>${readTime}</small></td>
                    <td><small>${claimTime}</small></td>
                </tr>
            `;
        }).join('');
    }

    // 渲染接收者分页
    function renderRecipientsPagination(pagination) {
        const paginationContainer = document.getElementById('recipientsPagination');
        if (!pagination || pagination.totalPages <= 1) {
            paginationContainer.innerHTML = '';
            return;
        }

        let paginationHTML = '';
        
        if (pagination.currentPage > 1) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="viewMailRecipients(${pagination.currentPage - 1})">上一页</a>
                </li>
            `;
        }

        for (let i = Math.max(1, pagination.currentPage - 2); i <= Math.min(pagination.totalPages, pagination.currentPage + 2); i++) {
            paginationHTML += `
                <li class="page-item ${i === pagination.currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="viewMailRecipients(${i})">${i}</a>
                </li>
            `;
        }

        if (pagination.currentPage < pagination.totalPages) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="viewMailRecipients(${pagination.currentPage + 1})">下一页</a>
                </li>
            `;
        }

        paginationContainer.innerHTML = paginationHTML;
    }

    // 更新接收者统计信息
    function updateRecipientsStats(stats) {
        const statsText = `总计: ${stats.total} | 已读: ${stats.read} | 已领取: ${stats.claimed}`;
        document.getElementById('recipientsStats').textContent = statsText;
    }

    // 筛选接收者
    function filterRecipients() {
        const status = document.getElementById('recipientStatusFilter').value;
        const search = document.getElementById('recipientSearchInput').value.trim();
        viewMailRecipients(1, status, search);
    }

    // 重发邮件
    async function resendMail() {
        if (!currentMail) {
            alert('请先选择一个邮件');
            return;
        }

        if (!confirm(`确定要重发邮件"${currentMail.title}"给所有未读玩家吗？`)) {
            return;
        }

        try {
            const response = await fetch(`/api/mails/${currentMail._id}/resend`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            const result = await response.json();
            
            if (result.success) {
                alert(result.message);
                // 关闭模态框并刷新历史列表
                bootstrap.Modal.getInstance(document.getElementById('mailDetailModal')).hide();
                loadMailHistory();
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error("重发邮件失败:", error);
            alert(`重发邮件失败: ${error.message}`);
        }
    }

    // 从历史列表重发邮件
    async function resendMailFromHistory(mailId) {
        const mail = currentMailHistory.find(m => m._id === mailId);
        if (!mail) {
            alert('邮件信息不存在');
            return;
        }

        if (!confirm(`确定要重发邮件"${mail.title}"给所有未读玩家吗？`)) {
            return;
        }

        try {
            const response = await fetch(`/api/mails/${mailId}/resend`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            const result = await response.json();
            
            if (result.success) {
                alert(result.message);
                loadMailHistory(); // 刷新历史列表
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error("重发邮件失败:", error);
            alert(`重发邮件失败: ${error.message}`);
        }
    }

    // 删除邮件
    async function deleteMail() {
        if (!currentMail) {
            alert('请先选择一个邮件');
            return;
        }

        if (!confirm(`确定要删除邮件"${currentMail.title}"吗？此操作不可恢复！`)) {
            return;
        }

        try {
            const response = await fetch(`/api/mails/${currentMail._id}`, {
                method: 'DELETE'
            });
            const result = await response.json();
            
            if (result.success) {
                alert('邮件删除成功！');
                // 关闭模态框并刷新历史列表
                bootstrap.Modal.getInstance(document.getElementById('mailDetailModal')).hide();
                loadMailHistory();
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error("删除邮件失败:", error);
            alert(`删除邮件失败: ${error.message}`);
        }
    }

    // 从历史列表删除邮件
    async function deleteMailFromHistory(mailId) {
        const mail = currentMailHistory.find(m => m._id === mailId);
        if (!mail) {
            alert('邮件信息不存在');
            return;
        }

        if (!confirm(`确定要删除邮件"${mail.title}"吗？此操作不可恢复！`)) {
            return;
        }

        try {
            const response = await fetch(`/api/mails/${mailId}`, {
                method: 'DELETE'
            });
            const result = await response.json();
            
            if (result.success) {
                alert('邮件删除成功！');
                loadMailHistory(); // 刷新历史列表
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error("删除邮件失败:", error);
            alert(`删除邮件失败: ${error.message}`);
        }
    }

    // ========== 原有功能保持不变 ==========

    // 使用模板填充表单
    function fillFormWithTemplate(e) {
        const selectedOption = e.target.options[e.target.selectedIndex];
        if (!selectedOption.value) {
            document.getElementById('mailTitle').value = '';
            document.getElementById('mailContent').value = '';
            document.getElementById('attachmentsContainer').innerHTML = '';
            return;
        }
        const template = JSON.parse(selectedOption.dataset.template);
        document.getElementById('mailTitle').value = template.title;
        document.getElementById('mailContent').value = template.content;

        const container = document.getElementById('attachmentsContainer');
        container.innerHTML = '';
        if (template.attachments) {
            template.attachments.forEach(att => addAttachmentField(null, att));
        }
    }

    // 添加附件字段
    function addAttachmentField(type = 'form', attachment = { type: 'resource', id: 'xianyu', count: 0 }) {
        const containerId = type === 'modal' ? 'modalAttachmentsContainer' : 'attachmentsContainer';
        const container = document.getElementById(containerId);
        const div = document.createElement('div');
        div.className = 'attachment-item mb-2';
        div.innerHTML = `
            <select class="form-select form-select-sm" style="width: 150px;">
                <option value="resource" ${attachment.type === 'resource' ? 'selected' : ''}>资源</option>
                <option value="item" ${attachment.type === 'item' ? 'selected' : ''}>物品</option>
            </select>
            <input type="text" class="form-control form-control-sm" placeholder="ID (如: xianyu, item_id)" value="${attachment.id}">
            <input type="number" class="form-control form-control-sm" placeholder="数量" value="${attachment.count}" min="1">
            <button type="button" class="btn btn-sm btn-danger" onclick="this.parentElement.remove()">
                <i class="bi bi-trash"></i>
            </button>
        `;
        container.appendChild(div);
    }

    // 从DOM读取附件
    function getAttachmentsFromDOM(containerId) {
        const container = document.getElementById(containerId);
        return Array.from(container.children).map(item => ({
            type: item.children[0].value,
            id: item.children[1].value,
            count: parseInt(item.children[2].value) || 0
        })).filter(att => att.id && att.count > 0);
    }

    // 发送邮件
    async function sendMail(e) {
        e.preventDefault();
        const recipientsType = document.getElementById('mailRecipients').value;
        const recipientIds = document.getElementById('recipientIds').value.split(',').map(id => id.trim()).filter(Boolean);
        
        if (recipientsType === 'selected' && recipientIds.length === 0) {
            alert('请至少输入一个玩家OpenID');
            return;
        }

        const mailData = {
            recipientsType,
            recipientIds,
            title: document.getElementById('mailTitle').value,
            content: document.getElementById('mailContent').value,
            attachments: getAttachmentsFromDOM('attachmentsContainer')
        };

        try {
            const response = await fetch('/api/send-mail', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(mailData)
            });
            const result = await response.json();
            if (result.success) {
                alert('邮件发送成功！');
                document.getElementById('sendMailForm').reset();
                document.getElementById('attachmentsContainer').innerHTML = '';
                loadMailHistory(); // 刷新邮件历史
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            alert(`邮件发送失败: ${error.message}`);
        }
    }

    // 准备新建模板
    function prepareNewTemplate() {
        document.getElementById('templateForm').reset();
        document.getElementById('templateId').value = '';
        document.getElementById('modalAttachmentsContainer').innerHTML = '';
        document.getElementById('templateModalLabel').textContent = '新建邮件模板';
    }

    // 保存模板
    async function saveTemplate() {
        const id = document.getElementById('templateId').value;
        const url = id ? `/api/mail-templates/${id}` : '/api/mail-templates';
        const method = id ? 'PUT' : 'POST';

        const templateData = {
            title: document.getElementById('templateTitle').value,
            content: document.getElementById('templateContent').value,
            attachments: getAttachmentsFromDOM('modalAttachmentsContainer')
        };

        try {
            const response = await fetch(url, {
                method: method,
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(templateData)
            });
            const result = await response.json();
            if (result.success) {
                alert('模板保存成功！');
                bootstrap.Modal.getInstance(document.getElementById('templateModal')).hide();
                loadMailTemplates();
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            alert(`模板保存失败: ${error.message}`);
        }
    }

    // 编辑模板
    async function editTemplate(id) {
        try {
            const response = await fetch(`/api/mail-templates`);
            const result = await response.json();
            const template = result.data.find(t => t._id === id);

            if (template) {
                document.getElementById('templateId').value = template._id;
                document.getElementById('templateTitle').value = template.title;
                document.getElementById('templateContent').value = template.content;
                document.getElementById('templateModalLabel').textContent = '编辑邮件模板';
                
                const container = document.getElementById('modalAttachmentsContainer');
                container.innerHTML = '';
                if(template.attachments) {
                    template.attachments.forEach(att => addAttachmentField('modal', att));
                }

                new bootstrap.Modal(document.getElementById('templateModal')).show();
            }
        } catch (error) {
            console.error("加载模板数据失败:", error);
        }
    }

    // 删除模板
    async function deleteTemplate(id) {
        if (!confirm('确定要删除这个模板吗？')) return;

        try {
            const response = await fetch(`/api/mail-templates/${id}`, { method: 'DELETE' });
            const result = await response.json();
            if (result.success) {
                alert('模板删除成功！');
                loadMailTemplates();
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            alert(`模板删除失败: ${error.message}`);
        }
    }
    </script>
</body>
</html>
