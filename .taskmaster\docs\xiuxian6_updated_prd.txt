<context>
# 修仙六道微信小游戏 - 产品需求文档 (更新版)

## 项目概述
修仙六道是一款基于微信小游戏原生框架开发的修仙角色战斗类游戏。游戏以修仙为背景，提供单角色养成、古宝收集、仙友结缘、技能修炼、挂机游历、竞技场PVP等丰富的游戏玩法，支持云端数据存储和离线收益系统。

## 核心功能模块
### 1. 单角色养成系统
- 角色管理：玩家专注培养一个主角色，拥有独特属性和技能
- 等级提升：通过修炼获得经验值，提升角色等级和境界
- 属性系统：包含生命值、攻击力、防御力、速度、暴击率等核心属性
- 境界突破：从炼气期到更高境界的修炼系统

### 2. 古宝系统
- 古宝收集：收集各种稀有古宝，包含武器、法宝、护符三大类
- 等级提升：使用强化材料提升古宝等级，增强基础属性
- 星级升级：消耗资源进行升星，获得额外属性加成
- 抽取系统：支持单抽和十连抽，包含保底机制

### 3. 仙友系统 (新增核心系统)
- 仙友结缘：通过抽卡系统与各种仙友结缘，每个仙友有独特的外观和属性
- 好感度培养：通过赠送礼物提高仙友好感度，好感度提升会增加玩家属性
- 特殊功能：部分仙友具有特殊能力，可放置在洞府的炼丹房或炼器室中
- 功能提升：仙友在炼丹房可提高炼丹成功率，在炼器室可提高炼器成功率
- 升星机制：仙友可以升星，升星提高额外属性加成并提升特殊功能效果
- 仙友管理：仙友背包管理、属性查看、好感度进度追踪

### 4. 技能修炼系统
- 技能学习：角色可学习和装备不同的技能
- 技能升级：使用历练点提升技能等级和效果
- 技能进阶：高级技能的进阶和突破

### 5. 剑心剑骨系统
- 剑心系统：特殊的属性增强系统，使用剑意进行升级
- 剑骨系统：提供持续的属性加成

### 6. 挂机游历系统
- 离线收益：玩家离线时自动进行游历获得经验和资源
- 地点选择：竹林、山洞等不同游历地点
- 怪物击杀：自动战斗击杀普通、精英、Boss怪物

### 7. 竞技场系统
- 排名系统：玩家排名和历史最高排名记录
- 挑战机制：每日有限次数的挑战机会

### 8. 洞府系统 (功能扩展)
- 洞府建设：洞府等级提升和功能扩展
- 灵气收集：定期收集洞府产生的灵气
- 炼丹房：放置仙友提高炼丹成功率，炼制各种丹药
- 炼器室：放置仙友提高炼器成功率，锻造和强化装备
- 仙友居所：为仙友提供居住场所，影响好感度增长

### 9. 邮件系统
- 系统邮件：接收游戏官方发送的各类通知和奖励
- 奖励领取：从邮件中领取物品、装备等奖励

## 用户体验设计
### 目标用户
- 修仙题材爱好者
- 角色养成游戏玩家
- 休闲挂机游戏用户
- 收集养成类游戏爱好者

### 核心用户流程
1. 用户登录 → 创建角色 → 主界面导航
2. 日常修炼 → 挂机收益 → 角色提升
3. 古宝抽取 → 装备强化 → 战力提升
4. 仙友结缘 → 好感度培养 → 属性增强
5. 洞府建设 → 仙友放置 → 功能提升
6. 竞技场挑战 → 排名提升 → 获得奖励
</context>

<PRD>
# 技术架构

## 开发框架
- 平台：微信小游戏原生框架
- 语言：JavaScript (ES6+)
- 架构：模块化 + 组件化设计
- 数据库：微信云开发数据库
- 云函数：Node.js

## 核心系统组件
### 管理器模块 (managers/)
- GameStateManager：游戏状态管理
- SceneManager：场景管理
- LoginManager：登录管理
- DataSyncManager：数据同步管理
- PlayerDataManager：玩家数据管理
- TreasureManager：古宝系统管理
- XianyouManager：仙友系统管理 (新增)
- AutoSaveManager：自动保存管理

### 游戏场景 (scenes/)
- MainScene：主页面
- CharacterDetailScene：角色详情
- TreasureDrawScene：古宝抽取
- TreasureScene：古宝系统
- XianyouDrawScene：仙友结缘 (新增)
- XianyouScene：仙友管理 (新增)
- XianyouDetailScene：仙友详情 (新增)
- DongfuScene：洞府系统 (功能扩展)
- TrialScene：试炼系统
- BackpackScene：背包系统
- ArenaScene：竞技场
- MailScene：邮件系统

### 数据模型 (models/)
- Character：角色模型
- Equipment：装备模型
- Skill：技能模型
- Xianyou：仙友模型 (新增)
- DongfuSystem：洞府系统 (功能扩展)

### 战斗系统 (battle/)
- BattleManager：战斗管理
- SingleBattleSystem：单人战斗
- BattleSystem：战斗逻辑

### UI组件 (ui/)
- Button：基础按钮组件
- EnhancedButton：增强按钮组件
- GridLayoutManager：网格布局管理器
- TitleBar：标题栏组件
- Dialog：对话框组件
- XianyouCard：仙友卡片组件 (新增)

## 云端数据库设计
### 核心数据表
- players：玩家基础信息
- player_resources：玩家资源数据
- characters：角色数据和属性
- player_treasures：玩家古宝数据
- player_xianyou：玩家仙友数据 (新增)
- player_skills：玩家技能数据
- player_items：背包物品数据

### 系统功能表
- player_dongfu：洞府系统数据 (功能扩展)
- player_arena：竞技场数据
- player_idle：挂机游历数据
- player_mails：玩家邮件
- xianyou_templates：仙友模板数据 (新增)

### 云函数服务
- createPlayerData：创建玩家数据
- databaseService：通用数据库操作
- playerService：玩家数据服务
- drawTreasure：古宝抽取
- drawXianyou：仙友结缘 (新增)
- calculateTreasureEffects：古宝效果计算
- calculateXianyouEffects：仙友属性计算 (新增)

# 开发路线图

## 第一阶段：核心基础架构
### 基础框架搭建
- 游戏初始化系统完善
- 场景管理器优化
- 登录系统稳定性提升
- 数据同步机制完善

### 用户界面优化
- 主界面布局优化
- 按钮组件交互改进
- 导航系统完善
- 加载界面优化

## 第二阶段：核心游戏系统
### 单角色系统完善
- 角色详情页面优化
- 属性计算系统完善
- 境界突破系统实现
- 角色数据管理优化

### 古宝系统深化
- 抽取动画效果优化
- 古宝强化系统完善
- 套装效果实现
- 古宝背包管理

### 仙友系统实现 (新增重点)
- 仙友抽取系统开发
- 仙友数据模型设计
- 好感度培养机制
- 仙友属性加成计算
- 仙友升星系统

## 第三阶段：战斗与挑战系统
### 战斗系统优化
- 战斗逻辑完善
- 伤害计算优化
- 战斗动画效果
- 战斗结果展示

### 试炼系统实现
- 试炼关卡设计
- 难度递增机制
- 奖励系统设计
- 进度保存功能

### 竞技场系统
- 排名机制实现
- 挑战逻辑完善
- 奖励分发系统
- 历史记录管理

## 第四阶段：洞府与仙友深度集成
### 洞府系统扩展
- 炼丹房功能实现
- 炼器室功能实现
- 仙友放置机制
- 功能效果计算

### 仙友系统深化
- 仙友特殊功能实现
- 洞府放置逻辑
- 好感度影响系统
- 仙友互动功能

### 技能系统实现
- 技能学习机制
- 技能升级系统
- 技能装备功能
- 技能效果计算

## 第五阶段：社交与持续游戏性
### 邮件系统完善
- 邮件模板管理
- 奖励自动发放
- 邮件分类功能
- 批量操作支持

### 挂机系统优化
- 离线收益计算
- 游历地点扩展
- 收益平衡调整
- 自动战斗优化

### 商业化功能
- VIP系统实现
- 活动系统开发
- 数据分析与优化

# 逻辑依赖链

## 基础依赖顺序
1. **游戏框架基础**：GameStateManager、SceneManager、LoginManager必须首先稳定
2. **数据层基础**：DataSyncManager、PlayerDataManager、云函数服务必须可靠运行
3. **UI基础组件**：Button、Dialog、TitleBar等基础组件必须完善
4. **主界面完善**：MainScene作为核心入口必须功能完整

## 功能模块依赖
1. **角色系统**依赖数据层和UI组件
2. **古宝系统**依赖角色系统和数据层
3. **仙友系统**依赖角色系统和数据层，与古宝系统并行开发
4. **洞府系统扩展**依赖仙友系统，需要仙友放置功能
5. **技能系统**依赖角色系统
6. **战斗系统**依赖角色、古宝、仙友、技能系统
7. **试炼和竞技场**依赖战斗系统
8. **邮件和挂机系统**可独立开发，但需要数据层支持

## 快速可用性路径
1. 确保用户能够登录并看到主界面
2. 角色详情页面能够正常显示和操作
3. 基础的古宝抽取功能可用
4. 基础的仙友结缘功能可用
5. 简单的战斗和试炼功能可体验
6. 洞府与仙友集成功能
7. 逐步添加更多功能模块

# 风险与缓解措施

## 技术挑战
### 数据同步一致性
- 风险：客户端与云端数据不一致，特别是仙友和古宝数据
- 缓解：完善DataSyncManager，增加数据校验机制，实现增量同步

### 性能优化
- 风险：仙友系统增加后，数据量增大可能影响性能
- 缓解：实现分页加载，优化数据结构，使用缓存机制

### 云函数稳定性
- 风险：仙友抽取等新云函数调用失败或超时
- 缓解：错误重试机制，降级方案，监控告警

## MVP定义
### 最小可用产品
1. 用户登录和角色创建
2. 主界面导航和基础UI
3. 角色详情查看和基础属性
4. 简单的古宝抽取功能
5. 基础的仙友结缘功能
6. 基础的数据保存和同步

### 可扩展基础
- 模块化架构确保功能可独立开发
- 标准化数据接口支持快速迭代
- 组件化UI支持界面快速调整
- 仙友系统为后续社交功能预留接口

## 资源约束
### 开发资源
- 风险：仙友系统增加开发复杂度
- 缓解：优先级排序，MVP先行，迭代开发，复用古宝系统的抽取逻辑

### 存储和带宽
- 风险：仙友数据增加存储成本
- 缓解：数据结构优化，图片资源压缩，分批加载

# 附录

## 技术规范
- 代码规范：ES6+语法，模块化开发
- 数据格式：JSON标准格式
- 错误处理：统一错误码和错误信息
- 日志记录：关键操作日志记录

## 仙友系统技术细节
### 数据结构设计
- 仙友模板：包含基础属性、特殊功能、升星效果
- 玩家仙友：包含好感度、当前星级、放置状态
- 好感度机制：礼物类型、好感度增长曲线
- 升星机制：升星材料、属性提升公式

### 功能实现要点
- 抽取概率：稀有度权重分配
- 属性计算：仙友属性对玩家属性的加成算法
- 洞府集成：仙友放置对炼丹炼器成功率的影响
- UI设计：仙友卡片展示、好感度进度条、升星动画

## 测试策略
- 单元测试：核心逻辑函数测试，特别是仙友属性计算
- 集成测试：系统间接口测试，仙友与洞府系统集成
- 用户测试：真实用户场景测试，仙友培养流程
- 性能测试：大量仙友数据的加载和处理性能
</PRD> 