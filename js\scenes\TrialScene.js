/**
 * 试炼场景类
 * 提供各类试炼挑战，获取资源奖励
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import game from '../../game';
import DemonKingConfig from '../config/DemonKingConfig';

class TrialScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager,resources) {
    super(ctx, screenWidth, screenHeight, sceneManager);

    // 场景资源
    this.resources = resources || {};

    // 当前选中的底部导航项
    this.selectedTabIndex = 3; // 试炼对应的导航索引

    // 试炼关卡数据
    this.trials = [
      { id: 1, name: '凡人试炼', level: 1, reward: { lingshi: 100, exp: 50, forgeStone: 100 }, completed: false },
      { id: 2, name: '修士试炼', level: 5, reward: { lingshi: 300, exp: 150 }, completed: false },
      { id: 3, name: '真人试炼', level: 10, reward: { lingshi: 500, exp: 250 }, completed: false },
      { id: 4, name: '金丹试炼', level: 20, reward: { lingshi: 1000, exp: 500 }, completed: false },
      { id: 5, name: '元婴试炼', level: 30, reward: { lingshi: 2000, exp: 1000 }, completed: false }
    ];

    // 剑心试炼关卡数据
    this.swordHeartTrials = [
      { id: 101, name: '初级剑心试炼', level: 1, reward: { swordIntent: 100 }, completed: false },
      { id: 102, name: '中级剑心试炼', level: 10, reward: { swordIntent: 300 }, completed: false },
      { id: 103, name: '高级剑心试炼', level: 20, reward: { swordIntent: 600 }, completed: false },
      { id: 104, name: '剑心真解试炼', level: 30, reward: { swordIntent: 1000 }, completed: false },
      { id: 105, name: '剑心奥义试炼', level: 40, reward: { swordIntent: 2000, swordHeartId: 'hantie' }, completed: false }
    ];

    // 当前试炼类型（青禾秘境或剑心试炼）
    this.trialType = 'qinghe'; // 'qinghe' 或 'swordHeart'

    // 试炼模式：'main' 主界面，'detail' 详情界面
    this.trialMode = 'main';

    // 当前选中的妖王境界
    this.selectedDemonRealm = '练气期';

    // 移除在构造函数中对initUI的调用
    // 初始化UI将在onShow方法中调用
  }

  // 初始化UI
  initUI() {
    // 底部导航栏按钮
    const tabBarHeight = 60;
    const tabBarY = this.screenHeight - tabBarHeight;
    const tabButtonWidth = this.screenWidth / 5;

    

    // 根据当前模式创建不同的UI
    if (this.trialMode === 'main') {
      this.createMainTrialButtons();
    } else if (this.trialMode === 'detail') {
      this.createDetailTrialButtons();
    }
  }

  // 创建主界面试炼按钮
  createMainTrialButtons() {
    const headerHeight = 120; // 修正为与drawHeader一致的高度
    const buttonWidth = 200;
    const buttonHeight = 80;
    const margin = 30;
    const startY = headerHeight + 50;

    // 青禾秘境按钮
    this.qingheButton = new Button(
      this.ctx,
      (this.screenWidth - buttonWidth) / 2,
      startY,
      buttonWidth,
      buttonHeight,
      '青禾秘境',
      null,
      '#4299e1',
      () => {
        this.trialType = 'qinghe';
        this.trialMode = 'detail';
        this.clearUIElements();
        this.initUI();
      }
    );

    this.addUIElement(this.qingheButton);

    // 剑心试炼按钮
    this.swordHeartButton = new Button(
      this.ctx,
      (this.screenWidth - buttonWidth) / 2,
      startY + buttonHeight + margin,
      buttonWidth,
      buttonHeight,
      '剑心试炼',
      null,
      '#805AD5',
      () => {
        this.trialType = 'swordHeart';
        this.trialMode = 'detail';
        this.clearUIElements();
        this.initUI();
      }
    );

    this.addUIElement(this.swordHeartButton);

    // 妖王挑战按钮
    this.demonKingButton = new Button(
      this.ctx,
      (this.screenWidth - buttonWidth) / 2,
      startY + (buttonHeight + margin) * 2,
      buttonWidth,
      buttonHeight,
      '妖王挑战',
      null,
      '#E53E3E',
      () => {
        this.trialType = 'demonKing';
        this.trialMode = 'detail';
        this.clearUIElements();
        this.initUI();
      }
    );

    this.addUIElement(this.demonKingButton);
  }

  // 创建详情界面按钮
  createDetailTrialButtons() {
    const headerHeight = 120; // 修正为与drawHeader一致的高度
    const buttonWidth = 80;
    const buttonHeight = 40;
    const margin = 10;
    const tabBarHeight = 60;

    // 返回按钮 - 放在左下角，底部导航栏上方
    this.backButton = new Button(
      this.ctx,
      margin,
      this.screenHeight - tabBarHeight - buttonHeight - margin,
      buttonWidth,
      buttonHeight,
      '返回',
      null,
      '#666666',
      () => {
        this.trialMode = 'main';
        this.clearUIElements();
        this.initUI();
      }
    );

    this.addUIElement(this.backButton);

    if (this.trialType === 'demonKing') {
      this.createDemonKingButtons();
    } else {
      this.createTrialButtons();
    }
  }

  // 创建妖王挑战按钮
  createDemonKingButtons() {
    const headerHeight = 120; // 修正为与drawHeader一致的高度
    const buttonWidth = 120;
    const buttonHeight = 50;
    const margin = 20;
    const startY = headerHeight + 60;

    // 境界选择按钮
    DemonKingConfig.realmOrder.forEach((realm, index) => {
      const demonKing = DemonKingConfig.getDemonKingByRealm(realm);
      if (!demonKing) return;

      const button = new Button(
        this.ctx,
        (this.screenWidth - buttonWidth) / 2,
        startY + index * (buttonHeight + margin),
        buttonWidth,
        buttonHeight,
        `${realm}妖王`,
        null,
        this.selectedDemonRealm === realm ? '#E53E3E' : null,
        () => {
          this.selectedDemonRealm = realm;
          this.startDemonKingBattle(realm);
        }
      );

      this.addUIElement(button);
    });
  }

  // 创建试炼关卡按钮
  createTrialButtons() {
    const headerHeight = 80;
    const buttonWidth = 200;
    const buttonHeight = 50;
    const buttonMargin = 10;

    // 清除之前的按钮
    if (this.trialButtons && this.trialButtons.length > 0) {
      this.trialButtons.forEach(button => {
        this.removeUIElement(button);
      });
    }

    this.trialButtons = [];

    // 根据当前试炼类型选择关卡数据
    const trialsData = this.trialType === 'qinghe' ? this.trials : this.swordHeartTrials;

    // 创建关卡按钮
    trialsData.forEach((trial, index) => {
      const button = new Button(
        this.ctx,
        (this.screenWidth - buttonWidth) / 2,
        headerHeight + 70 + index * (buttonHeight + buttonMargin),
        buttonWidth,
        buttonHeight,
        trial.name,
        null,
        null,
        () => {
          if (this.trialType === 'qinghe') {
            this.startTrial(trial.id);
          } else {
            this.startSwordHeartTrial(trial.id);
          }
        }
      );

      this.trialButtons.push(button);
      this.addUIElement(button);
    });
  }

  // 底部导航栏选中回调
  onTabSelected(index) {
    // 如果点击的是当前选中的项，不做处理
    if (this.selectedTabIndex === index) {
      return;
    }

    // 更新选中的索引
    this.selectedTabIndex = index;

    // 根据索引切换场景
    switch (index) {
      case 0:
        // 主页
        this.sceneManager.showScene('main');
        break;
      case 1:
        // 角色页面 - 直接进入女剑仙角色详情页
        this.sceneManager.showScene('characterDetail', { characterId: 1 });
        break;
      case 2:
        // 洞府页面
        this.sceneManager.showScene('dongfu');
        break;
      case 3:
        // 试炼页面，已经在试炼页面，不需要切换
        break;
      case 4:
        // 背包页面
        this.sceneManager.showScene('backpack');
        break;
    }
  }

  // 开始青禾秘境试炼
  startTrial(trialId) {
    // 获取试炼关卡
    const trial = this.trials.find(t => t.id === trialId);
    if (!trial) return;

    // 获取角色列表
    const characters = game.gameStateManager.getCharacters();
    if (characters.length === 0) {
      wx.showToast({
        title: '没有可用角色，无法进行试炼！',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 获取战力最高的角色
    const strongestCharacter = characters.reduce((prev, current) => {
      return (prev.power > current.power) ? prev : current;
    });

    // 检查角色等级是否达到要求
    if (strongestCharacter.level < trial.level) {
      wx.showToast({
        title: `角色等级不足，需要等级${trial.level}以上的角色才能挑战！`,
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 计算关卡战力 (基于关卡等级)
    const stagePower = trial.level * 100; // 每级100点战力

    // 获取玩家总战力
    const playerPower = strongestCharacter.power || 0;

    console.log(`试炼对比 - 角色战力: ${playerPower}, 关卡战力: ${stagePower}`);

    // 创建试炼关卡数据
    const stageData = {
      id: `trial_${trial.id}`,
      name: trial.name,
      enemyName: `${trial.name}守护者`,
      enemyLevel: trial.level,
      enemyHp: trial.level * 200,
      enemyAttack: trial.level * 15,
      enemyDefense: trial.level * 8,
      enemyAttackSpeed: Math.min(trial.level * 2, 50), // 最高50%攻击速度
      rewards: {
        lingshi: trial.reward ? trial.reward.lingshi : 100,
        xianyu: 10,
        exp: trial.reward ? trial.reward.exp : 50,
        lianlidian: trial.level * 5
      }
    };

    // 创建地点配置（适配IdleBattleScene格式）
    const baseEnemy = {
      name: `${trial.name}守护者`,
      level: trial.level,
      hpBase: trial.level * 200,
      attackBase: trial.level * 15,
      defenseBase: trial.level * 8,
      attackSpeed: Math.min(trial.level * 2, 50)
    };

    const locationConfig = {
      id: `trial_${trial.id}`,
      name: trial.name,
      level: trial.level,
      description: `${trial.name}试炼`,

      // IdleBattleScene期望的敌人配置格式
      normalEnemy: baseEnemy,
      eliteEnemy: {
        name: `精英${baseEnemy.name}`,
        hpMultiplier: 10,
        attackMultiplier: 2,
        defenseMultiplier: 1.5,
        attackSpeed: baseEnemy.attackSpeed * 1.2
      },
      bossEnemy: {
        name: `Boss${baseEnemy.name}`,
        hpMultiplier: 100,
        attackMultiplier: 5,
        defenseMultiplier: 3,
        attackSpeed: baseEnemy.attackSpeed * 1.5
      },

      rewards: stageData.rewards
    };

    // 使用IdleBattleScene战斗系统
    this.sceneManager.showScene('idleBattle', {
      playerCharacter: strongestCharacter,
      locationConfig: locationConfig,
      storyMode: true, // 标记为试炼模式，使用30秒限制
      onComplete: (result) => {
        console.log('试炼战斗结束，结果:', result);

        if (result.victory && result.playerAlive) {
          // 处理炼器石奖励（如果有）
          if (trial.reward && trial.reward.forgeStone > 0) {
            const player = game.gameStateManager.getPlayer();
            let forgeStoneItem = player.items ? player.items.find(item => item.name === '炼器石') : null;

            if (forgeStoneItem) {
              forgeStoneItem.count += trial.reward.forgeStone;
            } else {
              if (!player.items) {
                player.items = [];
              }
              player.items.push({
                id: 'forgeStone_' + Date.now(),
                name: '炼器石',
                type: 'material',
                quality: 0,
                count: trial.reward.forgeStone,
                stackable: true,
                description: '用于锻造装备的材料'
              });
            }
            game.gameStateManager.setPlayer(player);
          }

          // 标记试炼为已完成
          trial.completed = true;
        }

        // 战斗结束后返回试炼界面
        this.sceneManager.showScene('trial');
      }
    });
  }

  // 开始妖王挑战
  startDemonKingBattle(realm) {
    const demonKing = DemonKingConfig.getDemonKingByRealm(realm);
    if (!demonKing) {
      console.error('妖王数据不存在:', realm);
      return;
    }

    // 获取角色列表
    const characters = game.gameStateManager.getCharacters();
    if (characters.length === 0) {
      console.log('没有可用角色，无法进行妖王挑战！');
      return;
    }

    // 获取战力最高的角色
    const strongestCharacter = characters.reduce((prev, current) => {
      return (prev.power > current.power) ? prev : current;
    });

    // 检查角色等级是否达到要求
    if (strongestCharacter.level < demonKing.level) {
      console.log(`角色等级不足，需要等级${demonKing.level}以上的角色才能挑战${realm}妖王！`);
      return;
    }

    console.log(`开始挑战${realm}妖王:`, demonKing.name);

    // 创建妖王战斗数据
    const stageData = {
      id: `demon_king_${demonKing.id}`,
      name: `${demonKing.name}挑战`,
      enemyName: demonKing.name,
      enemyLevel: demonKing.level,
      enemyHp: demonKing.attributes.hp,
      enemyAttack: demonKing.attributes.attack,
      enemyDefense: demonKing.attributes.defense,
      enemyAttackSpeed: demonKing.attributes.speed,
      rewards: {
        lingshi: demonKing.rewards.lingshi,
        xianyu: 50,
        exp: demonKing.rewards.exp,
        functionPoints: demonKing.rewards.functionPoints
      }
    };

    // 创建地点配置（适配IdleBattleScene格式）
    const baseEnemy = {
      name: demonKing.name,
      level: demonKing.level,
      hpBase: demonKing.attributes.hp,
      attackBase: demonKing.attributes.attack,
      defenseBase: demonKing.attributes.defense,
      attackSpeed: demonKing.attributes.speed
    };

    const locationConfig = {
      id: `demon_king_${demonKing.id}`,
      name: `${demonKing.name}挑战`,
      level: demonKing.level,
      description: `${demonKing.name}妖王挑战`,

      // IdleBattleScene期望的敌人配置格式
      normalEnemy: baseEnemy,
      eliteEnemy: {
        name: `精英${baseEnemy.name}`,
        hpMultiplier: 10,
        attackMultiplier: 2,
        defenseMultiplier: 1.5,
        attackSpeed: baseEnemy.attackSpeed * 1.2
      },
      bossEnemy: {
        name: `Boss${baseEnemy.name}`,
        hpMultiplier: 100,
        attackMultiplier: 5,
        defenseMultiplier: 3,
        attackSpeed: baseEnemy.attackSpeed * 1.5
      },

      rewards: stageData.rewards
    };

    // 使用IdleBattleScene战斗系统
    this.sceneManager.showScene('idleBattle', {
      playerCharacter: strongestCharacter,
      locationConfig: locationConfig,
      storyMode: true, // 标记为试炼模式，使用30秒限制
      onComplete: (result) => {
        console.log('妖王挑战战斗结束，结果:', result);

        if (result.victory && result.playerAlive) {
          // 处理妖王掉落物品
          this.processDemonKingRewards(demonKing.rewards.items);
        }

        // 战斗结束后返回试炼界面
        this.sceneManager.showScene('trial');
      }
    });
  }

  // 处理妖王奖励物品
  processDemonKingRewards(items) {
    const player = game.gameStateManager.getPlayer();
    if (!player.items) player.items = [];

    items.forEach(item => {
      // 根据概率决定是否掉落
      if (Math.random() < item.probability) {
        // 查找是否已有该物品
        let existingItem = player.items.find(playerItem => playerItem.name === item.name);

        if (existingItem && existingItem.stackable) {
          existingItem.count += item.count;
        } else {
          player.items.push({
            id: item.id + '_' + Date.now(),
            name: item.name,
            type: 'material',
            quality: 2, // 妖王掉落物品为稀有品质
            count: item.count,
            stackable: true,
            description: `从${this.selectedDemonRealm}妖王处获得的珍贵材料`
          });
        }

        console.log(`获得物品: ${item.name} x${item.count}`);
      }
    });

    game.gameStateManager.setPlayer(player);
  }

  // 开始剑心试炼
  startSwordHeartTrial(trialId) {
    // 获取试炼关卡
    const trial = this.swordHeartTrials.find(t => t.id === trialId);
    if (!trial) return;

    // 获取角色列表
    const characters = game.gameStateManager.getCharacters();
    if (characters.length === 0) {
      wx.showToast({
        title: '没有可用角色，无法进行试炼！',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 获取女剑仙角色（主角）
    const mainCharacter = characters.find(char => char.id === 1);
    if (!mainCharacter) {
      wx.showToast({
        title: '女剑仙角色不存在，无法进行剑心试炼！',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 检查角色等级是否达到要求
    if (mainCharacter.level < trial.level) {
      wx.showToast({
        title: `角色等级不足，需要等级${trial.level}以上的角色才能挑战！`,
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 计算关卡战力 (基于关卡等级)
    const stagePower = trial.level * 120; // 剑心试炼难度略高

    // 获取玩家总战力
    const playerPower = mainCharacter.power || 0;

    console.log(`剑心试炼对比 - 角色战力: ${playerPower}, 关卡战力: ${stagePower}`);

    // 创建剑心试炼关卡数据
    const stageData = {
      id: `sword_heart_trial_${trial.id}`,
      name: trial.name,
      enemyName: `剑心幻影`,
      enemyLevel: trial.level,
      enemyHp: trial.level * 250, // 剑心试炼敌人血量更高
      enemyAttack: trial.level * 18,
      enemyDefense: trial.level * 10,
      enemyAttackSpeed: Math.min(trial.level * 3, 60), // 剑心试炼敌人更快
      rewards: {
        swordIntent: trial.reward ? trial.reward.swordIntent : 100,
        lianlidian: trial.level * 8
      }
    };

    // 创建地点配置（适配IdleBattleScene格式）
    const baseEnemy = {
      name: '剑心幻影',
      level: trial.level,
      hpBase: trial.level * 250,
      attackBase: trial.level * 18,
      defenseBase: trial.level * 10,
      attackSpeed: Math.min(trial.level * 3, 60)
    };

    const locationConfig = {
      id: `sword_heart_trial_${trial.id}`,
      name: trial.name,
      level: trial.level,
      description: `${trial.name}剑心试炼`,

      // IdleBattleScene期望的敌人配置格式
      normalEnemy: baseEnemy,
      eliteEnemy: {
        name: `精英${baseEnemy.name}`,
        hpMultiplier: 10,
        attackMultiplier: 2,
        defenseMultiplier: 1.5,
        attackSpeed: baseEnemy.attackSpeed * 1.2
      },
      bossEnemy: {
        name: `Boss${baseEnemy.name}`,
        hpMultiplier: 100,
        attackMultiplier: 5,
        defenseMultiplier: 3,
        attackSpeed: baseEnemy.attackSpeed * 1.5
      },

      rewards: stageData.rewards
    };

    // 使用IdleBattleScene战斗系统
    this.sceneManager.showScene('idleBattle', {
      playerCharacter: mainCharacter,
      locationConfig: locationConfig,
      storyMode: true, // 标记为试炼模式，使用30秒限制
      onComplete: (result) => {
        console.log('剑心试炼战斗结束，结果:', result);

        if (result.victory && result.playerAlive) {
          // 处理剑意奖励
          if (stageData.rewards.swordIntent > 0) {
            game.gameStateManager.addSwordIntent(stageData.rewards.swordIntent);
          }

          // 处理剑心解锁奖励
          if (trial.reward.swordHeartId) {
            game.gameStateManager.unlockSwordHeart(trial.reward.swordHeartId);
          }

          // 标记试炼为已完成
          trial.completed = true;
        }

        // 战斗结束后返回试炼界面
        this.sceneManager.showScene('trial');
      }
    });
  }

  // 场景显示时的回调
  onShow(params) {
    // 重置为主界面模式
    this.trialMode = 'main';

    // 清空UI元素
    this.clearUIElements();

    // 初始化UI（现在在onShow中调用，而不是构造函数中）
    this.initUI();

    // 更新选中的导航项
    this.selectedTabIndex = 3;
  }

  // 处理触摸结束事件
  handleTouchEnd(x, y) {
    // 检查底部导航栏点击
    const tabBarHeight = 80;
    const tabBarY = this.screenHeight - tabBarHeight;
    
    if (y >= tabBarY) {
      const tabWidth = this.screenWidth / 5;
      const tabIndex = Math.floor(x / tabWidth);
      
      if (tabIndex >= 0 && tabIndex < 5) {
        this.onTabSelected(tabIndex);
        return true; // 表示事件已处理
      }
    }

    // 调用父类的触摸处理
    return super.handleTouchEnd(x, y);
  }

  // 子类实现的绘制逻辑
  drawScene() {
    // 绘制背景
    this.drawBackground();

    // 绘制顶部导航栏
    this.drawHeader();

    // 绘制试炼信息
    this.drawTrialInfo();

    // 绘制底部导航栏
    this.drawTabBar();
  }

  // 绘制背景
  drawBackground() {
    // 如果有背景图资源，使用背景图
    if (this.resources && this.resources.mainBg) {
      try {
        // 确保背景图覆盖整个屏幕，包括底部导航栏区域
        const bgWidth = this.screenWidth;
        const bgHeight = this.screenHeight; // 使用屏幕高度确保完全覆盖

        this.ctx.drawImage(
          this.resources.mainBg,
          0,
          0,
          bgWidth,
          bgHeight
        );
      } catch (error) {
        console.error('绘制背景图失败', error);
        this.drawDefaultBackground();
      }
    } else {
      // 如果没有背景图资源，使用渐变色背景
      this.drawDefaultBackground();
    }
  }

  // 绘制默认背景
  drawDefaultBackground() {
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, '#1a2a6c');
    gradient.addColorStop(0.5, '#b21f1f');
    gradient.addColorStop(1, '#fdbb2d');

    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }

  // 绘制顶部导航栏
  drawHeader() {
    const headerHeight = 120; // 修正为与drawTrialInfo一致的高度
    const safeAreaHeight = 40;

    // 绘制安全区域
    this.ctx.fillStyle = '#000000';
    this.ctx.fillRect(0, 0, this.screenWidth, safeAreaHeight);

    // 绘制顶部导航栏背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, safeAreaHeight, this.screenWidth, headerHeight - safeAreaHeight);

    // 绘制试炼标题
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('试炼场', this.screenWidth / 2, safeAreaHeight + 30);

    // 绘制玩家信息
    try {
      const player = game && game.gameStateManager ? game.gameStateManager.getPlayer() : null;
      if (player && player.nickname) {
        // 绘制玩家昵称和等级
        this.ctx.font = '16px Arial';
        this.ctx.textAlign = 'left';
        this.ctx.fillText(`${player.nickname} Lv.${player.level || 1}`, 20, safeAreaHeight + 60);

        // 绘制资源信息
        this.ctx.textAlign = 'right';
        const resources = player.resources || {};
        this.ctx.fillText(`仙玉: ${resources.xianyu || 0}`, this.screenWidth - 20, safeAreaHeight + 60);
      } else {
        // 如果没有玩家信息，显示默认信息
        this.ctx.font = '16px Arial';
        this.ctx.textAlign = 'left';
        this.ctx.fillText('游客 Lv.1', 20, safeAreaHeight + 60);

        this.ctx.textAlign = 'right';
        this.ctx.fillText('仙玉: 0', this.screenWidth - 20, safeAreaHeight + 60);
      }
    } catch (error) {
      console.error('试炼场景获取玩家信息失败:', error);
      // 显示默认信息
      this.ctx.font = '16px Arial';
      this.ctx.textAlign = 'left';
      this.ctx.fillText('游客 Lv.1', 20, safeAreaHeight + 60);

      this.ctx.textAlign = 'right';
      this.ctx.fillText('仙玉: 0', this.screenWidth - 20, safeAreaHeight + 60);
    }
  }

  // 绘制试炼信息
  drawTrialInfo() {
    const headerHeight = 120; // 包含安全区域的总高度
    const infoY = headerHeight + 20;

    // 根据当前试炼模式绘制不同的信息
    if (this.trialMode === 'main') {
      // 绘制试炼模式选择提示
      this.ctx.font = '20px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('选择试炼类型', this.screenWidth / 2, infoY);

      this.ctx.font = '16px Arial';
      this.ctx.fillStyle = '#cccccc';
      this.ctx.fillText('通过试炼获得经验和奖励', this.screenWidth / 2, infoY + 30);
    } else if (this.trialMode === 'detail') {
      // 绘制具体试炼信息
      this.ctx.font = '18px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('选择试炼关卡', this.screenWidth / 2, infoY);

      // 绘制试炼说明
      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = '#cccccc';
      this.ctx.fillText('挑战不同难度的关卡，获得丰厚奖励', this.screenWidth / 2, infoY + 25);
    } else if (this.trialMode === 'demon') {
      // 绘制妖王试炼信息
      this.ctx.font = '18px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('妖王试炼', this.screenWidth / 2, infoY);

      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = '#cccccc';
      this.ctx.fillText('挑战强大的妖王，获得珍贵材料', this.screenWidth / 2, infoY + 25);

      // 如果选择了妖王境界，显示境界信息
      if (this.selectedDemonRealm) {
        this.ctx.font = '16px Arial';
        this.ctx.fillStyle = '#FFD700';
        this.ctx.fillText(`当前选择: ${this.selectedDemonRealm}妖王`, this.screenWidth / 2, infoY + 50);
      }
    } else if (this.trialMode === 'sword') {
      // 绘制剑心试炼信息
      this.ctx.font = '18px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('剑心试炼', this.screenWidth / 2, infoY);

      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = '#cccccc';
      this.ctx.fillText('通过试炼获得剑意，解锁强大剑心', this.screenWidth / 2, infoY + 25);
    }
  }

  // 绘制底部导航栏
  drawTabBar() {
    const tabBarHeight = 80;
    const tabBarY = this.screenHeight - tabBarHeight;
    const tabWidth = this.screenWidth / 5;

    // 导航栏数据
    const tabData = [
      { icon: '🏠', text: '主页', index: 0 },
      { icon: '👤', text: '角色', index: 1 },
      { icon: '🏔️', text: '洞府', index: 2 },
      { icon: '⚔️', text: '试炼', index: 3 },
      { icon: '🎒', text: '背包', index: 4 }
    ];

    // 绘制每个导航按钮
    for (let i = 0; i < tabData.length; i++) {
      const tab = tabData[i];
      const tabX = i * tabWidth;
      const centerX = tabX + tabWidth / 2;
      const isSelected = this.selectedTabIndex === i;

      // 绘制按钮背景（圆角矩形）
      const buttonRadius = 25;
      const buttonY = tabBarY + 10;
      const buttonHeight = 60;

      // 使用BaseScene的drawRoundRect方法
      const fillStyle = isSelected ? 'rgba(255, 215, 0, 0.3)' : 'rgba(255, 255, 255, 0.1)';
      this.drawRoundRect(tabX + 5, buttonY, tabWidth - 10, buttonHeight, buttonRadius, fillStyle);

      // 绘制图标
      const iconSize = isSelected ? 32 : 28;
      this.ctx.font = `${iconSize}px Arial`;
      this.ctx.textAlign = 'center';
      this.ctx.fillText(tab.icon, centerX, tabBarY + 35);

      // 绘制文字
      this.ctx.font = '12px Arial';
      this.ctx.fillStyle = isSelected ? '#FFD700' : '#FFFFFF';
      this.ctx.fillText(tab.text, centerX, tabBarY + 55);
    }
  }
}

export default TrialScene;