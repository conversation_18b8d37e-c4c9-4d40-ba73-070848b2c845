/**
 * 古宝升星云函数
 * 处理古宝星级提升逻辑
 */

const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

// 古宝升星配置数据 (从静态数据文件复制核心配置)
const treasureStarConfigs = {
  'ancient_sword': [
    { star: 1, cost: { lingshi: 1000, spirit_stone: 5 }, attribute_bonus: { attack: 30 } },
    { star: 2, cost: { lingshi: 3000, spirit_stone: 15 }, attribute_bonus: { crit: 0.03 } },
    { star: 3, cost: { lingshi: 8000, spirit_stone: 30 }, attribute_bonus: { attack: 50, hp: 100 } },
    { star: 4, cost: { lingshi: 20000, spirit_stone: 60 }, attribute_bonus: { crit: 0.05, attack: 80 } },
    { star: 5, cost: { lingshi: 50000, spirit_stone: 120 }, attribute_bonus: { attack: 150, hp: 200, crit: 0.08 } }
  ],
  'dragon_blade': [
    { star: 1, cost: { lingshi: 800, spirit_stone: 3 }, attribute_bonus: { attack: 25 } },
    { star: 2, cost: { lingshi: 2400, spirit_stone: 10 }, attribute_bonus: { speed: 8 } },
    { star: 3, cost: { lingshi: 6000, spirit_stone: 20 }, attribute_bonus: { attack: 40, hp: 80 } },
    { star: 4, cost: { lingshi: 15000, spirit_stone: 45 }, attribute_bonus: { speed: 15, attack: 60 } },
    { star: 5, cost: { lingshi: 35000, spirit_stone: 90 }, attribute_bonus: { attack: 120, hp: 150, speed: 25 } }
  ],
  'dragon_scale': [
    { star: 1, cost: { lingshi: 1500, spirit_stone: 10 }, attribute_bonus: { hp: 80 } },
    { star: 2, cost: { lingshi: 4500, spirit_stone: 25 }, attribute_bonus: { defense: 30 } },
    { star: 3, cost: { lingshi: 11000, spirit_stone: 50 }, attribute_bonus: { crit_defense: 0.05, hp: 200 } },
    { star: 4, cost: { lingshi: 25000, spirit_stone: 100 }, attribute_bonus: { defense: 60, crit_defense: 0.08 } },
    { star: 5, cost: { lingshi: 60000, spirit_stone: 180 }, attribute_bonus: { hp: 400, defense: 120, crit_defense: 0.20 } }
  ]
  // 这里可以添加更多古宝的升星配置...
};

// 获取古宝升星配置
function getStarUpgradeConfig(treasureId, targetStar) {
  const configs = treasureStarConfigs[treasureId];
  if (!configs) {
    // 如果没有配置，使用默认配置
    const defaultCosts = [
      { star: 1, cost: { lingshi: 1000, spirit_stone: 5 } },
      { star: 2, cost: { lingshi: 3000, spirit_stone: 15 } },
      { star: 3, cost: { lingshi: 8000, spirit_stone: 30 } },
      { star: 4, cost: { lingshi: 20000, spirit_stone: 60 } },
      { star: 5, cost: { lingshi: 50000, spirit_stone: 120 } }
    ];
    return defaultCosts.find(c => c.star === targetStar);
  }
  return configs.find(c => c.star === targetStar);
}

exports.main = async (event, context) => {
  const { treasureRecordId } = event;
  const { OPENID } = cloud.getWXContext();

  try {
    console.log('古宝升星请求:', { treasureRecordId, openid: OPENID });

    // 1. 验证参数
    if (!treasureRecordId) {
      return {
        success: false,
        error: 'INVALID_PARAMS',
        message: '古宝记录ID不能为空'
      };
    }

    // 2. 获取玩家古宝当前状态
    const treasureResult = await db.collection('player_treasures')
      .doc(treasureRecordId)
      .get();

    if (!treasureResult.data) {
      return {
        success: false,
        error: 'TREASURE_NOT_FOUND',
        message: '古宝不存在'
      };
    }

    const treasure = treasureResult.data;

    // 验证古宝所属权
    if (treasure.player_id !== OPENID) {
      return {
        success: false,
        error: 'ACCESS_DENIED',
        message: '无权操作此古宝'
      };
    }

    // 检查星级上限
    if (treasure.star >= 5) {
      return {
        success: false,
        error: 'MAX_STAR_REACHED',
        message: '已达到最高星级'
      };
    }

    // 3. 获取下一星级配置
    const nextStar = treasure.star + 1;
    const starConfig = getStarUpgradeConfig(treasure.treasure_id, nextStar);

    if (!starConfig) {
      return {
        success: false,
        error: 'STAR_CONFIG_NOT_FOUND',
        message: '升星配置不存在'
      };
    }

    // 4. 获取玩家当前资源
    const playerResult = await db.collection('players')
      .where({
        _openid: OPENID
      })
      .get();

    if (playerResult.data.length === 0) {
      return {
        success: false,
        error: 'PLAYER_NOT_FOUND',
        message: '玩家数据不存在'
      };
    }

    const player = playerResult.data[0];

    // 5. 验证升星消耗是否充足
    const resourceUpdates = {};
    for (const [resourceType, requiredAmount] of Object.entries(starConfig.cost)) {
      const currentAmount = player[resourceType] || 0;
      if (currentAmount < requiredAmount) {
        return {
          success: false,
          error: 'INSUFFICIENT_RESOURCES',
          message: `${resourceType}不足，需要${requiredAmount}，当前${currentAmount}`
        };
      }
      resourceUpdates[resourceType] = currentAmount - requiredAmount;
    }

    // 6. 使用事务更新数据
    const transaction = await db.startTransaction();

    try {
      // 更新古宝星级
      await transaction.collection('player_treasures')
        .doc(treasureRecordId)
        .update({
          data: {
            star: nextStar,
            updated_at: new Date()
          }
        });

      // 更新玩家资源
      await transaction.collection('players')
        .doc(player._id)
        .update({
          data: {
            ...resourceUpdates,
            updated_at: new Date()
          }
        });

      // 记录升星日志
      await transaction.collection('player_logs').add({
        data: {
          player_id: OPENID,
          action: 'ascend_treasure_star',
          details: {
            treasure_record_id: treasureRecordId,
            treasure_id: treasure.treasure_id,
            old_star: treasure.star,
            new_star: nextStar,
            resources_consumed: starConfig.cost,
            attribute_bonus: starConfig.attribute_bonus || {}
          },
          timestamp: new Date()
        }
      });

      // 提交事务
      await transaction.commit();

      // 检查特殊星级效果
      let specialEffect = null;
      if (nextStar === 3) {
        specialEffect = 'star_3_effect'; // 3星特殊动画
      } else if (nextStar === 5) {
        specialEffect = 'star_5_effect'; // 5星满星特殊外观
      }

      console.log('古宝升星成功:', {
        treasureRecordId,
        treasureId: treasure.treasure_id,
        oldStar: treasure.star,
        newStar: nextStar,
        resourcesConsumed: starConfig.cost
      });

      return {
        success: true,
        data: {
          oldStar: treasure.star,
          newStar: nextStar,
          attributeBonus: starConfig.attribute_bonus || {},
          resourcesConsumed: starConfig.cost,
          specialEffect: specialEffect,
          message: `古宝升星成功！达到${nextStar}星`
        }
      };

    } catch (transactionError) {
      // 回滚事务
      await transaction.rollback();
      throw transactionError;
    }

  } catch (error) {
    console.error('古宝升星失败:', error);
    return {
      success: false,
      error: 'SERVER_ERROR',
      message: '服务器错误，请稍后重试'
    };
  }
}; 