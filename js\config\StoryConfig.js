/**
 * 主线关卡配置
 * 包含敌人数据、奖励配置和关卡信息
 */

// 敌人类型配置
const ENEMY_TYPES = {
  // 第1-2关：森林区域
  FOREST_WOLF: {
    name: '森林野狼',
    description: '森林中的凶猛野狼',
    icon: '🐺',
    baseStats: { hp: 80, attack: 15, defense: 8, speed: 12 }
  },
  FOREST_BEAR: {
    name: '森林巨熊',
    description: '力大无穷的森林巨熊',
    icon: '🐻',
    baseStats: { hp: 120, attack: 20, defense: 15, speed: 8 }
  },
  
  // 第3-4关：山洞区域
  CAVE_BAT: {
    name: '洞穴蝙蝠',
    description: '敏捷的洞穴蝙蝠',
    icon: '🦇',
    baseStats: { hp: 60, attack: 18, defense: 5, speed: 20 }
  },
  CAVE_SPIDER: {
    name: '巨型蜘蛛',
    description: '毒性很强的巨型蜘蛛',
    icon: '🕷️',
    baseStats: { hp: 90, attack: 22, defense: 10, speed: 15 }
  },
  
  // 第5-6关：沙漠区域
  DESERT_SCORPION: {
    name: '沙漠毒蝎',
    description: '剧毒的沙漠毒蝎',
    icon: '🦂',
    baseStats: { hp: 100, attack: 25, defense: 12, speed: 14 }
  },
  DESERT_SNAKE: {
    name: '沙漠巨蟒',
    description: '巨大的沙漠巨蟒',
    icon: '🐍',
    baseStats: { hp: 140, attack: 28, defense: 18, speed: 10 }
  },
  
  // 第7-8关：雪山区域
  ICE_WOLF: {
    name: '冰霜狼王',
    description: '雪山中的狼王',
    icon: '🐺',
    baseStats: { hp: 160, attack: 32, defense: 20, speed: 16 }
  },
  ICE_YETI: {
    name: '雪山雪怪',
    description: '传说中的雪怪',
    icon: '👹',
    baseStats: { hp: 200, attack: 35, defense: 25, speed: 12 }
  },
  
  // 第9-10关：魔域区域
  DEMON_IMP: {
    name: '魔域小鬼',
    description: '来自魔域的小鬼',
    icon: '👿',
    baseStats: { hp: 180, attack: 40, defense: 22, speed: 18 }
  },
  DEMON_LORD: {
    name: '魔域领主',
    description: '强大的魔域领主',
    icon: '😈',
    baseStats: { hp: 250, attack: 45, defense: 30, speed: 15 }
  }
};

// 关卡配置
const STORY_CHAPTERS = [
  // 第1关：翠竹林
  {
    id: 1,
    name: '翠竹林',
    description: '修仙路的起点，这里生活着温和的森林生物',
    environment: '森林',
    bgColor: '#228B22',
    enemies: ['FOREST_WOLF', 'FOREST_WOLF'], // 每层的敌人配置
    bossEnemy: 'FOREST_BEAR', // 第10层Boss
    rewards: {
      base: { lingshi: 200, exp: 100, xianyu: 0 },
      increment: { lingshi: 20, exp: 10, xianyu: 0 },
      bossBonus: { lingshi: 100, exp: 50, xianyu: 1 }
    }
  },
  
  // 第2关：迷雾森林
  {
    id: 2,
    name: '迷雾森林',
    description: '被迷雾笼罩的神秘森林',
    environment: '森林',
    bgColor: '#2F4F2F',
    enemies: ['FOREST_BEAR', 'FOREST_WOLF'],
    bossEnemy: 'FOREST_BEAR',
    rewards: {
      base: { lingshi: 400, exp: 200, xianyu: 0 },
      increment: { lingshi: 40, exp: 20, xianyu: 0 },
      bossBonus: { lingshi: 200, exp: 100, xianyu: 1 }
    }
  },
  
  // 第3关：幽暗洞穴
  {
    id: 3,
    name: '幽暗洞穴',
    description: '阴暗潮湿的洞穴，栖息着危险的生物',
    environment: '洞穴',
    bgColor: '#2F2F2F',
    enemies: ['CAVE_BAT', 'CAVE_BAT'],
    bossEnemy: 'CAVE_SPIDER',
    rewards: {
      base: { lingshi: 600, exp: 300, xianyu: 1 },
      increment: { lingshi: 60, exp: 30, xianyu: 0 },
      bossBonus: { lingshi: 300, exp: 150, xianyu: 2 }
    }
  },
  
  // 第4关：毒虫巢穴
  {
    id: 4,
    name: '毒虫巢穴',
    description: '充满毒虫的危险巢穴',
    environment: '洞穴',
    bgColor: '#4B0082',
    enemies: ['CAVE_SPIDER', 'CAVE_BAT'],
    bossEnemy: 'CAVE_SPIDER',
    rewards: {
      base: { lingshi: 800, exp: 400, xianyu: 1 },
      increment: { lingshi: 80, exp: 40, xianyu: 0 },
      bossBonus: { lingshi: 400, exp: 200, xianyu: 2 }
    }
  },
  
  // 第5关：烈日沙漠
  {
    id: 5,
    name: '烈日沙漠',
    description: '酷热的沙漠，考验修仙者的意志',
    environment: '沙漠',
    bgColor: '#DAA520',
    enemies: ['DESERT_SCORPION', 'DESERT_SCORPION'],
    bossEnemy: 'DESERT_SNAKE',
    rewards: {
      base: { lingshi: 1000, exp: 500, xianyu: 1 },
      increment: { lingshi: 100, exp: 50, xianyu: 0 },
      bossBonus: { lingshi: 500, exp: 250, xianyu: 3 }
    }
  },
  
  // 第6关：毒蛇谷
  {
    id: 6,
    name: '毒蛇谷',
    description: '充满剧毒生物的险恶山谷',
    environment: '沙漠',
    bgColor: '#8B4513',
    enemies: ['DESERT_SNAKE', 'DESERT_SCORPION'],
    bossEnemy: 'DESERT_SNAKE',
    rewards: {
      base: { lingshi: 1200, exp: 600, xianyu: 2 },
      increment: { lingshi: 120, exp: 60, xianyu: 0 },
      bossBonus: { lingshi: 600, exp: 300, xianyu: 3 }
    }
  },
  
  // 第7关：冰封雪山
  {
    id: 7,
    name: '冰封雪山',
    description: '终年积雪的高山，寒风刺骨',
    environment: '雪山',
    bgColor: '#B0E0E6',
    enemies: ['ICE_WOLF', 'ICE_WOLF'],
    bossEnemy: 'ICE_YETI',
    rewards: {
      base: { lingshi: 1400, exp: 700, xianyu: 2 },
      increment: { lingshi: 140, exp: 70, xianyu: 0 },
      bossBonus: { lingshi: 700, exp: 350, xianyu: 4 }
    }
  },
  
  // 第8关：雪怪领域
  {
    id: 8,
    name: '雪怪领域',
    description: '雪怪统治的恐怖领域',
    environment: '雪山',
    bgColor: '#4682B4',
    enemies: ['ICE_YETI', 'ICE_WOLF'],
    bossEnemy: 'ICE_YETI',
    rewards: {
      base: { lingshi: 1600, exp: 800, xianyu: 2 },
      increment: { lingshi: 160, exp: 80, xianyu: 0 },
      bossBonus: { lingshi: 800, exp: 400, xianyu: 4 }
    }
  },
  
  // 第9关：魔域边界
  {
    id: 9,
    name: '魔域边界',
    description: '通往魔域的边界，邪恶气息浓重',
    environment: '魔域',
    bgColor: '#8B0000',
    enemies: ['DEMON_IMP', 'DEMON_IMP'],
    bossEnemy: 'DEMON_LORD',
    rewards: {
      base: { lingshi: 1800, exp: 900, xianyu: 3 },
      increment: { lingshi: 180, exp: 90, xianyu: 0 },
      bossBonus: { lingshi: 900, exp: 450, xianyu: 5 }
    }
  },
  
  // 第10关：魔王殿
  {
    id: 10,
    name: '魔王殿',
    description: '魔王的宫殿，最终的试炼之地',
    environment: '魔域',
    bgColor: '#000000',
    enemies: ['DEMON_LORD', 'DEMON_IMP'],
    bossEnemy: 'DEMON_LORD',
    rewards: {
      base: { lingshi: 2000, exp: 1000, xianyu: 3 },
      increment: { lingshi: 200, exp: 100, xianyu: 0 },
      bossBonus: { lingshi: 1000, exp: 500, xianyu: 10 }
    }
  }
];

/**
 * 生成敌人数据
 * @param {string} enemyType 敌人类型
 * @param {number} level 敌人等级
 * @param {boolean} isBoss 是否为Boss
 * @returns {Object} 敌人数据
 */
function generateEnemy(enemyType, level, isBoss = false) {
  const enemyConfig = ENEMY_TYPES[enemyType];
  if (!enemyConfig) {
    console.error(`未找到敌人类型: ${enemyType}`);
    return null;
  }
  
  // 基础属性
  const baseStats = enemyConfig.baseStats;
  
  // 等级加成系数
  const levelMultiplier = 1 + (level - 1) * 0.15;
  
  // Boss加成系数
  const bossMultiplier = isBoss ? 1.5 : 1;
  
  // 计算最终属性
  const finalStats = {
    hp: Math.floor(baseStats.hp * levelMultiplier * bossMultiplier),
    attack: Math.floor(baseStats.attack * levelMultiplier * bossMultiplier),
    defense: Math.floor(baseStats.defense * levelMultiplier * bossMultiplier),
    speed: Math.floor(baseStats.speed * levelMultiplier * bossMultiplier)
  };
  
  return {
    id: `enemy_${enemyType}_${level}_${Date.now()}`,
    name: isBoss ? `${enemyConfig.name}(首领)` : enemyConfig.name,
    description: enemyConfig.description,
    icon: enemyConfig.icon,
    level: level,
    maxHp: finalStats.hp,
    hp: finalStats.hp,
    attack: finalStats.attack,
    defense: finalStats.defense,
    speed: finalStats.speed,
    critical: 0.05 + (isBoss ? 0.05 : 0),
    criticalDamage: 1.5 + (isBoss ? 0.3 : 0),
    type: 'enemy',
    isBoss: isBoss,
    skills: [], // 暂时为空，后续可以添加技能
    isAlive: function() {
      return this.hp > 0;
    }
  };
}

/**
 * 获取关卡奖励
 * @param {number} chapterId 章节ID
 * @param {number} levelId 关卡ID
 * @returns {Object} 奖励数据
 */
function getStageRewards(chapterId, levelId) {
  const chapter = STORY_CHAPTERS.find(c => c.id === chapterId);
  if (!chapter) {
    return { lingshi: 100, exp: 50, xianyu: 0 };
  }
  
  const rewards = chapter.rewards;
  const isBoss = levelId === 10;
  
  // 基础奖励 + 层数递增奖励
  let finalRewards = {
    lingshi: rewards.base.lingshi + (levelId - 1) * rewards.increment.lingshi,
    exp: rewards.base.exp + (levelId - 1) * rewards.increment.exp,
    xianyu: rewards.base.xianyu + (levelId - 1) * rewards.increment.xianyu
  };
  
  // Boss额外奖励
  if (isBoss) {
    finalRewards.lingshi += rewards.bossBonus.lingshi;
    finalRewards.exp += rewards.bossBonus.exp;
    finalRewards.xianyu += rewards.bossBonus.xianyu;
  }
  
  return finalRewards;
}

/**
 * 获取关卡敌人配置
 * @param {number} chapterId 章节ID
 * @param {number} levelId 关卡ID
 * @returns {Array} 敌人列表
 */
function getStageEnemies(chapterId, levelId) {
  const chapter = STORY_CHAPTERS.find(c => c.id === chapterId);
  if (!chapter) {
    return [];
  }
  
  const enemyLevel = (chapterId - 1) * 10 + levelId;
  const isBoss = levelId === 10;
  
  if (isBoss) {
    // Boss关卡：一个Boss敌人
    return [generateEnemy(chapter.bossEnemy, enemyLevel, true)];
  } else {
    // 普通关卡：根据配置生成敌人
    return chapter.enemies.map(enemyType => 
      generateEnemy(enemyType, enemyLevel, false)
    ).filter(enemy => enemy !== null);
  }
}

export {
  ENEMY_TYPES,
  STORY_CHAPTERS,
  generateEnemy,
  getStageRewards,
  getStageEnemies
}; 