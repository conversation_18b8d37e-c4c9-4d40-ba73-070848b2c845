/**
 * 统一战斗系统
 * 支持攻击速度、技能冷却、吟唱时间等完整战斗机制
 * 可被不同场景重复调用
 */

class CombatSystem {
  constructor() {
    // 战斗状态
    this.isActive = false;
    this.battleTime = 0;
    this.timeLimit = 60000; // 默认60秒
    
    // 战斗单位
    this.player = null;
    this.enemy = null;
    
    // 时间轴系统
    this.playerNextAttack = 0;
    this.enemyNextAttack = 0;
    
    // 技能系统
    this.skillCooldowns = new Map(); // 技能冷却时间
    this.castingSkill = null; // 正在吟唱的技能
    this.castingProgress = 0; // 吟唱进度
    
    // 事件回调
    this.onDamage = null; // 伤害事件回调
    this.onSkillCast = null; // 技能释放回调
    this.onBattleEnd = null; // 战斗结束回调
    this.onUpdate = null; // 更新回调
    
    // 统计数据
    this.stats = {
      playerDamageDealt: 0,
      enemyDamageDealt: 0,
      skillsUsed: 0,
      enemiesKilled: 0
    };
  }

  /**
   * 初始化战斗
   * @param {Object} config 战斗配置
   */
  initBattle(config) {
    this.isActive = true;
    this.battleTime = 0;
    this.timeLimit = config.timeLimit || 60000;
    
    // 初始化战斗单位
    this.player = this.createCombatUnit(config.player, 'player');
    this.enemy = this.createCombatUnit(config.enemy, 'enemy');
    
    // 重置时间轴
    this.playerNextAttack = this.calculateAttackInterval(this.player);
    this.enemyNextAttack = this.calculateAttackInterval(this.enemy);
    
    // 重置技能系统
    this.skillCooldowns.clear();
    this.castingSkill = null;
    this.castingProgress = 0;
    
    // 重置统计
    this.stats = {
      playerDamageDealt: 0,
      enemyDamageDealt: 0,
      skillsUsed: 0,
      enemiesKilled: 0
    };
    
    console.log('战斗系统初始化完成', {
      player: this.player.name,
      enemy: this.enemy.name,
      timeLimit: this.timeLimit
    });
  }

  /**
   * 创建战斗单位
   * @param {Object} unitData 单位数据
   * @param {string} type 单位类型
   * @returns {Object} 战斗单位
   */
  createCombatUnit(unitData, type) {
    return {
      id: unitData.id || `${type}_${Date.now()}`,
      name: unitData.name || '未知',
      type: type,
      
      // 基础属性
      level: unitData.level || 1,
      maxHp: unitData.maxHp || unitData.hp || 1000,
      hp: unitData.hp || 1000,
      attack: unitData.attack || 100,
      defense: unitData.defense || 50,
      attackSpeed: unitData.attackSpeed || 1.0, // 攻击速度倍率
      critical: unitData.critical || 0.05, // 暴击率
      criticalDamage: unitData.criticalDamage || 1.5, // 暴击伤害
      
      // 技能
      skills: unitData.skills || [],
      
      // 状态
      isAlive: true,
      buffs: [], // 增益效果
      debuffs: [], // 减益效果
      
      // 方法
      takeDamage: function(damage) {
        this.hp = Math.max(0, this.hp - damage);
        if (this.hp <= 0) {
          this.isAlive = false;
        }
      },
      
      heal: function(amount) {
        this.hp = Math.min(this.maxHp, this.hp + amount);
      },
      
      getAttributes: function() {
        return {
          hp: this.hp,
          maxHp: this.maxHp,
          attack: this.attack,
          defense: this.defense,
          attackSpeed: this.attackSpeed,
          critical: this.critical,
          criticalDamage: this.criticalDamage
        };
      }
    };
  }

  /**
   * 更新战斗系统
   * @param {number} deltaTime 时间差（毫秒）
   */
  update(deltaTime) {
    if (!this.isActive) return;
    
    this.battleTime += deltaTime;
    
    // 检查时间限制
    if (this.battleTime >= this.timeLimit) {
      this.endBattle('timeout');
      return;
    }
    
    // 更新技能冷却
    this.updateSkillCooldowns(deltaTime);
    
    // 更新技能吟唱
    this.updateSkillCasting(deltaTime);
    
    // 更新自动攻击
    this.updateAutoAttacks(deltaTime);
    
    // 更新Buff/Debuff
    this.updateStatusEffects(deltaTime);
    
    // 检查战斗结束条件
    this.checkBattleEnd();
    
    // 触发更新回调
    if (this.onUpdate) {
      this.onUpdate({
        battleTime: this.battleTime,
        timeRemaining: this.timeLimit - this.battleTime,
        player: this.player,
        enemy: this.enemy,
        castingSkill: this.castingSkill,
        castingProgress: this.castingProgress
      });
    }
  }

  /**
   * 计算攻击间隔
   * @param {Object} unit 战斗单位
   * @returns {number} 攻击间隔（毫秒）
   */
  calculateAttackInterval(unit) {
    // 基础攻击间隔1秒，根据攻击速度调整
    const baseInterval = 1000;
    return baseInterval / unit.attackSpeed;
  }

  /**
   * 更新自动攻击
   * @param {number} deltaTime 时间差
   */
  updateAutoAttacks(deltaTime) {
    // 玩家自动攻击
    if (this.player.isAlive && this.enemy.isAlive) {
      this.playerNextAttack -= deltaTime;
      if (this.playerNextAttack <= 0) {
        this.executeNormalAttack(this.player, this.enemy);
        this.playerNextAttack = this.calculateAttackInterval(this.player);
      }
    }
    
    // 敌人自动攻击
    if (this.enemy.isAlive && this.player.isAlive) {
      this.enemyNextAttack -= deltaTime;
      if (this.enemyNextAttack <= 0) {
        this.executeNormalAttack(this.enemy, this.player);
        this.enemyNextAttack = this.calculateAttackInterval(this.enemy);
      }
    }
  }

  /**
   * 执行普通攻击
   * @param {Object} attacker 攻击者
   * @param {Object} target 目标
   */
  executeNormalAttack(attacker, target) {
    if (!attacker.isAlive || !target.isAlive) return;
    
    const damage = this.calculateDamage(attacker, target);
    const isCritical = Math.random() < attacker.critical;
    const finalDamage = isCritical ? Math.floor(damage * attacker.criticalDamage) : damage;
    
    // 应用伤害
    target.takeDamage(finalDamage);
    
    // 更新统计
    if (attacker.type === 'player') {
      this.stats.playerDamageDealt += finalDamage;
    } else {
      this.stats.enemyDamageDealt += finalDamage;
    }
    
    // 触发伤害回调
    if (this.onDamage) {
      this.onDamage({
        attacker: attacker,
        target: target,
        damage: finalDamage,
        isCritical: isCritical,
        type: 'normal'
      });
    }
    
    console.log(`${attacker.name} 对 ${target.name} 造成了 ${finalDamage} 点${isCritical ? '暴击' : ''}伤害`);
  }

  /**
   * 计算伤害
   * @param {Object} attacker 攻击者
   * @param {Object} target 目标
   * @returns {number} 伤害值
   */
  calculateDamage(attacker, target) {
    const baseAttack = attacker.attack;
    const defense = target.defense;
    
    // 简单的伤害公式：攻击力 - 防御力 * 0.5，最少1点伤害
    const baseDamage = Math.max(1, baseAttack - defense * 0.5);
    
    // 添加随机性 ±20%
    const randomFactor = 0.8 + Math.random() * 0.4;
    
    return Math.floor(baseDamage * randomFactor);
  }

  /**
   * 使用技能
   * @param {Object} skill 技能数据
   * @param {Object} caster 施法者
   * @param {Object} target 目标
   * @returns {boolean} 是否成功开始使用技能
   */
  useSkill(skill, caster, target) {
    if (!this.isActive || !caster.isAlive) return false;
    
    // 检查技能冷却
    const skillId = skill.id;
    const currentTime = this.battleTime;
    const lastUsed = this.skillCooldowns.get(skillId) || 0;
    
    if (currentTime - lastUsed < skill.cooldown) {
      console.log(`技能 ${skill.name} 还在冷却中`);
      return false;
    }
    
    // 检查是否正在吟唱其他技能
    if (this.castingSkill) {
      console.log('正在吟唱其他技能，无法使用新技能');
      return false;
    }
    
    // 开始吟唱
    this.castingSkill = {
      skill: skill,
      caster: caster,
      target: target,
      castTime: skill.castTime || 0,
      totalCastTime: skill.castTime || 0
    };
    this.castingProgress = 0;
    
    console.log(`开始吟唱技能: ${skill.name}，吟唱时间: ${skill.castTime}ms`);
    
    // 如果没有吟唱时间，立即释放
    if (!skill.castTime || skill.castTime <= 0) {
      this.completeSkillCast();
    }
    
    return true;
  }

  /**
   * 更新技能吟唱
   * @param {number} deltaTime 时间差
   */
  updateSkillCasting(deltaTime) {
    if (!this.castingSkill) return;
    
    this.castingSkill.castTime -= deltaTime;
    this.castingProgress = 1 - (this.castingSkill.castTime / this.castingSkill.totalCastTime);
    
    if (this.castingSkill.castTime <= 0) {
      this.completeSkillCast();
    }
  }

  /**
   * 完成技能释放
   */
  completeSkillCast() {
    if (!this.castingSkill) return;
    
    const { skill, caster, target } = this.castingSkill;
    
    // 执行技能效果
    this.executeSkillEffect(skill, caster, target);
    
    // 设置技能冷却
    this.skillCooldowns.set(skill.id, this.battleTime);
    
    // 更新统计
    this.stats.skillsUsed++;
    
    // 触发技能释放回调
    if (this.onSkillCast) {
      this.onSkillCast({
        skill: skill,
        caster: caster,
        target: target
      });
    }
    
    console.log(`${caster.name} 释放了技能: ${skill.name}`);
    
    // 清除吟唱状态
    this.castingSkill = null;
    this.castingProgress = 0;
  }

  /**
   * 执行技能效果
   * @param {Object} skill 技能
   * @param {Object} caster 施法者
   * @param {Object} target 目标
   */
  executeSkillEffect(skill, caster, target) {
    switch (skill.type) {
      case 'damage':
        this.executeSkillDamage(skill, caster, target);
        break;
      case 'heal':
        this.executeSkillHeal(skill, caster, target);
        break;
      case 'buff':
        this.executeSkillBuff(skill, caster, target);
        break;
      default:
        console.warn(`未知的技能类型: ${skill.type}`);
        break;
    }
  }

  /**
   * 执行技能伤害
   * @param {Object} skill 技能
   * @param {Object} caster 施法者
   * @param {Object} target 目标
   */
  executeSkillDamage(skill, caster, target) {
    if (!target.isAlive) return;
    
    const baseDamage = skill.damage || caster.attack;
    const multiplier = skill.damageMultiplier || 1.0;
    const damage = Math.floor(baseDamage * multiplier);
    
    // 检查暴击
    const isCritical = Math.random() < caster.critical;
    const finalDamage = isCritical ? Math.floor(damage * caster.criticalDamage) : damage;
    
    // 应用伤害
    target.takeDamage(finalDamage);
    
    // 更新统计
    if (caster.type === 'player') {
      this.stats.playerDamageDealt += finalDamage;
    } else {
      this.stats.enemyDamageDealt += finalDamage;
    }
    
    // 触发伤害回调
    if (this.onDamage) {
      this.onDamage({
        attacker: caster,
        target: target,
        damage: finalDamage,
        isCritical: isCritical,
        type: 'skill',
        skill: skill
      });
    }
  }

  /**
   * 执行技能治疗
   * @param {Object} skill 技能
   * @param {Object} caster 施法者
   * @param {Object} target 目标
   */
  executeSkillHeal(skill, caster, target) {
    if (!target.isAlive) return;
    
    const healAmount = skill.healAmount || Math.floor(caster.attack * 0.5);
    target.heal(healAmount);
    
    console.log(`${caster.name} 治疗了 ${target.name} ${healAmount} 点生命值`);
  }

  /**
   * 执行技能Buff
   * @param {Object} skill 技能
   * @param {Object} caster 施法者
   * @param {Object} target 目标
   */
  executeSkillBuff(skill, caster, target) {
    // 简化的Buff系统，后续可扩展
    console.log(`${caster.name} 对 ${target.name} 使用了增益技能: ${skill.name}`);
  }

  /**
   * 更新技能冷却
   * @param {number} deltaTime 时间差
   */
  updateSkillCooldowns(deltaTime) {
    // 技能冷却由时间戳管理，无需额外更新
  }

  /**
   * 更新状态效果
   * @param {number} deltaTime 时间差
   */
  updateStatusEffects(deltaTime) {
    // 简化的状态效果系统，后续可扩展
  }

  /**
   * 检查战斗结束条件
   */
  checkBattleEnd() {
    if (!this.player.isAlive) {
      this.endBattle('defeat');
    } else if (!this.enemy.isAlive) {
      this.stats.enemiesKilled++;
      this.endBattle('victory');
    }
  }

  /**
   * 结束战斗
   * @param {string} result 战斗结果
   */
  endBattle(result) {
    this.isActive = false;
    
    console.log('战斗结束，结果:', result);
    console.log('战斗统计:', this.stats);
    
    if (this.onBattleEnd) {
      this.onBattleEnd({
        result: result,
        stats: this.stats,
        battleTime: this.battleTime,
        player: this.player,
        enemy: this.enemy
      });
    }
  }

  /**
   * 获取技能冷却剩余时间
   * @param {string} skillId 技能ID
   * @param {number} cooldown 技能冷却时间
   * @returns {number} 剩余冷却时间
   */
  getSkillCooldownRemaining(skillId, cooldown) {
    const lastUsed = this.skillCooldowns.get(skillId) || 0;
    const elapsed = this.battleTime - lastUsed;
    return Math.max(0, cooldown - elapsed);
  }

  /**
   * 检查技能是否可用
   * @param {Object} skill 技能
   * @returns {boolean} 是否可用
   */
  isSkillAvailable(skill) {
    if (!this.isActive) return false;
    if (this.castingSkill) return false;
    
    const cooldownRemaining = this.getSkillCooldownRemaining(skill.id, skill.cooldown);
    return cooldownRemaining <= 0;
  }

  /**
   * 取消技能吟唱
   */
  cancelSkillCasting() {
    if (this.castingSkill) {
      console.log(`取消技能吟唱: ${this.castingSkill.skill.name}`);
      this.castingSkill = null;
      this.castingProgress = 0;
    }
  }

  /**
   * 获取战斗状态
   * @returns {Object} 战斗状态
   */
  getBattleState() {
    return {
      isActive: this.isActive,
      battleTime: this.battleTime,
      timeRemaining: this.timeLimit - this.battleTime,
      player: this.player,
      enemy: this.enemy,
      castingSkill: this.castingSkill,
      castingProgress: this.castingProgress,
      stats: this.stats
    };
  }

  /**
   * 清理战斗系统
   */
  cleanup() {
    this.isActive = false;
    this.player = null;
    this.enemy = null;
    this.castingSkill = null;
    this.skillCooldowns.clear();
    this.onDamage = null;
    this.onSkillCast = null;
    this.onBattleEnd = null;
    this.onUpdate = null;
  }
}

// 导出单例
const combatSystem = new CombatSystem();
export default combatSystem; 