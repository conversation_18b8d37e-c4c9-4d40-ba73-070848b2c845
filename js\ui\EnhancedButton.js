/**
 * 增强版按钮组件
 * 支持图标、渐变背景、阴影、动画效果等
 */
class EnhancedButton {
  constructor(config) {
    this.x = config.x;
    this.y = config.y;
    this.width = config.width;
    this.height = config.height;
    this.text = config.text;
    this.onClick = config.onClick;
    
    // 视觉配置
    this.icon = config.icon || null;
    this.backgroundColor = config.backgroundColor || '#4A90E2';
    this.textColor = config.textColor || '#FFFFFF';
    this.borderRadius = config.borderRadius || 8;
    this.shadow = config.shadow !== false; // 默认有阴影
    
    // 状态管理
    this.state = 'normal'; // normal, hover, pressed, disabled
    this.visible = config.visible !== false;
    this.enabled = config.enabled !== false;
    
    // 动画相关
    this.animationScale = 1.0;
    this.targetScale = 1.0;
    this.animationSpeed = 0.15;
    
    // 渐变效果
    this.gradient = config.gradient || null;
    
    // 按钮类型样式预设
    this.applyButtonType(config.type || 'default');
  }
  
  /**
   * 应用按钮类型样式
   */
  applyButtonType(type) {
    const styles = {
      primary: {
        backgroundColor: '#007AFF',
        gradient: ['#007AFF', '#0051D5'],
        textColor: '#FFFFFF'
      },
      success: {
        backgroundColor: '#34C759',
        gradient: ['#34C759', '#30A14E'],
        textColor: '#FFFFFF'
      },
      warning: {
        backgroundColor: '#FF9500',
        gradient: ['#FF9500', '#E6820E'],
        textColor: '#FFFFFF'
      },
      danger: {
        backgroundColor: '#FF3B30',
        gradient: ['#FF3B30', '#D70015'],
        textColor: '#FFFFFF'
      },
      secondary: {
        backgroundColor: '#8E8E93',
        gradient: ['#8E8E93', '#636366'],
        textColor: '#FFFFFF'
      },
      default: {
        backgroundColor: '#F2F2F7',
        gradient: ['#F2F2F7', '#E5E5EA'],
        textColor: '#000000'
      }
    };
    
    const style = styles[type] || styles.default;
    this.backgroundColor = style.backgroundColor;
    this.gradient = style.gradient;
    this.textColor = style.textColor;
  }
  
  /**
   * 更新按钮动画
   */
  update(deltaTime) {
    // 缩放动画
    if (Math.abs(this.animationScale - this.targetScale) > 0.01) {
      this.animationScale += (this.targetScale - this.animationScale) * this.animationSpeed;
    } else {
      this.animationScale = this.targetScale;
    }
  }
  
  /**
   * 检测点击
   */
  hitTest(x, y) {
    if (!this.visible || !this.enabled) return false;
    
    return x >= this.x && x <= this.x + this.width &&
           y >= this.y && y <= this.y + this.height;
  }
  
  /**
   * BaseScene要求的方法：检测点是否在按钮内
   */
  isPointInside(x, y) {
    return this.hitTest(x, y);
  }
  
  /**
   * 处理触摸开始
   */
  onTouchStart(x, y) {
    if (!this.hitTest(x, y)) return false;
    
    this.state = 'pressed';
    this.targetScale = 0.95;
    return true;
  }
  
  /**
   * 处理触摸结束
   */
  onTouchEnd(x, y) {
    if (this.state !== 'pressed') return false;
    
    this.state = 'normal';
    this.targetScale = 1.0;
    
    if (this.hitTest(x, y) && this.onClick) {
      // 添加点击反馈动画
      this.playClickAnimation();
      this.onClick();
    }
    
    return true;
  }
  
  /**
   * 播放点击动画
   */
  playClickAnimation() {
    this.targetScale = 1.1;
    setTimeout(() => {
      this.targetScale = 1.0;
    }, 150);
  }
  
  /**
   * 渲染按钮
   */
  render(ctx) {
    if (!this.visible) return;
    
    ctx.save();
    
    // 应用缩放变换
    const centerX = this.x + this.width / 2;
    const centerY = this.y + this.height / 2;
    
    ctx.translate(centerX, centerY);
    ctx.scale(this.animationScale, this.animationScale);
    ctx.translate(-centerX, -centerY);
    
    // 绘制阴影
    if (this.shadow && this.state !== 'pressed') {
      this.drawShadow(ctx);
    }
    
    // 绘制按钮背景
    this.drawBackground(ctx);
    
    // 绘制边框（如果需要）
    if (this.state === 'pressed') {
      this.drawBorder(ctx);
    }
    
    // 绘制图标
    if (this.icon) {
      this.drawIcon(ctx);
    }
    
    // 绘制文本
    this.drawText(ctx);
    
    ctx.restore();
  }
  
  /**
   * 绘制阴影
   */
  drawShadow(ctx) {
    const shadowOffset = 4;
    const shadowBlur = 8;
    
    ctx.save();
    ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
    ctx.shadowBlur = shadowBlur;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = shadowOffset;
    
    // 小尺寸按钮使用圆形阴影 - 必须是正方形或接近正方形的小按钮
    const isSquareSmallButton = this.width <= 80 && this.height >= 70 && this.height <= 100;
    if (isSquareSmallButton) {
      const centerX = this.x + this.width / 2;
      const centerY = this.y + Math.min(this.width, this.height - 25) / 2 + 10;
      const radius = Math.min(this.width, this.height - 25) * 0.35;
      
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
      ctx.fillStyle = this.backgroundColor;
      ctx.fill();
    } else {
      this.drawRoundedRect(ctx, this.x, this.y, this.width, this.height, this.borderRadius);
      ctx.fillStyle = this.backgroundColor;
      ctx.fill();
    }
    
    ctx.restore();
  }
  
  /**
   * 绘制背景
   */
  drawBackground(ctx) {
    // 小尺寸按钮使用圆形样式 - 必须是正方形或接近正方形的小按钮
    const isSquareSmallButton = this.width <= 80 && this.height >= 70 && this.height <= 100;
    if (isSquareSmallButton) {
      this.drawCircularBackground(ctx);
    } else {
      this.drawRectangularBackground(ctx);
    }
  }
  
  /**
   * 绘制圆形背景（小按钮）
   */
  drawCircularBackground(ctx) {
    const centerX = this.x + this.width / 2;
    const centerY = this.y + Math.min(this.width, this.height - 25) / 2 + 10;
    const radius = Math.min(this.width, this.height - 25) * 0.35;
    
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
    
    if (this.gradient) {
      // 径向渐变背景
      const gradient = ctx.createRadialGradient(
        centerX, centerY - radius * 0.3, 0,
        centerX, centerY, radius
      );
      gradient.addColorStop(0, this.gradient[0]);
      gradient.addColorStop(1, this.gradient[1]);
      ctx.fillStyle = gradient;
    } else {
      ctx.fillStyle = this.backgroundColor;
    }
    
    // 根据状态调整透明度
    if (this.state === 'pressed') {
      ctx.globalAlpha = 0.8;
    } else if (!this.enabled) {
      ctx.globalAlpha = 0.5;
    }
    
    ctx.fill();
    ctx.globalAlpha = 1.0;
  }
  
  /**
   * 绘制矩形背景（正常按钮）
   */
  drawRectangularBackground(ctx) {
    this.drawRoundedRect(ctx, this.x, this.y, this.width, this.height, this.borderRadius);
    
    if (this.gradient) {
      // 渐变背景
      const gradient = ctx.createLinearGradient(
        this.x, this.y,
        this.x, this.y + this.height
      );
      gradient.addColorStop(0, this.gradient[0]);
      gradient.addColorStop(1, this.gradient[1]);
      ctx.fillStyle = gradient;
    } else {
      // 纯色背景
      ctx.fillStyle = this.backgroundColor;
    }
    
    // 根据状态调整透明度
    if (this.state === 'pressed') {
      ctx.globalAlpha = 0.8;
    } else if (!this.enabled) {
      ctx.globalAlpha = 0.5;
    }
    
    ctx.fill();
    ctx.globalAlpha = 1.0;
  }
  
  /**
   * 绘制边框
   */
  drawBorder(ctx) {
    const isSquareSmallButton = this.width <= 80 && this.height >= 70 && this.height <= 100;
    
    if (isSquareSmallButton) {
      // 小按钮圆形边框
      const centerX = this.x + this.width / 2;
      const centerY = this.y + Math.min(this.width, this.height - 25) / 2 + 10;
      const radius = Math.min(this.width, this.height - 25) * 0.35;
      
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
      ctx.lineWidth = 2;
      ctx.stroke();
    } else {
      // 正常按钮矩形边框
      this.drawRoundedRect(ctx, this.x, this.y, this.width, this.height, this.borderRadius);
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
      ctx.lineWidth = 2;
      ctx.stroke();
    }
  }
  
  /**
   * 绘制图标
   */
  drawIcon(ctx) {
    if (typeof this.icon === 'string') {
      // 文本图标
      const isSquareSmallButton = this.width <= 80 && this.height >= 70 && this.height <= 100;
      
      if (isSquareSmallButton) {
        // 小按钮圆形布局：图标在圆形中央
        const centerX = this.x + this.width / 2;
        const centerY = this.y + Math.min(this.width, this.height - 25) / 2 + 10;
        const radius = Math.min(this.width, this.height - 25) * 0.35;
        const iconSize = radius * 0.8;
        
        ctx.font = `${iconSize}px Arial`;
        ctx.fillStyle = '#FFFFFF';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        
        ctx.fillText(this.icon, centerX, centerY);
      } else {
        // 正常按钮布局
        ctx.font = `${this.height * 0.4}px Arial`;
        ctx.fillStyle = this.textColor;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        
        const iconY = this.y + this.height * 0.35;
        ctx.fillText(this.icon, this.x + this.width / 2, iconY);
      }
    } else if (this.icon && this.icon.complete) {
      // 图片图标
      const isSquareSmallButton = this.width <= 80 && this.height >= 70 && this.height <= 100;
      
      if (isSquareSmallButton) {
        // 小按钮圆形布局
        const centerX = this.x + this.width / 2;
        const centerY = this.y + Math.min(this.width, this.height - 25) / 2 + 10;
        const radius = Math.min(this.width, this.height - 25) * 0.35;
        const iconSize = radius * 1.2;
        const iconX = centerX - iconSize / 2;
        const iconY = centerY - iconSize / 2;
        
        ctx.drawImage(this.icon, iconX, iconY, iconSize, iconSize);
      } else {
        // 正常按钮布局
        const iconSize = Math.min(this.width, this.height) * 0.4;
        const iconX = this.x + (this.width - iconSize) / 2;
        const iconY = this.y + this.height * 0.15;
        
        ctx.drawImage(this.icon, iconX, iconY, iconSize, iconSize);
      }
    }
  }
  
  /**
   * 绘制文本
   */
  drawText(ctx) {
    if (!this.text) return;
    
    const isSquareSmallButton = this.width <= 80 && this.height >= 70 && this.height <= 100;
    
    if (isSquareSmallButton) {
      // 小按钮圆形布局：文本在圆形下方
      const radius = Math.min(this.width, this.height - 25) * 0.35;
      const textY = this.y + radius * 2 + 20;
      const fontSize = 10; // 固定较小字体
      
      ctx.font = `${fontSize}px Arial`;
      ctx.fillStyle = '#333333';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'top';
      
      // 添加文本阴影效果
      ctx.save();
      ctx.shadowColor = 'rgba(255, 255, 255, 0.8)';
      ctx.shadowBlur = 1;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 1;
      
      ctx.fillText(this.text, this.x + this.width / 2, textY);
      ctx.restore();
      
    } else {
      // 正常按钮布局
      const fontSize = Math.min(this.width / 6, this.height / 4);
      ctx.font = `${fontSize}px Arial`;
      ctx.fillStyle = this.textColor;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      
      const textY = this.icon ? 
        this.y + this.height * 0.75 : 
        this.y + this.height / 2;
      
      // 添加文本阴影效果
      if (this.shadow) {
        ctx.save();
        ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        ctx.shadowBlur = 2;
        ctx.shadowOffsetX = 1;
        ctx.shadowOffsetY = 1;
      }
      
      ctx.fillText(this.text, this.x + this.width / 2, textY);
      
      if (this.shadow) {
        ctx.restore();
      }
    }
  }
  
  /**
   * 绘制圆角矩形路径
   */
  drawRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();
  }
  
  /**
   * 设置按钮状态
   */
  setState(state) {
    this.state = state;
    this.targetScale = state === 'pressed' ? 0.95 : 1.0;
  }
  
  /**
   * 设置按钮可见性
   */
  setVisible(visible) {
    this.visible = visible;
  }
  
  /**
   * 设置按钮可用性
   */
  setEnabled(enabled) {
    this.enabled = enabled;
    this.state = enabled ? 'normal' : 'disabled';
  }
  
  /**
   * 更新按钮位置
   */
  setPosition(x, y) {
    this.x = x;
    this.y = y;
  }
  
  /**
   * 更新按钮大小
   */
  setSize(width, height) {
    this.width = width;
    this.height = height;
  }
}

export default EnhancedButton; 