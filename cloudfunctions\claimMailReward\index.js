// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: 'cloud1-9gzbxxbff827656f'
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  if (!openid) {
    return {
      success: false,
      error: 'No openid found'
    }
  }
  
  const { mailId } = event
  
  if (!mailId) {
    return {
      success: false,
      error: 'Mail ID is required'
    }
  }
  
  try {
    // 查询邮件接收记录
    const mailRecipientResult = await db.collection('mail_recipients').where({
      _openid: openid,
      mail_id: mailId,
      deleted: false
    }).get()
    
    if (mailRecipientResult.data.length === 0) {
      return {
        success: false,
        error: 'Mail not found'
      }
    }
    
    const mailRecipient = mailRecipientResult.data[0]
    
    // 检查是否已经领取过
    if (mailRecipient.is_claimed) {
      return {
        success: false,
        error: 'Reward already claimed'
      }
    }
    
    // 检查是否有奖励
    if (!mailRecipient.has_rewards) {
      return {
        success: false,
        error: 'No rewards to claim'
      }
    }
    
    // 获取邮件详情
    const mailResult = await db.collection('mails').doc(mailId).get()
    
    if (!mailResult.data) {
      return {
        success: false,
        error: 'Mail details not found'
      }
    }
    
    const mail = mailResult.data
    const rewards = mail.rewards || {}
    
    // 获取玩家数据
    const playerResult = await db.collection('player').where({
      _openid: openid
    }).get()
    
    if (playerResult.data.length === 0) {
      return {
        success: false,
        error: 'Player not found'
      }
    }
    
    const player = playerResult.data[0]
    const playerId = player._id
    
    // 开始事务处理
    const transaction = await db.startTransaction()
    
    try {
      // 更新邮件接收状态
      await transaction.collection('mail_recipients').doc(mailRecipient._id).update({
        data: {
          is_claimed: true,
          claimed_at: db.serverDate()
        }
      })
      
      // 准备更新玩家数据
      const updateData = {}
      
      // 更新基础资源
      if (rewards.xianyu) {
        updateData.xianyu = db.command.inc(rewards.xianyu)
      }
      if (rewards.lingshi) {
        updateData.lingshi = db.command.inc(rewards.lingshi)
      }
      if (rewards.lianlidian) {
        updateData.lianlidian = db.command.inc(rewards.lianlidian)
      }
      if (rewards.sword_intent) {
        updateData.sword_intent = db.command.inc(rewards.sword_intent)
      }
      
      // 更新玩家基础资源
      if (Object.keys(updateData).length > 0) {
        updateData.updatedAt = db.serverDate()
        await transaction.collection('player').doc(playerId).update({
          data: updateData
        })
      }
      
      // 处理物品奖励
      if (rewards.items && rewards.items.length > 0) {
        for (const item of rewards.items) {
          // 查询玩家是否已有该物品
          const existingItemResult = await transaction.collection('items').where({
            _openid: openid,
            name: item.name
          }).get()
          
          if (existingItemResult.data.length > 0) {
            // 如果已有该物品，增加数量
            const existingItem = existingItemResult.data[0]
            await transaction.collection('items').doc(existingItem._id).update({
              data: {
                count: db.command.inc(item.count || 1),
                updated_at: db.serverDate()
              }
            })
          } else {
            // 如果没有该物品，创建新记录
            await transaction.collection('items').add({
              data: {
                _openid: openid,
                name: item.name,
                type: item.type || 'material',
                quality: item.quality || 1,
                count: item.count || 1,
                description: item.description || '',
                created_at: db.serverDate(),
                updated_at: db.serverDate()
              }
            })
          }
        }
      }
      
      // 提交事务
      await transaction.commit()
      
      return {
        success: true,
        message: 'Reward claimed successfully',
        rewards: rewards
      }
      
    } catch (transactionError) {
      // 回滚事务
      await transaction.rollback()
      throw transactionError
    }
    
  } catch (error) {
    console.error('Claim mail reward error:', error)
    return {
      success: false,
      error: error.message || 'Internal server error'
    }
  }
}
