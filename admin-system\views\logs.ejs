<%- include('header') %>

<div class="container-fluid">
    <div class="row">
        <%- include('sidebar') %>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">系统监控与日志管理</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshLogs()">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportLogs()">
                            <i class="fas fa-download"></i> 导出日志
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearOldLogs()">
                            <i class="fas fa-trash"></i> 清理旧日志
                        </button>
                    </div>
                </div>
            </div>

            <!-- 系统监控面板 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-white bg-primary">
                        <div class="card-header"><i class="fas fa-server"></i> 系统状态</div>
                        <div class="card-body">
                            <h4 class="card-title" id="systemStatus">正常</h4>
                            <p class="card-text">运行时间: <span id="uptime">--</span></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-success">
                        <div class="card-header"><i class="fas fa-clock"></i> 响应时间</div>
                        <div class="card-body">
                            <h4 class="card-title" id="responseTime">-- ms</h4>
                            <p class="card-text">平均响应时间</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-warning">
                        <div class="card-header"><i class="fas fa-exclamation-triangle"></i> 错误数量</div>
                        <div class="card-body">
                            <h4 class="card-title" id="errorCount">--</h4>
                            <p class="card-text">24小时内错误</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-info">
                        <div class="card-header"><i class="fas fa-users"></i> 活跃管理员</div>
                        <div class="card-body">
                            <h4 class="card-title" id="activeAdmins">--</h4>
                            <p class="card-text">当前在线管理员</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 日志搜索筛选 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">日志搜索与筛选</h5>
                </div>
                <div class="card-body">
                    <form class="row g-3" id="logSearchForm">
                        <div class="col-md-3">
                            <label for="logLevel" class="form-label">日志级别</label>
                            <select class="form-select" id="logLevel">
                                <option value="">全部级别</option>
                                <option value="info">信息</option>
                                <option value="warn">警告</option>
                                <option value="error">错误</option>
                                <option value="debug">调试</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="logModule" class="form-label">功能模块</label>
                            <select class="form-select" id="logModule">
                                <option value="">全部模块</option>
                                <option value="auth">认证系统</option>
                                <option value="player">玩家管理</option>
                                <option value="mail">邮件系统</option>
                                <option value="template">模板管理</option>
                                <option value="system">系统操作</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="startDate" class="form-label">开始时间</label>
                            <input type="datetime-local" class="form-control" id="startDate">
                        </div>
                        <div class="col-md-3">
                            <label for="endDate" class="form-label">结束时间</label>
                            <input type="datetime-local" class="form-control" id="endDate">
                        </div>
                        <div class="col-md-6">
                            <label for="logKeyword" class="form-label">关键词搜索</label>
                            <input type="text" class="form-control" id="logKeyword" placeholder="输入关键词搜索日志内容">
                        </div>
                        <div class="col-md-3">
                            <label for="adminUser" class="form-label">操作管理员</label>
                            <input type="text" class="form-control" id="adminUser" placeholder="管理员用户名">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="resetLogSearch()">
                                    <i class="fas fa-undo"></i> 重置
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 日志统计图表 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">24小时日志趋势</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="logTrendChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">日志级别分布</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="logLevelChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 日志列表 -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">系统日志</h5>
                    <div>
                        <small class="text-muted">共 <span id="logTotalCount">0</span> 条记录</small>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>时间</th>
                                    <th>级别</th>
                                    <th>模块</th>
                                    <th>操作管理员</th>
                                    <th>操作内容</th>
                                    <th>IP地址</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="logTableBody">
                                <tr>
                                    <td colspan="7" class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    <nav aria-label="日志分页">
                        <ul class="pagination pagination-sm justify-content-center mb-0" id="logPagination">
                            <!-- 分页内容将由JavaScript动态生成 -->
                        </ul>
                    </nav>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- 日志详情模态框 -->
<div class="modal fade" id="logDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">日志详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>时间:</strong> <span id="detailTimestamp"></span></p>
                        <p><strong>级别:</strong> <span id="detailLevel"></span></p>
                        <p><strong>模块:</strong> <span id="detailModule"></span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>管理员:</strong> <span id="detailAdmin"></span></p>
                        <p><strong>IP地址:</strong> <span id="detailIp"></span></p>
                        <p><strong>用户代理:</strong> <span id="detailUserAgent"></span></p>
                    </div>
                </div>
                <hr>
                <div>
                    <strong>操作内容:</strong>
                    <pre class="bg-light p-3 mt-2" id="detailMessage"></pre>
                </div>
                <div id="detailData" style="display: none;">
                    <strong>详细数据:</strong>
                    <pre class="bg-light p-3 mt-2" id="detailDataContent"></pre>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<script>
// 全局变量
let currentLogPage = 1;
let logPageSize = 20;
let logTotalPages = 1;
let logTrendChart, logLevelChart;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    loadSystemStatus();
    loadLogs();
    
    // 绑定搜索表单事件
    document.getElementById('logSearchForm').addEventListener('submit', function(e) {
        e.preventDefault();
        currentLogPage = 1;
        loadLogs();
    });
    
    // 每5分钟刷新一次系统状态
    setInterval(loadSystemStatus, 5 * 60 * 1000);
});

// 初始化图表
function initializeCharts() {
    // 日志趋势图表
    const trendCtx = document.getElementById('logTrendChart').getContext('2d');
    logTrendChart = new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '日志数量',
                data: [],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // 日志级别分布图表
    const levelCtx = document.getElementById('logLevelChart').getContext('2d');
    logLevelChart = new Chart(levelCtx, {
        type: 'doughnut',
        data: {
            labels: ['信息', '警告', '错误', '调试'],
            datasets: [{
                data: [0, 0, 0, 0],
                backgroundColor: [
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 206, 86, 0.8)',
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(75, 192, 192, 0.8)'
                ]
            }]
        },
        options: {
            responsive: true
        }
    });
}

// 加载系统状态
async function loadSystemStatus() {
    try {
        const response = await fetch('/api/system/status');
        const data = await response.json();
        
        if (data.success) {
            const status = data.data;
            document.getElementById('systemStatus').textContent = status.status;
            document.getElementById('uptime').textContent = formatUptime(status.uptime);
            document.getElementById('responseTime').textContent = status.avgResponseTime + ' ms';
            document.getElementById('errorCount').textContent = status.errorCount;
            document.getElementById('activeAdmins').textContent = status.activeAdmins;
            
            // 更新图表数据
            updateLogCharts(status.logTrend, status.logLevelStats);
        }
    } catch (error) {
        console.error('加载系统状态失败:', error);
    }
}

// 加载日志列表
async function loadLogs() {
    try {
        const params = new URLSearchParams({
            page: currentLogPage,
            pageSize: logPageSize,
            level: document.getElementById('logLevel').value || '',
            module: document.getElementById('logModule').value || '',
            startDate: document.getElementById('startDate').value || '',
            endDate: document.getElementById('endDate').value || '',
            keyword: document.getElementById('logKeyword').value || '',
            admin: document.getElementById('adminUser').value || ''
        });
        
        const response = await fetch(`/api/logs?${params}`);
        const data = await response.json();
        
        if (data.success) {
            renderLogTable(data.data.logs);
            renderLogPagination(data.data.pagination);
            document.getElementById('logTotalCount').textContent = data.data.pagination.total;
        } else {
            showAlert('danger', '加载日志失败: ' + data.message);
        }
    } catch (error) {
        console.error('加载日志失败:', error);
        showAlert('danger', '加载日志失败: ' + error.message);
    }
}

// 渲染日志表格
function renderLogTable(logs) {
    const tbody = document.getElementById('logTableBody');
    
    if (logs.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">暂无日志记录</td></tr>';
        return;
    }
    
    tbody.innerHTML = logs.map(log => `
        <tr>
            <td>${formatDateTime(log.timestamp)}</td>
            <td><span class="badge bg-${getLevelColor(log.level)}">${getLevelText(log.level)}</span></td>
            <td>${log.module || '-'}</td>
            <td>${log.admin_user || '-'}</td>
            <td class="text-truncate" style="max-width: 300px;" title="${log.message}">${log.message}</td>
            <td>${log.ip_address || '-'}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="showLogDetail('${log._id}')">
                    <i class="fas fa-eye"></i> 详情
                </button>
            </td>
        </tr>
    `).join('');
}

// 渲染分页
function renderLogPagination(pagination) {
    const paginationElement = document.getElementById('logPagination');
    logTotalPages = pagination.totalPages;
    currentLogPage = pagination.page;
    
    let paginationHtml = '';
    
    // 上一页按钮
    if (pagination.page > 1) {
        paginationHtml += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="changeLogPage(${pagination.page - 1})">上一页</a>
            </li>
        `;
    }
    
    // 页码按钮
    const startPage = Math.max(1, pagination.page - 2);
    const endPage = Math.min(pagination.totalPages, pagination.page + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHtml += `
            <li class="page-item ${i === pagination.page ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changeLogPage(${i})">${i}</a>
            </li>
        `;
    }
    
    // 下一页按钮
    if (pagination.page < pagination.totalPages) {
        paginationHtml += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="changeLogPage(${pagination.page + 1})">下一页</a>
            </li>
        `;
    }
    
    paginationElement.innerHTML = paginationHtml;
}

// 更新图表数据
function updateLogCharts(trendData, levelStats) {
    // 更新趋势图表
    logTrendChart.data.labels = trendData.map(item => item.hour + ':00');
    logTrendChart.data.datasets[0].data = trendData.map(item => item.count);
    logTrendChart.update();
    
    // 更新级别分布图表
    logLevelChart.data.datasets[0].data = [
        levelStats.info || 0,
        levelStats.warn || 0,
        levelStats.error || 0,
        levelStats.debug || 0
    ];
    logLevelChart.update();
}

// 显示日志详情
async function showLogDetail(logId) {
    try {
        const response = await fetch(`/api/logs/${logId}`);
        const data = await response.json();
        
        if (data.success) {
            const log = data.data;
            
            document.getElementById('detailTimestamp').textContent = formatDateTime(log.timestamp);
            document.getElementById('detailLevel').innerHTML = `<span class="badge bg-${getLevelColor(log.level)}">${getLevelText(log.level)}</span>`;
            document.getElementById('detailModule').textContent = log.module || '-';
            document.getElementById('detailAdmin').textContent = log.admin_user || '-';
            document.getElementById('detailIp').textContent = log.ip_address || '-';
            document.getElementById('detailUserAgent').textContent = log.user_agent || '-';
            document.getElementById('detailMessage').textContent = log.message;
            
            if (log.data) {
                document.getElementById('detailData').style.display = 'block';
                document.getElementById('detailDataContent').textContent = JSON.stringify(log.data, null, 2);
            } else {
                document.getElementById('detailData').style.display = 'none';
            }
            
            new bootstrap.Modal(document.getElementById('logDetailModal')).show();
        } else {
            showAlert('danger', '获取日志详情失败: ' + data.message);
        }
    } catch (error) {
        console.error('获取日志详情失败:', error);
        showAlert('danger', '获取日志详情失败: ' + error.message);
    }
}

// 刷新日志
function refreshLogs() {
    loadSystemStatus();
    loadLogs();
    showAlert('success', '日志已刷新');
}

// 导出日志
async function exportLogs() {
    try {
        const params = new URLSearchParams({
            level: document.getElementById('logLevel').value || '',
            module: document.getElementById('logModule').value || '',
            startDate: document.getElementById('startDate').value || '',
            endDate: document.getElementById('endDate').value || '',
            keyword: document.getElementById('logKeyword').value || '',
            admin: document.getElementById('adminUser').value || ''
        });
        
        const response = await fetch(`/api/logs/export?${params}`);
        
        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `logs_${new Date().toISOString().slice(0, 10)}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            
            showAlert('success', '日志导出成功');
        } else {
            showAlert('danger', '日志导出失败');
        }
    } catch (error) {
        console.error('导出日志失败:', error);
        showAlert('danger', '导出日志失败: ' + error.message);
    }
}

// 清理旧日志
async function clearOldLogs() {
    if (!confirm('确定要清理30天前的日志吗？此操作不可恢复。')) {
        return;
    }
    
    try {
        const response = await fetch('/api/logs/cleanup', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ days: 30 })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('success', `成功清理了 ${data.data.deletedCount} 条旧日志`);
            loadLogs();
        } else {
            showAlert('danger', '清理日志失败: ' + data.message);
        }
    } catch (error) {
        console.error('清理日志失败:', error);
        showAlert('danger', '清理日志失败: ' + error.message);
    }
}

// 重置搜索表单
function resetLogSearch() {
    document.getElementById('logSearchForm').reset();
    currentLogPage = 1;
    loadLogs();
}

// 切换页码
function changeLogPage(page) {
    currentLogPage = page;
    loadLogs();
}

// 工具函数
function formatDateTime(timestamp) {
    return new Date(timestamp).toLocaleString('zh-CN');
}

function formatUptime(seconds) {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${days}天 ${hours}小时 ${minutes}分钟`;
}

function getLevelColor(level) {
    const colors = {
        'info': 'primary',
        'warn': 'warning',
        'error': 'danger',
        'debug': 'secondary'
    };
    return colors[level] || 'secondary';
}

function getLevelText(level) {
    const texts = {
        'info': '信息',
        'warn': '警告',
        'error': '错误',
        'debug': '调试'
    };
    return texts[level] || level;
}

function showAlert(type, message) {
    // 创建提示框
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 3000);
}
</script>

<%- include('footer') %> 