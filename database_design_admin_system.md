# 后台管理系统数据库设计

## 概述
为了支持更灵活的游戏内容管理，我们将创建一套完整的后台管理系统，包含物品模板管理、抽取池配置等功能。这些模板数据将独立存储在数据库中，玩家抽取时从模板表中获取配置。

## 新增数据表

### 1. skill_temp (功法模板表)
存储所有功法的模板配置数据。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| _id | string | PRIMARY KEY | 模板ID |
| name | string | NOT NULL | 功法名称 |
| realm | string | NOT NULL | 所属境界 |
| description | string | | 功法描述 |
| icon | string | | 图标路径 |
| rarity | number | DEFAULT 1 | 稀有度(1-5) |
| max_level | number | DEFAULT 30 | 最大等级 |
| base_attributes | object | | 基础属性 |
| level_growth | object | | 每级成长属性 |
| unlock_requirements | object | | 解锁条件 |
| is_active | boolean | DEFAULT true | 是否启用 |
| created_at | datetime | DEFAULT NOW | 创建时间 |
| updated_at | datetime | DEFAULT NOW | 更新时间 |

**base_attributes 结构：**
```json
{
  "hp": 20,
  "attack": 5,
  "defense": 3,
  "speed": 0,
  "crit": 0,
  "penetration": 0
}
```

**level_growth 结构：**
```json
{
  "hp": 4,
  "attack": 1,
  "defense": 0.6
}
```

### 2. s_heart_temp (剑心模板表)
存储所有剑心的模板配置数据。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| _id | string | PRIMARY KEY | 模板ID |
| name | string | NOT NULL | 剑心名称 |
| description | string | | 剑心描述 |
| color | string | | 显示颜色 |
| rarity | number | DEFAULT 1 | 稀有度(1-5) |
| max_level | number | DEFAULT 50 | 最大等级 |
| max_advancement | number | DEFAULT 9 | 最大进阶等级 |
| base_attributes | object | | 基础属性 |
| level_up_attributes | object | | 每级提升属性 |
| advancement_attributes | object | | 进阶加成属性 |
| advancement_requirements | array | | 进阶需求材料 |
| is_active | boolean | DEFAULT true | 是否启用 |
| created_at | datetime | DEFAULT NOW | 创建时间 |
| updated_at | datetime | DEFAULT NOW | 更新时间 |

### 3. treasure_tmp (古宝模板表)
存储所有古宝的模板配置数据。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| _id | string | PRIMARY KEY | 模板ID |
| name | string | NOT NULL | 古宝名称 |
| type | string | NOT NULL | 类型(attack/defense/special) |
| category | string | NOT NULL | 分类(weapon/artifact/talisman) |
| rarity | number | DEFAULT 1 | 稀有度(1-5) |
| max_level | number | DEFAULT 100 | 最大等级 |
| max_star | number | DEFAULT 5 | 最大星级 |
| base_attributes | object | | 基础属性 |
| upgrade_growth | object | | 每级成长属性 |
| star_upgrade | array | | 升星配置 |
| set_id | string | | 套装ID |
| description | string | | 古宝描述 |
| icon | string | | 图标路径 |
| is_active | boolean | DEFAULT true | 是否启用 |
| created_at | datetime | DEFAULT NOW | 创建时间 |
| updated_at | datetime | DEFAULT NOW | 更新时间 |

### 4. item_temp (物品模板表)
存储所有物品的模板配置数据。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| _id | string | PRIMARY KEY | 模板ID |
| name | string | NOT NULL | 物品名称 |
| type | string | NOT NULL | 类型(pill/material/scroll/gem) |
| rarity | number | DEFAULT 1 | 稀有度(1-5) |
| max_stack | number | DEFAULT 99 | 最大堆叠数量 |
| effects | object | | 物品效果 |
| use_conditions | object | | 使用条件 |
| description | string | | 物品描述 |
| icon | string | | 图标路径 |
| is_consumable | boolean | DEFAULT true | 是否消耗品 |
| is_active | boolean | DEFAULT true | 是否启用 |
| created_at | datetime | DEFAULT NOW | 创建时间 |

### 5. treasure_set (套装模板表)
存储古宝套装配置。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| _id | string | PRIMARY KEY | 套装ID |
| name | string | NOT NULL | 套装名称 |
| description | string | | 套装描述 |
| set_effects | array | | 套装效果配置 |
| required_pieces | array | | 需要的古宝ID列表 |
| is_active | boolean | DEFAULT true | 是否启用 |
| created_at | datetime | DEFAULT NOW | 创建时间 |
| updated_at | datetime | DEFAULT NOW | 更新时间 |

**set_effects 结构：**
```json
[
  {
    "pieces": 2,
    "attributes": {"attack": 50, "hp": 100},
    "description": "2件套：攻击力+50，生命值+100"
  },
  {
    "pieces": 4,
    "attributes": {"crit": 0.1, "crit_damage": 0.2},
    "description": "4件套：暴击率+10%，暴击伤害+20%"
  }
]
```

### 6. gacha_pools (抽取池配置表)
存储各种抽取池的配置。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| _id | string | PRIMARY KEY | 抽取池ID |
| name | string | NOT NULL | 抽取池名称 |
| type | string | NOT NULL | 类型(skill/treasure/sword_heart/item) |
| cost_type | string | NOT NULL | 消费货币类型 |
| single_cost | number | NOT NULL | 单抽消费 |
| ten_cost | number | NOT NULL | 十连抽消费 |
| pool_items | array | | 抽取池物品配置 |
| pity_config | object | | 保底配置 |
| is_active | boolean | DEFAULT true | 是否启用 |
| start_time | datetime | | 开始时间 |
| end_time | datetime | | 结束时间 |
| created_at | datetime | DEFAULT NOW | 创建时间 |
| updated_at | datetime | DEFAULT NOW | 更新时间 |

**pool_items 结构：**
```json
[
  {
    "template_id": "ancient_sword",
    "weight": 1,
    "rarity": 5
  },
  {
    "template_id": "dragon_blade", 
    "weight": 5,
    "rarity": 4
  }
]
```

### 7. admin_logs (管理员操作日志表)
记录所有管理员操作。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| _id | string | PRIMARY KEY | 日志ID |
| admin_id | string | NOT NULL | 管理员ID |
| action | string | NOT NULL | 操作类型 |
| target_type | string | NOT NULL | 目标类型 |
| target_id | string | | 目标ID |
| before_data | object | | 操作前数据 |
| after_data | object | | 操作后数据 |
| description | string | | 操作描述 |
| ip_address | string | | IP地址 |
| user_agent | string | | 用户代理 |
| created_at | datetime | DEFAULT NOW | 创建时间 |

## 现有表修改

### 1. player_skill 表修改
添加模板关联字段：

| 新增字段 | 类型 | 约束 | 说明 |
|----------|------|------|------|
| template_id | string | | 关联的功法模板ID |

### 2. sword_hearts 表修改
添加模板关联字段：

| 新增字段 | 类型 | 约束 | 说明 |
|----------|------|------|------|
| template_id | string | | 关联的剑心模板ID |

### 3. player_treasures 表修改
添加模板关联字段：

| 新增字段 | 类型 | 约束 | 说明 |
|----------|------|------|------|
| template_id | string | | 关联的古宝模板ID |

### 4. player_items 表修改
添加模板关联字段：

| 新增字段 | 类型 | 约束 | 说明 |
|----------|------|------|------|
| template_id | string | | 关联的物品模板ID |

## 数据迁移策略

### 1. 静态配置迁移
- 将 `SkillConfig.js` 中的功法数据导入到 `skill_templates` 表
- 将 `SwordHeartConfig.js` 中的剑心数据导入到 `sword_heart_templates` 表  
- 将 `treasure_data.js` 中的古宝数据导入到 `treasure_templates` 表

### 2. 现有玩家数据迁移
- 为现有的玩家技能数据添加 `template_id` 关联
- 为现有的玩家剑心数据添加 `template_id` 关联
- 为现有的玩家古宝数据添加 `template_id` 关联

### 3. 系统修改
- 修改抽取系统从模板表读取配置
- 修改属性计算系统使用模板数据
- 保持向后兼容性，支持无模板ID的旧数据

## 后台管理功能

### 1. 功法模板管理
- 创建、编辑、删除功法模板
- 批量导入/导出功能
- 按境界、稀有度筛选
- 预览功法属性成长曲线

### 2. 剑心模板管理
- 创建、编辑、删除剑心模板
- 配置进阶需求和属性加成
- 颜色选择器
- 批量操作功能

### 3. 古宝模板管理
- 创建、编辑、删除古宝模板
- 配置升级和升星属性
- 套装关联管理
- 图标上传功能

### 4. 抽取池管理
- 创建、编辑抽取池配置
- 权重分配和概率预览
- 保底机制配置
- 活动时间设置

### 5. 数据统计
- 模板使用情况统计
- 抽取数据分析
- 玩家获取统计
- 操作日志查看

## 技术实现

### 1. 后端API
- RESTful API设计
- 数据验证和错误处理
- 操作日志记录
- 权限控制

### 2. 前端界面
- 响应式设计
- 表格分页和搜索
- 表单验证
- 批量操作支持

### 3. 数据同步
- 实时数据更新
- 缓存策略
- 版本控制
- 回滚机制

这个设计将为游戏提供强大而灵活的内容管理能力，管理员可以通过后台轻松管理所有游戏物品，而玩家的抽取体验将更加丰富和平衡。 