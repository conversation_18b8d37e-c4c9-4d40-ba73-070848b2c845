// 云函数入口文件
const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
  env: 'cloud1-9gzbxxbff827656f'
});

/**
 * 用户登录云函数
 * 获取用户的openid、appid等基本信息
 * 兼容旧版本调用方式
 */
exports.main = async (event, context) => {
  try {
    // 获取微信调用上下文
    const wxContext = cloud.getWXContext();

    // 记录登录日志
    console.log('用户登录', {
      timestamp: new Date().toISOString(),
      openid: wxContext.OPENID ? '已获取' : '未获取',
      event: event
    });

    // 检查是否成功获取到openid
    if (!wxContext.OPENID) {
      console.error('登录失败：未能获取到用户openid');
      return {
        success: false,
        error: '登录失败，未能获取到用户信息',
        code: 'LOGIN_FAILED'
      };
    }

    // 返回登录结果（保持向后兼容）
    return {
      success: true,
      event,
      openid: wxContext.OPENID,
      appid: wxContext.APPID,
      unionid: wxContext.UNIONID,
      timestamp: Date.now()
    };

  } catch (error) {
    console.error('登录云函数执行出错:', error);

    return {
      success: false,
      error: error.message || '登录过程中发生错误',
      code: 'FUNCTION_ERROR'
    };
  }
};