// 云函数入口文件 - 通用数据库服务
const cloud = require('wx-server-sdk')

cloud.init({
  env: 'cloud1-9gzbxxbff827656f'
})

const db = cloud.database()
const _ = db.command

// 数据表映射 - 基于优化后的数据库设计
const TABLES = {
  // 核心数据表
  PLAYERS: 'players',
  PLAYER_RESOURCES: 'player_resources',
  CHARACTERS: 'characters',
  PLAYER_TREASURES: 'player_treasures',
  PLAYER_SKILLS: 'player_skills',
  PLAYER_ITEMS: 'player_items',
  PLAYER_SWORD_HEARTS: 'player_sword_hearts',
  PLAYER_SWORD_BONES: 'player_sword_bones',
  
  // 系统功能表
  PLAYER_DONGFU: 'player_dongfu',
  PLAYER_ARENA: 'player_arena',
  PLAYER_IDLE: 'player_idle',
  PLAYER_SKILL_CULTIVATION: 'player_skill_cultivation',
  
  // 交易记录表
  RECHARGE_RECORDS: 'recharge_records',
  GACHA_RECORDS: 'gacha_records',
  BATTLE_RECORDS: 'battle_records',
  
  // 邮件通知表
  MAIL_TEMPLATES: 'mail_templates',
  PLAYER_MAILS: 'player_mails',
  
  // 活动任务表
  DAILY_TASKS: 'daily_tasks',
  ACTIVITY_PARTICIPATION: 'activity_participation',
  
  // 系统配置表
  GAME_CONFIGS: 'game_configs'
}

// 操作类型
const OPERATIONS = {
  CREATE: 'create',
  CREATE_MANY: 'createMany',
  UPDATE: 'update',
  UPDATE_MANY: 'updateMany',
  DELETE: 'delete',
  DELETE_MANY: 'deleteMany',
  GET: 'get',
  LIST: 'list',
  COUNT: 'count',
  AGGREGATE: 'aggregate'
}

// 需要openid权限校验的表
const OPENID_REQUIRED_TABLES = [
  TABLES.PLAYERS,
  TABLES.PLAYER_RESOURCES,
  TABLES.CHARACTERS,
  TABLES.PLAYER_TREASURES,
  TABLES.PLAYER_SKILLS,
  TABLES.PLAYER_ITEMS,
  TABLES.PLAYER_SWORD_HEARTS,
  TABLES.PLAYER_SWORD_BONES,
  TABLES.PLAYER_DONGFU,
  TABLES.PLAYER_ARENA,
  TABLES.PLAYER_IDLE,
  TABLES.PLAYER_SKILL_CULTIVATION,
  TABLES.RECHARGE_RECORDS,
  TABLES.GACHA_RECORDS,
  TABLES.BATTLE_RECORDS,
  TABLES.PLAYER_MAILS,
  TABLES.DAILY_TASKS,
  TABLES.ACTIVITY_PARTICIPATION
]

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  const { 
    table, 
    operation, 
    params = {}, 
    skipAuth = false,
    transaction = false 
  } = event
  
  try {
    // 权限校验
    if (!skipAuth && OPENID_REQUIRED_TABLES.includes(table)) {
      if (!openid) {
        return {
          success: false,
          error: '未授权访问',
          code: 'UNAUTHORIZED'
        }
      }
      
      // 自动添加openid过滤条件
      if (!params.filter) params.filter = {}
      if (!params.filter.where) params.filter.where = {}
      
      // 确保只能操作自己的数据
      if (operation !== OPERATIONS.CREATE) {
        params.filter.where._openid = openid
      } else if (params.data) {
        // 创建时自动设置openid
        if (Array.isArray(params.data)) {
          params.data.forEach(item => item._openid = openid)
        } else {
          params.data._openid = openid
        }
      }
    }
    
    // 验证表名
    if (!Object.values(TABLES).includes(table)) {
      return {
        success: false,
        error: `无效的表名: ${table}`,
        code: 'INVALID_TABLE'
      }
    }
    
    // 验证操作类型
    if (!Object.values(OPERATIONS).includes(operation)) {
      return {
        success: false,
        error: `无效的操作类型: ${operation}`,
        code: 'INVALID_OPERATION'
      }
    }
    
    const collection = db.collection(table)
    let result
    
    // 执行数据库操作
    switch (operation) {
      case OPERATIONS.CREATE:
        result = await executeCreate(collection, params)
        break
        
      case OPERATIONS.CREATE_MANY:
        result = await executeCreateMany(collection, params)
        break
        
      case OPERATIONS.UPDATE:
        result = await executeUpdate(collection, params)
        break
        
      case OPERATIONS.UPDATE_MANY:
        result = await executeUpdateMany(collection, params)
        break
        
      case OPERATIONS.DELETE:
        result = await executeDelete(collection, params)
        break
        
      case OPERATIONS.DELETE_MANY:
        result = await executeDeleteMany(collection, params)
        break
        
      case OPERATIONS.GET:
        result = await executeGet(collection, params)
        break
        
      case OPERATIONS.LIST:
        result = await executeList(collection, params)
        break
        
      case OPERATIONS.COUNT:
        result = await executeCount(collection, params)
        break
        
      case OPERATIONS.AGGREGATE:
        result = await executeAggregate(collection, params)
        break
        
      default:
        throw new Error(`不支持的操作: ${operation}`)
    }
    
    return {
      success: true,
      data: result,
      timestamp: Date.now()
    }
    
  } catch (error) {
    console.error('数据库操作失败:', error)
    return {
      success: false,
      error: error.message,
      code: error.code || 'DB_OPERATION_FAILED',
      timestamp: Date.now()
    }
  }
}

// 执行创建操作
async function executeCreate(collection, params) {
  const { data } = params
  
  if (!data) {
    throw new Error('创建操作需要data参数')
  }
  
  // 添加时间戳
  const createData = {
    ...data,
    created_at: db.serverDate(),
    updated_at: db.serverDate()
  }
  
  const result = await collection.add({
    data: createData
  })
  
  return {
    _id: result._id,
    data: createData
  }
}

// 执行批量创建操作
async function executeCreateMany(collection, params) {
  const { data } = params
  
  if (!Array.isArray(data) || data.length === 0) {
    throw new Error('批量创建操作需要data数组参数')
  }
  
  // 添加时间戳
  const createData = data.map(item => ({
    ...item,
    created_at: db.serverDate(),
    updated_at: db.serverDate()
  }))
  
  // 分批处理，每批最多20条
  const batchSize = 20
  const results = []
  
  for (let i = 0; i < createData.length; i += batchSize) {
    const batch = createData.slice(i, i + batchSize)
    const batchResult = await Promise.all(
      batch.map(item => collection.add({ data: item }))
    )
    results.push(...batchResult)
  }
  
  return {
    count: results.length,
    ids: results.map(r => r._id)
  }
}

// 执行更新操作
async function executeUpdate(collection, params) {
  const { filter, data } = params
  
  if (!filter || !data) {
    throw new Error('更新操作需要filter和data参数')
  }
  
  // 添加更新时间戳
  const updateData = {
    ...data,
    updated_at: db.serverDate()
  }
  
  const query = buildQuery(collection, filter)
  const result = await query.update({
    data: updateData
  })
  
  return {
    stats: result.stats
  }
}

// 执行批量更新操作
async function executeUpdateMany(collection, params) {
  const { filter, data } = params
  
  if (!filter || !data) {
    throw new Error('批量更新操作需要filter和data参数')
  }
  
  // 添加更新时间戳
  const updateData = {
    ...data,
    updated_at: db.serverDate()
  }
  
  const query = buildQuery(collection, filter)
  const result = await query.update({
    data: updateData
  })
  
  return {
    stats: result.stats
  }
}

// 执行删除操作
async function executeDelete(collection, params) {
  const { filter } = params
  
  if (!filter) {
    throw new Error('删除操作需要filter参数')
  }
  
  const query = buildQuery(collection, filter)
  const result = await query.remove()
  
  return {
    stats: result.stats
  }
}

// 执行批量删除操作
async function executeDeleteMany(collection, params) {
  const { filter } = params
  
  if (!filter) {
    throw new Error('批量删除操作需要filter参数')
  }
  
  const query = buildQuery(collection, filter)
  const result = await query.remove()
  
  return {
    stats: result.stats
  }
}

// 执行查询单条记录操作
async function executeGet(collection, params) {
  const { filter = {}, fields } = params
  
  let query = buildQuery(collection, filter)
  
  if (fields && Array.isArray(fields)) {
    const fieldObj = {}
    fields.forEach(field => fieldObj[field] = true)
    query = query.field(fieldObj)
  }
  
  const result = await query.get()
  
  return result.data.length > 0 ? result.data[0] : null
}

// 执行列表查询操作
async function executeList(collection, params) {
  const { 
    filter = {}, 
    fields, 
    pageSize = 20, 
    pageNumber = 1,
    getCount = false 
  } = params
  
  let query = buildQuery(collection, filter)
  
  if (fields && Array.isArray(fields)) {
    const fieldObj = {}
    fields.forEach(field => fieldObj[field] = true)
    query = query.field(fieldObj)
  }
  
  // 分页
  const skip = (pageNumber - 1) * pageSize
  query = query.skip(skip).limit(pageSize)
  
  const result = await query.get()
  
  const response = {
    records: result.data,
    pageNumber,
    pageSize
  }
  
  // 如果需要总数
  if (getCount) {
    const countQuery = buildQuery(collection, filter)
    const countResult = await countQuery.count()
    response.total = countResult.total
    response.totalPages = Math.ceil(countResult.total / pageSize)
  }
  
  return response
}

// 执行计数操作
async function executeCount(collection, params) {
  const { filter = {} } = params
  
  const query = buildQuery(collection, filter)
  const result = await query.count()
  
  return {
    count: result.total
  }
}

// 执行聚合操作
async function executeAggregate(collection, params) {
  const { pipeline } = params
  
  if (!Array.isArray(pipeline)) {
    throw new Error('聚合操作需要pipeline数组参数')
  }
  
  const result = await collection.aggregate()
    .match(pipeline[0] || {})
    .group(pipeline[1] || {})
    .end()
  
  return result.list
}

// 构建查询对象
function buildQuery(collection, filter) {
  let query = collection
  
  if (filter.where) {
    query = query.where(filter.where)
  }
  
  if (filter.orderBy) {
    if (Array.isArray(filter.orderBy)) {
      filter.orderBy.forEach(order => {
        query = query.orderBy(order.field, order.direction || 'asc')
      })
    } else {
      query = query.orderBy(filter.orderBy.field, filter.orderBy.direction || 'asc')
    }
  }
  
  return query
} 