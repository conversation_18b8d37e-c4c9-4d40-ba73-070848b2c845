/**
 * 好感度进度条组件
 * 用于显示仙友的好感度进度，支持动画效果
 * 版本: v1.0 - 基础好感度进度条功能
 */
class FavorabilityBar {
  constructor(config) {
    this.x = config.x || 0;
    this.y = config.y || 0;
    this.width = config.width || 200;
    this.height = config.height || 20;
    
    // 好感度数据
    this.currentValue = config.currentValue || 0;
    this.maxValue = config.maxValue || 100;
    this.targetValue = this.currentValue;
    
    // 显示配置
    this.showText = config.showText !== false;
    this.showPercentage = config.showPercentage || false;
    this.borderRadius = config.borderRadius || 10;
    
    // 颜色配置
    this.backgroundColor = config.backgroundColor || '#E5E5EA';
    this.fillColor = config.fillColor || null; // 如果为null，使用渐变色
    this.borderColor = config.borderColor || '#C7C7CC';
    this.textColor = config.textColor || '#1C1C1E';
    
    // 动画配置
    this.animationSpeed = config.animationSpeed || 0.05;
    this.animatedValue = this.currentValue;
    this.enableAnimation = config.enableAnimation !== false;
    
    // 状态
    this.visible = config.visible !== false;
    
    // 回调函数
    this.onValueChange = config.onValueChange || null;
    this.onAnimationComplete = config.onAnimationComplete || null;
  }
  
  /**
   * 更新组件
   */
  update(deltaTime) {
    if (!this.enableAnimation) {
      this.animatedValue = this.targetValue;
      return;
    }
    
    // 平滑动画到目标值
    if (Math.abs(this.animatedValue - this.targetValue) > 0.1) {
      const diff = this.targetValue - this.animatedValue;
      this.animatedValue += diff * this.animationSpeed;
    } else {
      if (this.animatedValue !== this.targetValue) {
        this.animatedValue = this.targetValue;
        if (this.onAnimationComplete) {
          this.onAnimationComplete(this.animatedValue);
        }
      }
    }
  }
  
  /**
   * 渲染进度条
   */
  render(ctx) {
    if (!this.visible) return;
    
    ctx.save();
    
    // 绘制背景
    this.drawBackground(ctx);
    
    // 绘制进度
    this.drawProgress(ctx);
    
    // 绘制边框
    this.drawBorder(ctx);
    
    // 绘制文本
    if (this.showText) {
      this.drawText(ctx);
    }
    
    ctx.restore();
  }
  
  /**
   * 绘制背景
   */
  drawBackground(ctx) {
    this.drawRoundedRect(ctx, this.x, this.y, this.width, this.height, this.borderRadius);
    ctx.fillStyle = this.backgroundColor;
    ctx.fill();
  }
  
  /**
   * 绘制进度
   */
  drawProgress(ctx) {
    const progress = Math.min(Math.max(this.animatedValue / this.maxValue, 0), 1);
    const progressWidth = this.width * progress;
    
    if (progressWidth <= 0) return;
    
    // 创建裁剪区域
    ctx.save();
    this.drawRoundedRect(ctx, this.x, this.y, this.width, this.height, this.borderRadius);
    ctx.clip();
    
    // 绘制进度条
    this.drawRoundedRect(ctx, this.x, this.y, progressWidth, this.height, this.borderRadius);
    
    if (this.fillColor) {
      // 使用单色
      ctx.fillStyle = this.fillColor;
    } else {
      // 使用渐变色，根据好感度等级变化
      const gradient = this.createProgressGradient(ctx, progress);
      ctx.fillStyle = gradient;
    }
    
    ctx.fill();
    ctx.restore();
  }
  
  /**
   * 创建进度渐变色
   */
  createProgressGradient(ctx, progress) {
    const gradient = ctx.createLinearGradient(this.x, this.y, this.x + this.width, this.y);
    
    // 根据进度决定颜色
    if (progress < 0.3) {
      // 低好感度 - 红色系
      gradient.addColorStop(0, '#FF6B6B');
      gradient.addColorStop(1, '#FF8E8E');
    } else if (progress < 0.6) {
      // 中等好感度 - 橙色系
      gradient.addColorStop(0, '#FF9500');
      gradient.addColorStop(1, '#FFAD33');
    } else if (progress < 0.8) {
      // 较高好感度 - 蓝色系
      gradient.addColorStop(0, '#007AFF');
      gradient.addColorStop(1, '#339CFF');
    } else {
      // 高好感度 - 金色系
      gradient.addColorStop(0, '#FFD700');
      gradient.addColorStop(1, '#FFED4A');
    }
    
    return gradient;
  }
  
  /**
   * 绘制边框
   */
  drawBorder(ctx) {
    this.drawRoundedRect(ctx, this.x, this.y, this.width, this.height, this.borderRadius);
    ctx.strokeStyle = this.borderColor;
    ctx.lineWidth = 1;
    ctx.stroke();
  }
  
  /**
   * 绘制文本
   */
  drawText(ctx) {
    const textY = this.y + this.height + 16;
    
    ctx.font = '12px Arial';
    ctx.fillStyle = this.textColor;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'top';
    
    let text;
    if (this.showPercentage) {
      const percentage = Math.round((this.animatedValue / this.maxValue) * 100);
      text = `${percentage}%`;
    } else {
      text = `${Math.round(this.animatedValue)}/${this.maxValue}`;
    }
    
    // 添加好感度等级描述
    const level = this.getFavorabilityLevel();
    if (level) {
      text += ` (${level})`;
    }
    
    ctx.fillText(text, this.x + this.width / 2, textY);
  }
  
  /**
   * 获取好感度等级描述
   */
  getFavorabilityLevel() {
    const progress = this.animatedValue / this.maxValue;
    
    if (progress < 0.2) return '陌生';
    if (progress < 0.4) return '认识';
    if (progress < 0.6) return '友好';
    if (progress < 0.8) return '亲密';
    if (progress < 0.95) return '挚友';
    return '心心相印';
  }
  
  /**
   * 绘制圆角矩形
   */
  drawRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();
  }
  
  /**
   * 设置当前值
   */
  setValue(value, animate = true) {
    const oldValue = this.currentValue;
    this.currentValue = Math.min(Math.max(value, 0), this.maxValue);
    
    if (animate && this.enableAnimation) {
      this.targetValue = this.currentValue;
    } else {
      this.targetValue = this.currentValue;
      this.animatedValue = this.currentValue;
    }
    
    if (this.onValueChange && oldValue !== this.currentValue) {
      this.onValueChange(this.currentValue, oldValue);
    }
  }
  
  /**
   * 增加值
   */
  addValue(amount, animate = true) {
    this.setValue(this.currentValue + amount, animate);
  }
  
  /**
   * 设置最大值
   */
  setMaxValue(maxValue) {
    this.maxValue = Math.max(maxValue, 1);
    // 确保当前值不超过最大值
    if (this.currentValue > this.maxValue) {
      this.setValue(this.maxValue);
    }
  }
  
  /**
   * 获取当前进度百分比
   */
  getProgress() {
    return this.currentValue / this.maxValue;
  }
  
  /**
   * 获取动画进度百分比
   */
  getAnimatedProgress() {
    return this.animatedValue / this.maxValue;
  }
  
  /**
   * 重置到0
   */
  reset(animate = true) {
    this.setValue(0, animate);
  }
  
  /**
   * 填满到最大值
   */
  fill(animate = true) {
    this.setValue(this.maxValue, animate);
  }
  
  /**
   * 设置位置
   */
  setPosition(x, y) {
    this.x = x;
    this.y = y;
  }
  
  /**
   * 设置尺寸
   */
  setSize(width, height) {
    this.width = width;
    this.height = height;
  }
  
  /**
   * 设置可见性
   */
  setVisible(visible) {
    this.visible = visible;
  }
  
  /**
   * 启用/禁用动画
   */
  setAnimationEnabled(enabled) {
    this.enableAnimation = enabled;
    if (!enabled) {
      this.animatedValue = this.targetValue;
    }
  }
  
  /**
   * 设置动画速度
   */
  setAnimationSpeed(speed) {
    this.animationSpeed = Math.min(Math.max(speed, 0.01), 1);
  }
  
  /**
   * 检查动画是否完成
   */
  isAnimationComplete() {
    return Math.abs(this.animatedValue - this.targetValue) < 0.1;
  }
}

export default FavorabilityBar; 