<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #2c3e50;
        }
        .sidebar .nav-link {
            color: #ecf0f1;
            border-radius: 5px;
            margin: 2px 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: #34495e;
            color: #fff;
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
            padding: 20px;
        }
        .stats-card {
            border-left: 4px solid #007bff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: none;
        }
        .stats-card.success {
            border-left-color: #28a745;
        }
        .stats-card.warning {
            border-left-color: #ffc107;
        }
        .stats-card.danger {
            border-left-color: #dc3545;
        }
        .stats-card.info {
            border-left-color: #17a2b8;
        }
        .stats-card.purple {
            border-left-color: #6f42c1;
        }
        .nav-section {
            margin-bottom: 20px;
        }
        .nav-section-title {
            color: #bdc3c7;
            font-size: 0.8rem;
            text-transform: uppercase;
            font-weight: bold;
            padding: 0 1rem;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <h5 class="text-white text-center mb-4">修仙后台</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item"><a class="nav-link <%= title === '数据总览' ? 'active' : '' %>" href="/"><i class="bi bi-speedometer2 me-2"></i>数据总览</a></li>
                        <li class="nav-item"><a class="nav-link <%= title === '数据统计' ? 'active' : '' %>" href="/dashboard"><i class="bi bi-graph-up me-2"></i>数据统计</a></li>
                        <li class="nav-item"><a class="nav-link <%= title === '玩家管理' ? 'active' : '' %>" href="/players"><i class="bi bi-people me-2"></i>玩家管理</a></li>
                        <li class="nav-item"><a class="nav-link <%= title === '玩家资源' ? 'active' : '' %>" href="/player-resources"><i class="bi bi-gem me-2"></i>玩家资源</a></li>
                        <li class="nav-item"><a class="nav-link <%= title === '装备技能' ? 'active' : '' %>" href="/player-equipment"><i class="bi bi-shield-check me-2"></i>装备技能</a></li>
                        <li class="nav-item"><a class="nav-link <%= title.includes('邮件') ? 'active' : '' %>" href="/mails"><i class="bi bi-envelope-fill me-2"></i>邮件管理</a></li>
                        <li class="nav-item"><a class="nav-link <%= title.includes('监控') || title.includes('日志') ? 'active' : '' %>" href="/logs"><i class="bi bi-clipboard-data me-2"></i>系统监控</a></li>
                        <li class="nav-item"><a class="nav-link <%= title.includes('批量操作') || title.includes('数据管理') ? 'active' : '' %>" href="/batch-operations"><i class="bi bi-gear-fill me-2"></i>批量操作</a></li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <div>
                        <h1 class="h2">系统概览</h1>
                        <p class="text-muted">修仙游戏后台管理系统总览</p>
                    </div>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="dropdown me-2">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-person-circle"></i> 
                                <span id="currentUsername"><%= user ? user.username : '管理员' %></span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><h6 class="dropdown-header">用户信息</h6></li>
                                <li><span class="dropdown-item-text small">
                                    角色: <span class="badge bg-primary"><%= user ? user.role : 'super_admin' %></span>
                                </span></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="changePassword()">
                                    <i class="bi bi-key"></i> 修改密码
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="logout()">
                                    <i class="bi bi-box-arrow-right"></i> 注销登录
                                </a></li>
                            </ul>
                        </div>
                        <button class="btn btn-outline-primary" onclick="refreshStats()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新数据
                        </button>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-xl-2 col-md-4 col-sm-6 mb-4">
                        <div class="card stats-card h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            总玩家数
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalPlayers">
                                            <div class="spinner-border spinner-border-sm" role="status"></div>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-people text-primary" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-2 col-md-4 col-sm-6 mb-4">
                        <div class="card stats-card success h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            功法模板
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="skillTemplates">
                                            <div class="spinner-border spinner-border-sm" role="status"></div>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-book text-success" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-2 col-md-4 col-sm-6 mb-4">
                        <div class="card stats-card info h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            剑心模板
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="swordHeartTemplates">
                                            <div class="spinner-border spinner-border-sm" role="status"></div>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-heart text-info" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-2 col-md-4 col-sm-6 mb-4">
                        <div class="card stats-card purple h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-purple text-uppercase mb-1">
                                            古宝模板
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="treasureTemplates">
                                            <div class="spinner-border spinner-border-sm" role="status"></div>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-gem" style="font-size: 2rem; color: #6f42c1;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-2 col-md-4 col-sm-6 mb-4">
                        <div class="card stats-card warning h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            抽取池
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="gachaPools">
                                            <div class="spinner-border spinner-border-sm" role="status"></div>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-dice-3 text-warning" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-2 col-md-4 col-sm-6 mb-4">
                        <div class="card stats-card danger h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                            邮件数量
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalMails">
                                            <div class="spinner-border spinner-border-sm" role="status"></div>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-envelope text-danger" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速操作和系统信息 -->
                <div class="row">
                    <div class="col-lg-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-lightning-charge"></i> 快速操作
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="/players" class="btn btn-primary">
                                        <i class="bi bi-people"></i> 管理玩家
                                    </a>
                                    <a href="/mails" class="btn btn-success">
                                        <i class="bi bi-envelope-plus"></i> 发送邮件
                                    </a>
                                    <a href="/skills" class="btn btn-info">
                                        <i class="bi bi-book"></i> 创建功法
                                    </a>
                                    <a href="/treasures" class="btn btn-warning">
                                        <i class="bi bi-gem"></i> 创建古宝
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-info-circle"></i> 系统信息
                                </h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li class="mb-2"><strong>系统版本:</strong> v2.0.0</li>
                                    <li class="mb-2"><strong>数据库:</strong> 微信云开发</li>
                                    <li class="mb-2"><strong>服务器时间:</strong> <span id="serverTime"></span></li>
                                    <li class="mb-2"><strong>运行状态:</strong> <span class="badge bg-success">正常</span></li>
                                    <li class="mb-2"><strong>最后更新:</strong> <span id="lastUpdate"></span></li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-graph-up"></i> 数据统计
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6 mb-3">
                                        <div class="border-end">
                                            <div class="h4 mb-0 text-primary" id="activeTemplates">-</div>
                                            <small class="text-muted">活跃模板</small>
                                        </div>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <div class="h4 mb-0 text-success" id="totalTemplates">-</div>
                                        <small class="text-muted">总模板数</small>
                                    </div>
                                    <div class="col-6">
                                        <div class="border-end">
                                            <div class="h4 mb-0 text-warning" id="todayCreated">-</div>
                                            <small class="text-muted">今日创建</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="h4 mb-0 text-info" id="systemHealth">100%</div>
                                        <small class="text-muted">系统健康</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载时获取统计数据
        document.addEventListener('DOMContentLoaded', function() {
            // 页面已经通过后端session验证，直接加载数据
            updateServerTime();
            setInterval(updateServerTime, 1000); // 每秒更新时间
            loadStats();
            setInterval(loadStats, 120000); // 每2分钟刷新统计数据
        });

        // 更新服务器时间
        function updateServerTime() {
            const now = new Date();
            document.getElementById('serverTime').textContent = now.toLocaleString('zh-CN');
            document.getElementById('lastUpdate').textContent = now.toLocaleString('zh-CN');
        }

        // 简单的fetch封装
        async function simpleFetch(url, options = {}) {
            try {
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    }
                });
                
                if (response.status === 401 || response.status === 403) {
                    // session过期，跳转到登录页
                    alert('登录已过期，请重新登录');
                    window.location.href = '/login';
                    return null;
                }
                
                return response;
            } catch (error) {
                console.error('请求失败:', error);
                throw error;
            }
        }

        // 注销登录
        async function logout() {
            if (!confirm('确定要注销登录吗？')) {
                return;
            }

            try {
                await simpleFetch('/api/auth/logout', { method: 'POST' });
            } catch (error) {
                console.error('注销请求失败:', error);
            } finally {
                window.location.href = '/login';
            }
        }

        // 加载统计数据
        async function loadStats() {
            try {
                const response = await simpleFetch('/api/stats');
                if (!response) return;
                
                const result = await response.json();
                
                if (result.success) {
                    const data = result.data;
                    
                    // 更新统计卡片
                    document.getElementById('totalPlayers').textContent = data.players || 0;
                    document.getElementById('skillTemplates').textContent = data.skill_templates || 0;
                    document.getElementById('swordHeartTemplates').textContent = data.sword_heart_templates || 0;
                    document.getElementById('treasureTemplates').textContent = data.treasure_templates || 0;
                    document.getElementById('gachaPools').textContent = data.gacha_pools || 0;
                    document.getElementById('totalMails').textContent = data.mails || 0;
                    
                    // 更新数据统计
                    const totalTemplates = (data.skill_templates || 0) + (data.sword_heart_templates || 0) + (data.treasure_templates || 0);
                    document.getElementById('activeTemplates').textContent = totalTemplates;
                    document.getElementById('totalTemplates').textContent = totalTemplates + (data.gacha_pools || 0);
                    document.getElementById('todayCreated').textContent = Math.floor(Math.random() * 10); // 模拟今日创建数
                } else {
                    console.error('获取统计数据失败:', result.error);
                    throw new Error(result.error);
                }
            } catch (error) {
                console.error('获取统计数据出错:', error);
                // 显示错误状态，但不影响页面功能
                ['totalPlayers', 'skillTemplates', 'swordHeartTemplates', 'treasureTemplates', 'gachaPools', 'totalMails'].forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.innerHTML = '<span class="text-danger" title="' + error.message + '">错误</span>';
                    }
                });
            }
        }

        // 手动刷新统计数据
        function refreshStats() {
            // 显示加载状态
            ['totalPlayers', 'skillTemplates', 'swordHeartTemplates', 'treasureTemplates', 'gachaPools', 'totalMails'].forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
                }
            });
            
            // 延迟一点时间再加载，给用户反馈
            setTimeout(loadStats, 500);
        }
    </script>
</body>
</html>
