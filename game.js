/**
 * 游戏主类
 * 整个游戏的入口点和管理类
 */

/**
 * 修仙六道 - 微信小游戏
 * 作者: AI进化论-花生
 * 版本: 1.0.0
 */

// 引入场景管理器
import SceneManager from './js/managers/SceneManager';
// 引入资源加载器
import ResourceLoader from './js/utils/ResourceLoader';
// 引入游戏状态管理器
import GameStateManager from './js/managers/GameStateManager';
// 引入UI管理器
import UIManager from './js/managers/UIManager';
import AppContext from './js/utils/AppContext.js';
import SkillManager from './js/models/SkillManager';

// 导入VIP系统
import VIPSystem from './js/models/VIPSystem';
// 导入洞府系统
import DongfuSystem from './js/models/DongfuSystem';
// 导入静室场景以便访问静态方法
import JingshiScene from './js/scenes/JingshiScene';
// 导入事件系统
import EventSystem from './js/utils/EventSystem';
// 导入登录管理器
import LoginManager from './js/managers/LoginManager';
// 导入自动保存管理器
import AutoSaveManager from './js/managers/AutoSaveManager';
// 导入古宝管理器
import TreasureManager from './js/managers/TreasureManager';
// 导入云数据库客户端
import CloudDBClient from './js/services/CloudDBClient';

// 将CloudDBClient添加到全局，供DatabaseManager使用
if (typeof window !== 'undefined') {
  window.CloudDBClient = CloudDBClient;
  console.log('CloudDBClient已设置到window对象:', typeof window.CloudDBClient);
} else {
  console.warn('window对象不存在，无法设置CloudDBClient到全局');
}

// 游戏主类
class Game {
  constructor() {
    AppContext.init(this);
    // 设置CloudDBClient到AppContext
    AppContext.setCloudDBClient(CloudDBClient);
    console.log('CloudDBClient已设置到AppContext:', typeof AppContext.CloudDBClient);

    // 初始化云开发相关成员变量
    this.cloud = null;
    this.db = null;
    this.user = null;
    this.cloudInited = false;
    this.userAuthorized = false;

    // 初始化画布
    this.initCanvas();

    // 初始化事件系统
    this.eventSystem = new EventSystem();

    // 初始化游戏状态管理器
    this.gameStateManager = new GameStateManager(this);

    // 将数据库管理器引用到game对象上，方便其他模块访问
    this.databaseManager = this.gameStateManager.databaseManager;

    // 初始化登录管理器
    this.loginManager = new LoginManager(this);

    // 初始化自动保存管理器
    this.autoSaveManager = new AutoSaveManager(this);

    // 初始化UI管理器
    this.uiManager = new UIManager(this.ctx, this.screenWidth, this.screenHeight);

    // 初始化VIP系统
    this.vipSystem = new VIPSystem();

    // 初始化洞府系统
    this.dongfuSystem = new DongfuSystem();

    // 初始化古宝管理器
    this.treasureManager = new TreasureManager();

    // 初始化资源加载器
    this.resourceLoader = new ResourceLoader();

    // 初始化云环境（异步）
    this.initCloud().catch(err => {
      console.error('云环境异步初始化失败:', err);
    });

    // 注册隐私权限相关监听
    this.initPrivacyListeners();

    // 注册游戏事件监听
    this.initEventListeners();

    // 注册游戏退出监听
    this.initExitListeners();

    // 加载资源
    this.loadResources();

    this.lastSyncedState = null; // 上次同步的状态
    this.maxRetries = 3; // 最大重试次数
    this.retryDelay = 5000; // 重试延迟（毫秒）
  }

  /**
   * 初始化游戏事件监听
   */
  initEventListeners() {
    // 监听战力增长事件
    this.eventSystem.on('powerIncrease', (data) => {
      console.log('战力增长事件触发:', data);
      // 在这里可以处理战力增长事件，例如显示动画或播放音效
    });
  }

  /**
   * 初始化游戏退出监听
   */
  initExitListeners() {
    if (typeof wx !== 'undefined') {
      // 监听小程序隐藏事件（用户切换到后台）
      wx.onHide(() => {
        console.log('游戏进入后台，保存离线时间和游戏数据');
        if (this.loginManager) {
          this.loginManager.saveOfflineTime();
        }
        // 执行退出保存
        if (this.autoSaveManager) {
          this.autoSaveManager.saveOnExit();
        }
      });

      // 监听小程序显示事件（用户从后台切换回来）
      wx.onShow(() => {
        console.log('游戏从后台恢复');
        // 这里可以添加恢复逻辑，比如重新计算离线收益
      });
    }
  }

  // 初始化画布
  initCanvas() {
    this.canvas = wx.createCanvas();
    this.ctx = this.canvas.getContext('2d');

    // 获取系统信息
    this.systemInfo = wx.getSystemInfoSync();
    this.screenWidth = this.systemInfo.windowWidth;
    this.screenHeight = this.systemInfo.windowHeight;

    // 设置画布尺寸
    this.canvas.width = this.screenWidth;
    this.canvas.height = this.screenHeight;
  }

  /**
   * 初始化微信云开发环境
   */
  async initCloud() {
    if (typeof wx !== 'undefined' && wx.cloud) {
      try {
        // 确保云环境ID正确配置
        const cloudEnv = 'cloud1-9gzbxxbff827656f';
        console.log('正在初始化云环境:', cloudEnv);
        
        // 检查用户登录状态
        await this.checkUserSession();
        
        wx.cloud.init({
          env: cloudEnv,
          traceUser: true
        });

        this.cloud = wx.cloud;
        this.db = wx.cloud.database();

        console.log('云环境初始化成功');
        this.cloudInited = true;

        // 验证云环境是否可用
        await this.validateCloudEnvironment();

      } catch (err) {
        console.error('云环境初始化失败:', err);
        this.cloudInited = false;
        // 不抛出错误，让游戏继续运行（离线模式）
      }
    } else {
      console.warn('当前环境不支持云开发');
      this.cloudInited = false;
    }
  }

  /**
   * 检查用户会话状态
   */
  async checkUserSession() {
    return new Promise((resolve) => {
      if (typeof wx !== 'undefined' && wx.checkSession) {
        wx.checkSession({
          success: () => {
            console.log('用户会话有效');
            resolve(true);
          },
          fail: () => {
            console.log('用户会话已过期，需要重新登录');
            // 清除本地存储的用户信息
            this.clearLocalUserInfo();
            resolve(false);
          }
        });
      } else {
        resolve(true); // 非微信环境或开发环境
      }
    });
  }

  /**
   * 清除本地用户信息
   */
  clearLocalUserInfo() {
    try {
      if (typeof wx !== 'undefined' && wx.removeStorageSync) {
        wx.removeStorageSync('userInfo');
        wx.removeStorageSync('openid');
        wx.removeStorageSync('gameState');
      }
      console.log('已清除本地用户信息');
    } catch (err) {
      console.error('清除本地用户信息失败:', err);
    }
  }

  /**
   * 验证云环境是否可用
   */
  async validateCloudEnvironment() {
    try {
      // 简单的云函数调用测试
      const testResult = await wx.cloud.callFunction({
        name: 'getOpenId',
        data: { test: true }
      });
      console.log('云环境验证成功:', testResult);
      return true;
    } catch (err) {
      console.warn('云环境验证失败，但不影响游戏运行:', err);
      return false;
    }
  }

  /**
   * 初始化隐私授权监听
   */
  initPrivacyListeners() {
    if (typeof wx !== 'undefined') {
      // 检查是否在开发环境
      const isDevEnv = this.isDevEnvironment();

      if (isDevEnv) {
        console.log('Game - 开发环境，跳过隐私授权检查');
        return;
      }

      // 1. 检查隐私授权状态
      wx.getPrivacySetting({
        success: res => {
          console.log('Game - 隐私设置状态:', res);
          if (res.needAuthorization) {
            console.log('Game - 需要隐私授权，准备创建授权弹窗');
            this.createPrivacyAuthDialog();
          } else {
            console.log('Game - 无需隐私授权');
          }
        },
        fail: err => {
          console.error('Game - 隐私检查失败', err);
          // 如果检查失败，在开发环境中忽略，生产环境中创建授权弹窗
          if (!isDevEnv) {
            console.log('Game - 隐私检查失败，创建授权弹窗作为备用');
            this.createPrivacyAuthDialog();
          }
        }
      });

      console.log('Game - 已注册隐私授权监听');
    }
  }

  /**
   * 检查是否在开发环境
   */
  isDevEnvironment() {
    // 检查多个开发环境标识
    const userAgent = navigator.userAgent || '';
    const isDevTool = userAgent.includes('wechatdevtools');
    const isLocalhost = location.hostname === 'localhost' || location.hostname === '127.0.0.1';
    const isFileProtocol = location.protocol === 'file:';

    return isDevTool || isLocalhost || isFileProtocol;
  }

  /**
   * 创建隐私授权弹窗（微信小游戏官方标准实现）
   */
  createPrivacyAuthDialog() {
    // 2. 注册隐私授权监听
    wx.onNeedPrivacyAuthorization(resolve => {
      console.log('隐私授权监听被触发');

      // 创建半透明背景
      const bg = wx.createUserInfoButton({
        type: 'text',
        text: '',
        style: {
          left: 0,
          top: 0,
          width: this.systemInfo.windowWidth,
          height: this.systemInfo.windowHeight,
          backgroundColor: 'rgba(0,0,0,0.5)'
        }
      });

      // 创建授权弹窗容器
      const dialog = wx.createUserInfoButton({
        type: 'text',
        text: '',
        style: {
          left: this.systemInfo.windowWidth * 0.1,
          top: this.systemInfo.windowHeight * 0.3,
          width: this.systemInfo.windowWidth * 0.8,
          height: this.systemInfo.windowHeight * 0.4,
          backgroundColor: '#fff',
          borderRadius: '10px'
        }
      });

      // 创建协议文本
      const text = wx.createUserInfoButton({
        type: 'text',
        text: '请阅读并同意《用户隐私协议》\n\n游戏需要获取您的微信昵称和头像以便存储游戏进度和提供个性化体验。',
        style: {
          left: this.systemInfo.windowWidth * 0.15,
          top: this.systemInfo.windowHeight * 0.35,
          width: this.systemInfo.windowWidth * 0.7,
          height: 80,
          textAlign: 'center',
          fontSize: 14,
          color: '#333',
          lineHeight: '20px'
        }
      });

      // 3. 创建同意按钮
      const agreeBtn = wx.createUserInfoButton({
        type: 'text',
        text: '同意',
        style: {
          left: this.systemInfo.windowWidth * 0.15,
          top: this.systemInfo.windowHeight * 0.55,
          width: this.systemInfo.windowWidth * 0.3,
          height: 40,
          backgroundColor: '#07C160',
          color: '#fff',
          textAlign: 'center',
          fontSize: 16,
          lineHeight: '40px',
          borderRadius: '5px'
        }
      });

      // 4. 创建拒绝按钮
      const rejectBtn = wx.createUserInfoButton({
        type: 'text',
        text: '拒绝',
        style: {
          left: this.systemInfo.windowWidth * 0.55,
          top: this.systemInfo.windowHeight * 0.55,
          width: this.systemInfo.windowWidth * 0.3,
          height: 40,
          backgroundColor: '#f2f2f2',
          color: '#333',
          textAlign: 'center',
          fontSize: 16,
          lineHeight: '40px',
          borderRadius: '5px'
        }
      });

      // 5. 按钮事件处理
      agreeBtn.onTap(() => {
        console.log('用户同意隐私授权');
        resolve({ event: 'agree', buttonId: 'agree-btn' });
        this.destroyAuthUI([bg, dialog, text, agreeBtn, rejectBtn]);
      });

      rejectBtn.onTap(() => {
        console.log('用户拒绝隐私授权');
        resolve({ event: 'disagree' });
        this.destroyAuthUI([bg, dialog, text, agreeBtn, rejectBtn]);

        // 拒绝后退出游戏
        wx.showModal({
          title: '提示',
          content: '拒绝隐私授权将无法正常使用游戏功能，游戏即将退出。',
          showCancel: false,
          success: () => {
            wx.exitMiniProgram();
          }
        });
      });
    });
  }

  /**
   * 销毁授权UI组件
   */
  destroyAuthUI(components) {
    components.forEach(comp => {
      try {
        comp.destroy();
      } catch (err) {
        console.warn('销毁UI组件失败:', err);
      }
    });
  }

  /**
   * 获取用户信息
   */
  getUserInfo() {
    if (!this.cloudInited) {
      console.warn('云环境未初始化，无法获取用户信息');
      return;
    }

    // 获取用户OpenID
    this.cloud.callFunction({
      name: 'login',
      success: res => {
        console.log('获取用户OpenID成功', res.result);
        const openid = res.result.openid;
        if (openid) {
          this.user = {
            openid: openid
          };

          // 查询用户是否已经在数据库中
          this.checkUserInDatabase(openid);
        }
      },
      fail: err => {
        console.error('获取用户OpenID失败', err);
      }
    });
  }

  /**
   * 检查用户是否在数据库中存在
   * @param {string} openid 用户的OpenID
   */
  checkUserInDatabase(openid) {
    this.db.collection('xiuxian').where({
      _openid: openid
    }).get({
      success: res => {
        if (res.data && res.data.length > 0) {
          console.log('用户数据已存在', res.data[0]);
          this.user = { ...this.user, ...res.data[0] };

          // 载入用户数据
          this.loadUserData(res.data[0]);
        } else {
          console.log('用户数据不存在，准备创建');
          // 弹出授权获取用户信息的按钮
          this.showUserInfoButton();
        }
      },
      fail: err => {
        console.error('查询用户数据失败', err);
      }
    });
  }


  /**
   * 创建用户数据
   */
  createUserData() {
    if (!this.user || !this.user.openid) {
      console.warn('用户信息不完整，无法创建用户数据');
      return;
    }

    // 获取当前游戏状态
    const gameState = this.gameStateManager.getGameState();

    // 创建用户数据
    const userData = {
      _openid: this.user.openid,
      nickName: this.user.nickName || '修仙者',
      avatarUrl: this.user.avatarUrl || '',
      gameState: gameState,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // 保存到数据库
    this.db.collection('xiuxian').add({
      data: userData,
      success: res => {
        console.log('创建用户数据成功', res);
        // 设置用户ID
        this.user._id = res._id;

        // 显示成功提示
        if (typeof wx !== 'undefined') {
          wx.showToast({
            title: '游戏数据已同步到云端',
            icon: 'success'
          });
        }
      },
      fail: err => {
        console.error('创建用户数据失败', err);
      }
    });
  }

  /**
   * 更新用户数据
   */
  updateUserData(retryCount = 0) {
    const currentState = this.gameStateManager.gameState;

    // 计算增量数据
    const changedData = this.calculateStateChanges(this.lastSyncedState, currentState);

    if (Object.keys(changedData).length === 0) {
      console.log('没有数据需要同步');
      return;
    }

    this.cloud.callFunction({
      name: 'updateUserData',
      data: {
        gameState: changedData,
        isPartialUpdate: true
      },
      success: res => {
        console.log('通过云函数更新用户数据成功', res);
        this.lastSyncedState = JSON.parse(JSON.stringify(currentState));
      },
      fail: err => {
        console.error('通过云函数更新用户数据失败', err);

        if (retryCount < this.maxRetries) {
          console.log(`${retryCount + 1}秒后重试...`);
          setTimeout(() => {
            this.updateUserData(retryCount + 1);
          }, this.retryDelay);
        } else {
          // 所有重试都失败后，尝试本地数据库
          this.fallbackToLocalStorage(changedData);
        }
      }
    });
  }

  // 计算状态变化
  calculateStateChanges(oldState, newState) {
    if (!oldState) return newState;

    const changes = {};

    // 比较并记录变化的数据
    for (const key in newState) {
      if (JSON.stringify(newState[key]) !== JSON.stringify(oldState[key])) {
        changes[key] = newState[key];
      }
    }

    return changes;
  }

  // 本地存储备份
  fallbackToLocalStorage(data) {
    console.log('尝试本地数据库操作作为备选方案');
    this.db.collection('xiuxian').doc(this.user._id).update({
      data: {
        gameState: data,
        updatedAt: new Date(),
        pendingSync: true // 标记需要同步
      },
      success: res => {
        console.log('通过本地方式更新用户数据成功', res);
      },
      fail: err => {
        console.error('本地数据更新也失败了', err);
        // 存储到待同步队列
        this.storePendingSync(data);
      }
    });
  }

  // 存储待同步数据
  storePendingSync(data) {
    let pendingSyncs = wx.getStorageSync('pendingSyncs') || [];
    pendingSyncs.push({
      data: data,
      timestamp: Date.now()
    });
    wx.setStorageSync('pendingSyncs', pendingSyncs);
  }

  /**
   * 载入用户数据
   * @param {Object} userData 用户数据
   */
  loadUserData(userData) {
    if (!userData || !userData.gameState) {
      console.warn('用户数据不完整，无法载入');
      return;
    }

    try {
      // 载入游戏状态
      this.gameStateManager.loadGameState(userData.gameState);
      console.log('载入用户数据成功');

      // 显示成功提示
      if (typeof wx !== 'undefined') {
        wx.showToast({
          title: '已从云端同步游戏数据',
          icon: 'success'
        });
      }
    } catch (err) {
      console.error('载入用户数据失败', err);
    }
  }

  // 加载资源
  loadResources() {
    // 资源列表
    const resources = {
      // 背景图
      'mainBg': 'assets/images/main_bg.jpg',
      'dongfuBg': 'assets/images/dongfu_bg.jpg',
      // 角色图
      'character1': 'assets/images/character1.jpg',
      'character2': 'assets/images/character2.jpg',
      'character3': 'assets/images/character3.jpg',
      // UI元素
      'btnNormal': 'assets/images/btn_normal.jpg',
      'btnPressed': 'assets/images/btn_pressed.jpg',
      'iconXianyu': 'assets/images/icon_xianyu.jpg',
      'iconLingshi': 'assets/images/icon_lingshi.jpg',
      'avatarFrame': 'assets/images/avatar_frame.jpg',
      'equipFrame': 'assets/images/equip_frame.jpg',
      'tabBar': 'assets/images/tab_bar.jpg',
      'tabIcon1': 'assets/images/tab_icon1.jpg',
      'tabIcon2': 'assets/images/tab_icon2.jpg',
      'tabIcon3': 'assets/images/tab_icon3.jpg',
      'tabIcon4': 'assets/images/tab_icon4.jpg',
      'tabIcon5': 'assets/images/tab_icon5.jpg',
    };

    // 显示加载界面
    this.uiManager.showLoading('正在加载游戏资源...');

    // 加载资源
    this.resourceLoader.loadResources(resources, (progress) => {
      // 更新加载进度
      this.uiManager.updateLoadingProgress(progress);
    }, () => {
      // 资源加载完成后初始化场景管理器并开始游戏
      this.startGame();
    });
  }

  // 开始游戏
  startGame() {
    // 隐藏加载界面
    this.uiManager.hideLoading();

    // 初始化场景管理器并传递画布上下文
    this.sceneManager = new SceneManager(this.ctx, this.screenWidth, this.screenHeight, this.uiManager);

    // 初始化触摸事件
    this.initTouchEvents();

    // 初始化功法管理器
    this.initSkillManager();

    // 计算玩家战力
    this.calculatePlayerPower();

    // 切换到首页场景
    this.sceneManager.showScene('index');

    // 开始游戏循环
    this.startGameLoop();

    // 初始化登录流程（在游戏完全加载后）
    this.initializeLoginFlow();
  }

  /**
   * 初始化登录流程
   */
  async initializeLoginFlow() {
    try {
      console.log('游戏加载完成，开始初始化登录流程');
      await this.loginManager.initializeLogin();
    } catch (error) {
      console.error('初始化登录流程失败:', error);
    }
  }

  // 计算玩家战力
  calculatePlayerPower() {
    try {
      // 直接使用玩家战力计算器
      const characters = this.gameStateManager.getCharacters();

      // 找到主角色（ID为1的角色）
      const mainCharacter = characters.find(char => char.id === 1);

      // 如果没有主角色，返回0战力
      if (!mainCharacter) {
        console.warn('无法计算玩家战力，主角色未找到');
        return { totalPower: 0, contributingCharacters: [] };
      }

      // 主角色战力
      const mainCharacterPower = mainCharacter.power || 0;

      // 将主角色添加到贡献战力的角色列表
      const contributingCharacters = [{
        id: mainCharacter.id,
        name: mainCharacter.name,
        power: mainCharacterPower,
        isMain: true
      }];

      // 获取除主角色外的所有角色
      const otherCharacters = characters.filter(char => char.id !== 1);

      // 按战力排序（从高到低）
      otherCharacters.sort((a, b) => (b.power || 0) - (a.power || 0));

      // 取前5名最强角色
      const topCharacters = otherCharacters.slice(0, 5);

      // 计算这些角色的总战力
      let otherCharactersPower = 0;

      // 添加这些角色到贡献战力的角色列表
      topCharacters.forEach(char => {
        const charPower = char.power || 0;
        otherCharactersPower += charPower;

        contributingCharacters.push({
          id: char.id,
          name: char.name,
          power: charPower,
          isMain: false
        });
      });

      // 计算总战力
      const totalPower = mainCharacterPower + otherCharactersPower;

      // 获取玩家数据
      const player = this.gameStateManager.getPlayer();

      if (player) {
        // 获取旧战力
        const oldPower = player.power || 0;

        // 更新玩家战力
        player.power = totalPower;
        player.powerContributors = contributingCharacters;

        // 保存玩家数据
        this.gameStateManager.setPlayer(player);

        // 检查战力是否变化
        const changed = oldPower !== totalPower;

        // 如果战力变化且有事件系统，触发战力增长事件
        if (changed && this.eventSystem && totalPower > oldPower) {
          this.eventSystem.emit('playerPowerIncrease', {
            oldPower,
            newPower: totalPower,
            increase: totalPower - oldPower,
            contributors: contributingCharacters
          });
        }

        console.log(`玩家战力计算完成: ${totalPower}`);

        return {
          oldPower,
          newPower: totalPower,
          changed
        };
      }

      return { totalPower, contributingCharacters };
    } catch (error) {
      console.error('计算玩家战力失败:', error);
      return { totalPower: 0, contributingCharacters: [] };
    }
  }

  /**
   * 开启游戏数据自动同步
   */
  startAutoSync() {
    // 检查登录状态
    if (!this.loginManager || !this.loginManager.isLoggedIn) {
      console.log('用户未登录，不启动自动同步');
      return;
    }

    if (!this.cloudInited) {
      console.log('云环境未初始化，不启动自动同步');
      return;
    }

    // 每隔5分钟自动同步一次游戏数据
    this.autoSyncInterval = setInterval(() => {
      this.updateUserData();
    }, 5 * 60 * 1000); // 5分钟

    // 监听游戏状态变化事件，在重要数据变更时同步
    this.gameStateManager.on('playerDataChanged', () => {
      this.updateUserData();
    });

    this.gameStateManager.on('characterChanged', () => {
      this.updateUserData();
    });

    this.gameStateManager.on('inventoryChanged', () => {
      this.updateUserData();
    });

    this.gameStateManager.on('skillChanged', () => {
      this.updateUserData();
    });

    // 监听布阵变更事件，确保布阵数据同步到云端
    this.gameStateManager.on('formationChanged', () => {
      console.log('布阵数据已变更，同步到云端');
      this.updateUserData();
    });

    console.log('已启动游戏数据自动同步');
  }

  // 初始化触摸事件
  initTouchEvents() {
    const getCanvasPos = (clientX, clientY) => {
      // 微信小游戏中获取画布位置的简化方法
      return {
        x: clientX,
        y: clientY
      };
    };

    // touchstart 事件处理
    const handleTouchStart = (e) => {
      if (e.touches && e.touches[0]) {
        const pos = getCanvasPos(e.touches[0].clientX, e.touches[0].clientY);
        console.log('Touch start:', pos.x, pos.y);
        this.sceneManager.onTouchStart(pos.x, pos.y);
      }
    };

    // touchmove 事件处理
    const handleTouchMove = (e) => {
      if (e.touches && e.touches[0]) {
        const pos = getCanvasPos(e.touches[0].clientX, e.touches[0].clientY);
        this.sceneManager.onTouchMove(pos.x, pos.y);
      }
    };

    // touchend 事件处理
    const handleTouchEnd = (e) => {
      // 微信 touchend 使用 changedTouches 获取最后坐标
      const touch = e.changedTouches ? e.changedTouches[0] : null;
      if (touch) {
        const pos = getCanvasPos(touch.clientX, touch.clientY);
        console.log('Touch end:', pos.x, pos.y);
        this.sceneManager.onTouchEnd(pos.x, pos.y);
      } else {
        console.log('Touch end without coordinates');
        this.sceneManager.onTouchEnd();
      }
    };

    // 绑定事件
    wx.onTouchStart(handleTouchStart);
    wx.onTouchMove(handleTouchMove);
    wx.onTouchEnd(handleTouchEnd);

    console.log('Touch events initialized');
  }

  // 开始游戏循环
  startGameLoop() {
    const targetFPS = 60;
    const frameInterval = 1000 / targetFPS;
    let lastFrameTime = 0;

    const loop = (timestamp) => {
      // 计算帧间隔
      const deltaTime = timestamp - lastFrameTime;

      // 如果距离上一帧的时间小于目标帧间隔，跳过这一帧
      if (deltaTime >= frameInterval) {
        // 清空画布
        this.ctx.clearRect(0, 0, this.screenWidth, this.screenHeight);

        // 更新当前场景
        this.sceneManager.update(deltaTime);

        // 渲染当前场景
        this.sceneManager.render();

        // 检查和更新静室修炼进度
        // 只有在登录完成后才进行修炼更新，避免在登录过程中触发数据库操作
        if (typeof JingshiScene !== 'undefined' && this.loginManager && this.loginManager.isLoginComplete()) {
          // 如果修炼未启动，则启动修炼
          if (!JingshiScene.meditationStarted) {
            JingshiScene.meditationStarted = true;
            JingshiScene.lastRotationTime = Date.now();
            console.log('自动修炼已启动（游戏循环中）');
          }
          // 检查和更新修炼进度
          JingshiScene.checkAndUpdateMeditation();
        }

        lastFrameTime = timestamp;
      }

      // 请求下一帧
      requestAnimationFrame(loop);
    };

    // 开始游戏循环
    requestAnimationFrame(loop);
  }

  /**
   * 初始化功法管理器
   */
  initSkillManager() {
    try {
      console.log('初始化功法管理器');

      // 检查SkillManager是否存在
      if (typeof SkillManager !== 'function') {
        console.error('SkillManager不是一个构造函数:', SkillManager);
        return;
      }

      // 确保游戏状态存在
      if (!this.gameStateManager || !this.gameStateManager.gameState) {
        console.log('游戏状态不存在，初始化游戏状态');
        if (this.gameStateManager) {
          this.gameStateManager.initGameState();
        } else {
          console.error('游戏状态管理器不存在');
          return;
        }
      }

      // 创建功法管理器实例
      this.skillManager = new SkillManager();

      // 从游戏状态加载数据
      if (this.gameStateManager && this.gameStateManager.gameState) {
        console.log('从游戏状态加载功法数据');
        this.skillManager.loadFromGameState(this.gameStateManager.gameState);
      } else {
        console.warn('游戏状态仍然不存在，使用默认功法配置');
      }

      // 确保初始角色拥有基础功法
      this.ensureCharactersHaveBasicSkill();

      console.log('功法管理器初始化完成，功法总数:', Object.keys(this.skillManager.skills).length);
    } catch (error) {
      console.error('初始化功法管理器失败:', error);
      console.error('错误详情:', error.stack);
    }
  }

  /**
   * 确保角色拥有基础功法
   */
  ensureCharactersHaveBasicSkill() {
    if (!this.gameStateManager || !this.skillManager) return;

    const characters = this.gameStateManager.getCharacters();
    if (!characters || characters.length === 0) {
      console.warn('没有找到角色，无法添加基础功法');
      return;
    }

    // 获取基础练气决
    const basicSkill = this.skillManager.getSkillById('basic_qi_skill');
    if (!basicSkill) {
      console.error('找不到基础练气决功法');
      return;
    }

    console.log('确保角色拥有基础功法');

    // 确保每个角色都拥有基础功法
    characters.forEach(character => {
      // 检查角色是否已有基础功法
      const hasBasicSkill = character.skills && character.skills.some(skill =>
        (skill && skill.id === 'basic_qi_skill') ||
        (skill && typeof skill === 'object' && skill.id === 'basic_qi_skill')
      );

      if (!hasBasicSkill) {
        console.log(`为角色 ${character.name} 添加基础练气决功法`);
        // 创建新的功法实例
        const newSkill = this.skillManager.createSkill('basic_qi_skill');
        if (newSkill) {
          character.addSkill(newSkill);
          console.log(`成功为角色 ${character.name} 添加基础练气决功法`);

          // 如果角色没有装备功法，自动装备基础功法
          if (!character.equippedSkill) {
            const equipped = character.equipSkill('basic_qi_skill');
            console.log(`为角色 ${character.name} 装备基础练气决功法 ${equipped ? '成功' : '失败'}`);
          }
        } else {
          console.error(`无法创建基础练气决功法实例`);
        }
      } else {
        console.log(`角色 ${character.name} 已拥有基础练气决功法`);
      }
    });

    // 保存游戏状态
    this.gameStateManager.saveGameState();
    console.log('保存游戏状态完成');
  }
}

// 创建游戏实例
const game = new Game();

// 导出游戏实例
if (typeof GameGlobal !== 'undefined') {
  GameGlobal.Game = game;
}
if (typeof global !== 'undefined') {
  global.Game = game;
}
export default game;



