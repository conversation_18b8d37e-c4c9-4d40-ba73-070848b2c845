/**
 * 审计日志记录中间件
 * 记录所有管理员操作到数据库，用于安全审计和操作追踪
 */

const CloudFunctionAdapter = require('../lib/CloudFunctionAdapter');

class AuditLogger {
  constructor() {
    this.cloudAdapter = new CloudFunctionAdapter({
      enableLogging: false // 避免审计日志产生过多输出
    });
    
    // 需要记录的操作类型
    this.AUDIT_ACTIONS = {
      LOGIN: 'login',
      LOGOUT: 'logout', 
      CREATE: 'create',
      READ: 'read',
      UPDATE: 'update',
      DELETE: 'delete',
      BATCH_UPDATE: 'batch_update',
      BATCH_DELETE: 'batch_delete',
      EXPORT: 'export',
      IMPORT: 'import'
    };
    
    // 敏感操作列表
    this.SENSITIVE_OPERATIONS = [
      'DELETE',
      'BATCH_DELETE', 
      'BATCH_UPDATE',
      'UPDATE'
    ];
  }

  /**
   * 记录审计日志
   * @param {Object} logData 日志数据
   */
  async recordAuditLog(logData) {
    try {
      const auditRecord = {
        ...logData,
        timestamp: new Date(),
        created_at: new Date()
      };
      
      // 尝试调用云函数记录日志
      await this.cloudAdapter.create('admin_logs', auditRecord);
      
      // 敏感操作额外输出到控制台
      if (this.SENSITIVE_OPERATIONS.includes(logData.action)) {
        console.warn(`⚠️ 敏感操作记录:`, {
          admin: logData.admin_id,
          action: logData.action,
          target: logData.target_type,
          ip: logData.ip_address,
          timestamp: auditRecord.timestamp
        });
      }
      
    } catch (error) {
      // 审计日志记录失败，使用本地备份，但不影响主要功能
      console.warn('⚠️ 云端审计日志记录失败，使用本地备份:', error.message);
      
      // 将日志写入本地文件作为备份
      this.saveToLocalBackup(logData, error);
      
      // 不要抛出错误，避免影响主要业务流程
    }
  }

  /**
   * 处理审计日志记录失败的情况
   * @param {Error} error 错误对象
   * @param {Object} logData 失败的日志数据
   */
  handleAuditFailure(error, logData) {
    // 将失败的日志写入本地文件作为备份
    const fs = require('fs');
    const path = require('path');
    
    try {
      const failureLog = {
        timestamp: new Date().toISOString(),
        error: error.message,
        originalLogData: logData
      };
      
      const logDir = path.join(__dirname, '../logs');
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }
      
      const logFile = path.join(logDir, `audit_failure_${new Date().toISOString().split('T')[0]}.log`);
      fs.appendFileSync(logFile, JSON.stringify(failureLog) + '\n');
      
    } catch (fileError) {
      console.error('💥 连本地日志备份都失败了:', fileError);
    }
  }

  /**
   * 从请求中提取关键信息
   * @param {Object} req Express请求对象
   * @returns {Object} 提取的信息
   */
  extractRequestInfo(req) {
    return {
      ip_address: req.ip || req.connection?.remoteAddress || 'unknown',
      user_agent: req.get('User-Agent') || '',
      request_id: req.get('X-Request-ID') || this.generateRequestId(),
      session_id: req.session?.id || '',
      method: req.method,
      url: req.originalUrl || req.url,
      query: JSON.stringify(req.query),
      params: JSON.stringify(req.params)
    };
  }

  /**
   * 生成请求ID
   * @returns {string} 唯一请求ID
   */
  generateRequestId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * 创建审计日志中间件
   * @param {string} action 操作类型
   * @param {string} targetType 目标类型
   * @returns {Function} Express中间件函数
   */
  createAuditMiddleware(action, targetType) {
    return async (req, res, next) => {
      const requestInfo = this.extractRequestInfo(req);
      const startTime = Date.now();
      
      // 记录请求开始
      const logData = {
        admin_id: req.user?.userId || 'anonymous',
        admin_username: req.user?.username || 'anonymous',
        action: action,
        target_type: targetType,
        target_id: req.params.id || '',
        before_data: null,
        after_data: null,
        status: 'started',
        duration_ms: 0,
        description: `${action} ${targetType}`,
        ...requestInfo
      };

      // 保存原始响应方法
      const originalJson = res.json;
      
      // 重写响应方法以捕获结果
      res.json = function(data) {
        const duration = Date.now() - startTime;
        
        // 更新日志数据
        logData.duration_ms = duration;
        logData.status = data.success ? 'success' : 'error';
        logData.after_data = data.success ? (data.data || null) : null;
        logData.error_message = data.success ? null : (data.error || 'Unknown error');
        
        // 异步记录日志，不阻塞响应
        setImmediate(() => {
          auditLogger.recordAuditLog(logData);
        });
        
        // 调用原始响应方法
        return originalJson.call(this, data);
      };

      next();
    };
  }

  /**
   * 批量操作审计中间件
   * @param {string} action 操作类型
   * @param {string} targetType 目标类型
   * @returns {Function} Express中间件函数
   */
  createBatchAuditMiddleware(action, targetType) {
    return async (req, res, next) => {
      const requestInfo = this.extractRequestInfo(req);
      
      // 批量操作需要特别记录
      const logData = {
        admin_id: req.user?.userId || 'anonymous',
        admin_username: req.user?.username || 'anonymous',
        action: `BATCH_${action}`,
        target_type: targetType,
        target_id: 'multiple',
        batch_size: req.body?.ids?.length || 0,
        batch_data: JSON.stringify(req.body),
        description: `批量${action} ${targetType}`,
        ...requestInfo
      };

      // 立即记录批量操作开始
      await this.recordAuditLog({
        ...logData,
        status: 'started'
      });

      // 监听响应完成
      res.on('finish', async () => {
        await this.recordAuditLog({
          ...logData,
          status: res.statusCode < 400 ? 'success' : 'error',
          response_status: res.statusCode
        });
      });

      next();
    };
  }

  /**
   * 登录审计中间件
   * @returns {Function} Express中间件函数
   */
  createLoginAuditMiddleware() {
    return async (req, res, next) => {
      const requestInfo = this.extractRequestInfo(req);
      
      // 保存原始响应方法
      const originalJson = res.json;
      
      res.json = function(data) {
        const logData = {
          admin_id: data.success ? (data.user?.id || 'unknown') : 'failed',
          admin_username: req.body?.username || 'unknown',
          action: 'LOGIN',
          target_type: 'auth',
          target_id: '',
          status: data.success ? 'success' : 'error',
          error_message: data.success ? null : (data.error || 'Login failed'),
          description: data.success ? '管理员登录成功' : '管理员登录失败',
          ...requestInfo
        };
        
        // 异步记录审计日志，不阻塞登录响应
        setImmediate(async () => {
          try {
            await auditLogger.recordAuditLog(logData);
          } catch (error) {
            // 审计日志失败不应该影响登录功能
            console.warn('⚠️ 登录审计日志记录失败（已忽略）:', error.message);
          }
        });
        
        // 调用原始响应方法
        return originalJson.call(this, data);
      };

      next();
    };
  }

  /**
   * 查询审计日志
   * @param {Object} filters 查询条件
   * @param {Object} pagination 分页参数
   * @returns {Object} 查询结果
   */
  async queryAuditLogs(filters = {}, pagination = {}) {
    try {
      const { page = 1, limit = 50 } = pagination;
      
      // 构建查询条件
      const conditions = {};
      
      if (filters.admin_id) {
        conditions.admin_id = filters.admin_id;
      }
      
      if (filters.action) {
        conditions.action = filters.action;
      }
      
      if (filters.start_date || filters.end_date) {
        conditions.timestamp = {};
        if (filters.start_date) {
          conditions.timestamp.$gte = new Date(filters.start_date);
        }
        if (filters.end_date) {
          conditions.timestamp.$lte = new Date(filters.end_date);
        }
      }

      const result = await this.cloudAdapter.list('admin_logs', {
        conditions,
        page,
        limit,
        sort: { timestamp: -1 } // 按时间倒序
      });

      return result;
      
    } catch (error) {
      console.error('查询审计日志失败:', error);
      throw error;
    }
  }

  /**
   * 将日志写入本地备份
   * @param {Object} logData 日志数据
   * @param {Error} error 错误对象
   */
  saveToLocalBackup(logData, error) {
    // 将日志写入本地文件作为备份
    const fs = require('fs');
    const path = require('path');
    
    try {
      const backupLog = {
        timestamp: new Date().toISOString(),
        error: error.message,
        originalLogData: logData
      };
      
      const logDir = path.join(__dirname, '../logs');
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }
      
      const logFile = path.join(logDir, `audit_failure_${new Date().toISOString().split('T')[0]}.log`);
      fs.appendFileSync(logFile, JSON.stringify(backupLog) + '\n');
      
    } catch (fileError) {
      console.error('💥 连本地日志备份都失败了:', fileError);
    }
  }
}

// 创建全局审计记录器实例
const auditLogger = new AuditLogger();

module.exports = {
  AuditLogger,
  auditLogger
}; 