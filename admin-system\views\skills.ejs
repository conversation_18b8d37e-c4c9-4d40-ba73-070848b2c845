<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #2c3e50;
        }
        .sidebar .nav-link {
            color: #ecf0f1;
            border-radius: 5px;
            margin: 2px 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: #34495e;
            color: #fff;
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: none;
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
        }
        .rarity-badge {
            font-size: 0.8em;
        }
        .rarity-1 { background-color: #6c757d; }
        .rarity-2 { background-color: #28a745; }
        .rarity-3 { background-color: #007bff; }
        .rarity-4 { background-color: #6f42c1; }
        .rarity-5 { background-color: #ffc107; color: #212529; }
        .json-editor {
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        .attribute-input {
            margin-bottom: 10px;
        }
        .attribute-group {
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">修仙游戏管理后台</h5>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/">
                                <i class="bi bi-house-door"></i> 首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/players">
                                <i class="bi bi-people"></i> 玩家管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/mails">
                                <i class="bi bi-envelope"></i> 邮件管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/skills">
                                <i class="bi bi-book"></i> 功法模板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/sword-hearts">
                                <i class="bi bi-heart"></i> 剑心模板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/treasures">
                                <i class="bi bi-gem"></i> 古宝模板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/gacha-pools">
                                <i class="bi bi-dice-3"></i> 抽取池
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <div>
                        <h1 class="h2">功法模板管理</h1>
                        <p class="text-muted">管理游戏中的所有功法模板，包括创建、编辑和删除功能</p>
                    </div>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#skillModal" onclick="openCreateModal()">
                            <i class="bi bi-plus-circle"></i> 创建功法
                        </button>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <input type="text" class="form-control" id="searchInput" placeholder="搜索功法名称...">
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="realmFilter">
                                    <option value="">所有境界</option>
                                    <option value="练气期">练气期</option>
                                    <option value="筑基期">筑基期</option>
                                    <option value="金丹期">金丹期</option>
                                    <option value="元婴期">元婴期</option>
                                    <option value="化神期">化神期</option>
                                    <option value="返虚期">返虚期</option>
                                    <option value="合道期">合道期</option>
                                    <option value="渡劫期">渡劫期</option>
                                    <option value="大乘期">大乘期</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="rarityFilter">
                                    <option value="">所有稀有度</option>
                                    <option value="1">1星</option>
                                    <option value="2">2星</option>
                                    <option value="3">3星</option>
                                    <option value="4">4星</option>
                                    <option value="5">5星</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-outline-primary" onclick="searchSkills()">
                                    <i class="bi bi-search"></i> 搜索
                                </button>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-outline-secondary" onclick="resetFilters()">
                                    <i class="bi bi-arrow-clockwise"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功法列表 -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>功法ID</th>
                                        <th>名称</th>
                                        <th>分类</th>
                                        <th>境界</th>
                                        <th>类型</th>
                                        <th>稀有度</th>
                                        <th>最大等级</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="skillsTableBody">
                                    <!-- 数据将通过JavaScript加载 -->
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页 -->
                        <nav aria-label="功法列表分页">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页将通过JavaScript生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 功法编辑模态框 -->
    <div class="modal fade" id="skillModal" tabindex="-1" aria-labelledby="skillModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="skillModalLabel">创建功法模板</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="skillForm">
                        <div class="row">
                            <!-- 基本信息 -->
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">基本信息</h6>
                                <div class="mb-3">
                                    <label for="skillId" class="form-label">功法ID <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="skillId" required>
                                    <div class="form-text">唯一标识符，不可重复</div>
                                </div>
                                <div class="mb-3">
                                    <label for="skillName" class="form-label">功法名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="skillName" required>
                                </div>
                                <div class="mb-3">
                                    <label for="skillCategory" class="form-label">功法分类 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="skillCategory" required>
                                    <div class="form-text">如：攻击类、防御类、辅助类等</div>
                                </div>
                                <div class="mb-3">
                                    <label for="skillRealm" class="form-label">所属境界 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="skillRealm" required>
                                        <option value="">请选择境界</option>
                                        <option value="练气期">练气期</option>
                                        <option value="筑基期">筑基期</option>
                                        <option value="金丹期">金丹期</option>
                                        <option value="元婴期">元婴期</option>
                                        <option value="化神期">化神期</option>
                                        <option value="返虚期">返虚期</option>
                                        <option value="合道期">合道期</option>
                                        <option value="渡劫期">渡劫期</option>
                                        <option value="大乘期">大乘期</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="skillType" class="form-label">功法类型</label>
                                    <select class="form-select" id="skillType">
                                        <option value="passive">被动</option>
                                        <option value="active">主动</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="skillRarity" class="form-label">稀有度</label>
                                    <select class="form-select" id="skillRarity">
                                        <option value="1">1星</option>
                                        <option value="2">2星</option>
                                        <option value="3">3星</option>
                                        <option value="4">4星</option>
                                        <option value="5">5星</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="skillMaxLevel" class="form-label">最大等级</label>
                                    <input type="number" class="form-control" id="skillMaxLevel" value="10" min="1" max="100">
                                </div>
                            </div>

                            <!-- 属性配置 -->
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">属性配置</h6>
                                
                                <!-- 基础属性 -->
                                <div class="attribute-group">
                                    <h6>基础属性</h6>
                                    <div id="baseAttributesContainer">
                                        <div class="attribute-input">
                                            <div class="row">
                                                <div class="col-5">
                                                    <select class="form-select attribute-type">
                                                        <option value="hp">生命值</option>
                                                        <option value="attack">攻击力</option>
                                                        <option value="defense">防御力</option>
                                                        <option value="speed">速度</option>
                                                        <option value="crit">暴击率</option>
                                                        <option value="crit_damage">暴击伤害</option>
                                                        <option value="dao_rule">大道法则</option>
                                                        <option value="penetration">破防</option>
                                                    </select>
                                                </div>
                                                <div class="col-5">
                                                    <input type="number" class="form-control attribute-value" placeholder="数值">
                                                </div>
                                                <div class="col-2">
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeAttribute(this)">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="addAttribute('base')">
                                        <i class="bi bi-plus"></i> 添加基础属性
                                    </button>
                                </div>

                                <!-- 等级成长 -->
                                <div class="attribute-group">
                                    <h6>等级成长</h6>
                                    <div id="levelGrowthContainer">
                                        <div class="attribute-input">
                                            <div class="row">
                                                <div class="col-5">
                                                    <select class="form-select attribute-type">
                                                        <option value="hp">生命值</option>
                                                        <option value="attack">攻击力</option>
                                                        <option value="defense">防御力</option>
                                                        <option value="speed">速度</option>
                                                        <option value="crit">暴击率</option>
                                                        <option value="crit_damage">暴击伤害</option>
                                                        <option value="dao_rule">大道法则</option>
                                                        <option value="penetration">破防</option>
                                                    </select>
                                                </div>
                                                <div class="col-5">
                                                    <input type="number" class="form-control attribute-value" placeholder="每级成长">
                                                </div>
                                                <div class="col-2">
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeAttribute(this)">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="addAttribute('growth')">
                                        <i class="bi bi-plus"></i> 添加成长属性
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="skillDescription" class="form-label">功法描述</label>
                                    <textarea class="form-control" id="skillDescription" rows="3"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="skillIcon" class="form-label">图标路径</label>
                                    <input type="text" class="form-control" id="skillIcon" placeholder="如：icons/skills/skill_name.png">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveSkill()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentPage = 1;
        let currentSkillId = null;
        let isEditMode = false;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadSkills();
            
            // 绑定搜索事件
            document.getElementById('searchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchSkills();
                }
            });
        });

        // 加载功法列表
        async function loadSkills(page = 1) {
            try {
                const search = document.getElementById('searchInput').value;
                const realm = document.getElementById('realmFilter').value;
                const rarity = document.getElementById('rarityFilter').value;
                
                const params = new URLSearchParams({
                    page: page,
                    limit: 20
                });
                
                if (search) params.append('search', search);
                if (realm) params.append('realm', realm);
                if (rarity) params.append('rarity', rarity);
                
                const response = await fetch(`/api/skill-templates?${params}`);
                const result = await response.json();
                
                if (result.success) {
                    displaySkills(result.data);
                    displayPagination(result.page, Math.ceil(result.total / result.limit), result.total);
                    currentPage = result.page;
                } else {
                    console.error('加载功法列表失败:', result.error);
                    alert('加载功法列表失败: ' + result.error);
                }
            } catch (error) {
                console.error('加载功法列表出错:', error);
                alert('加载功法列表出错: ' + error.message);
            }
        }

        // 显示功法列表
        function displaySkills(skills) {
            const tbody = document.getElementById('skillsTableBody');
            tbody.innerHTML = '';
            
            skills.forEach(skill => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><code>${skill.skill_id}</code></td>
                    <td><strong>${skill.name}</strong></td>
                    <td><span class="badge bg-info">${skill.category}</span></td>
                    <td><span class="badge bg-secondary">${skill.realm}</span></td>
                    <td><span class="badge bg-${skill.type === 'active' ? 'warning' : 'success'}">${skill.type === 'active' ? '主动' : '被动'}</span></td>
                    <td><span class="badge rarity-${skill.rarity} rarity-badge">${skill.rarity}星</span></td>
                    <td>${skill.max_level}</td>
                    <td>${new Date(skill.created_at).toLocaleDateString()}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary me-1" onclick="editSkill('${skill._id}')">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteSkill('${skill._id}', '${skill.name}')">
                            <i class="bi bi-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 显示分页
        function displayPagination(current, total, count) {
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';
            
            // 显示总数
            const info = document.createElement('li');
            info.className = 'page-item disabled';
            info.innerHTML = `<span class="page-link">共 ${count} 条记录</span>`;
            pagination.appendChild(info);
            
            // 上一页
            if (current > 1) {
                const prev = document.createElement('li');
                prev.className = 'page-item';
                prev.innerHTML = `<a class="page-link" href="#" onclick="loadSkills(${current - 1})">上一页</a>`;
                pagination.appendChild(prev);
            }
            
            // 页码
            const start = Math.max(1, current - 2);
            const end = Math.min(total, current + 2);
            
            for (let i = start; i <= end; i++) {
                const item = document.createElement('li');
                item.className = `page-item ${i === current ? 'active' : ''}`;
                item.innerHTML = `<a class="page-link" href="#" onclick="loadSkills(${i})">${i}</a>`;
                pagination.appendChild(item);
            }
            
            // 下一页
            if (current < total) {
                const next = document.createElement('li');
                next.className = 'page-item';
                next.innerHTML = `<a class="page-link" href="#" onclick="loadSkills(${current + 1})">下一页</a>`;
                pagination.appendChild(next);
            }
        }

        // 搜索功法
        function searchSkills() {
            currentPage = 1;
            loadSkills(1);
        }

        // 重置筛选
        function resetFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('realmFilter').value = '';
            document.getElementById('rarityFilter').value = '';
            currentPage = 1;
            loadSkills(1);
        }

        // 打开创建模态框
        function openCreateModal() {
            isEditMode = false;
            currentSkillId = null;
            document.getElementById('skillModalLabel').textContent = '创建功法模板';
            document.getElementById('skillForm').reset();
            clearAttributes();
            addAttribute('base');
            addAttribute('growth');
        }

        // 编辑功法
        async function editSkill(skillId) {
            try {
                const response = await fetch(`/api/skill-templates?search=${skillId}`);
                const result = await response.json();
                
                if (result.success && result.data.length > 0) {
                    const skill = result.data[0];
                    isEditMode = true;
                    currentSkillId = skillId;
                    
                    document.getElementById('skillModalLabel').textContent = '编辑功法模板';
                    document.getElementById('skillId').value = skill.skill_id;
                    document.getElementById('skillName').value = skill.name;
                    document.getElementById('skillCategory').value = skill.category;
                    document.getElementById('skillRealm').value = skill.realm;
                    document.getElementById('skillType').value = skill.type;
                    document.getElementById('skillRarity').value = skill.rarity;
                    document.getElementById('skillMaxLevel').value = skill.max_level;
                    document.getElementById('skillDescription').value = skill.description || '';
                    document.getElementById('skillIcon').value = skill.icon || '';
                    
                    // 加载属性
                    loadAttributes(skill.base_attributes, 'base');
                    loadAttributes(skill.level_growth, 'growth');
                    
                    // 显示模态框
                    const modal = new bootstrap.Modal(document.getElementById('skillModal'));
                    modal.show();
                } else {
                    alert('获取功法信息失败');
                }
            } catch (error) {
                console.error('编辑功法出错:', error);
                alert('编辑功法出错: ' + error.message);
            }
        }

        // 删除功法
        async function deleteSkill(skillId, skillName) {
            if (!confirm(`确定要删除功法 "${skillName}" 吗？此操作不可撤销。`)) {
                return;
            }
            
            try {
                const response = await fetch(`/api/skill-templates/${skillId}`, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('功法删除成功');
                    loadSkills(currentPage);
                } else {
                    alert('删除失败: ' + result.error);
                }
            } catch (error) {
                console.error('删除功法出错:', error);
                alert('删除功法出错: ' + error.message);
            }
        }

        // 保存功法
        async function saveSkill() {
            try {
                const formData = {
                    skill_id: document.getElementById('skillId').value,
                    name: document.getElementById('skillName').value,
                    category: document.getElementById('skillCategory').value,
                    realm: document.getElementById('skillRealm').value,
                    type: document.getElementById('skillType').value,
                    rarity: parseInt(document.getElementById('skillRarity').value),
                    max_level: parseInt(document.getElementById('skillMaxLevel').value),
                    description: document.getElementById('skillDescription').value,
                    icon: document.getElementById('skillIcon').value,
                    base_attributes: getAttributes('base'),
                    level_growth: getAttributes('growth')
                };
                
                const url = isEditMode ? `/api/skill-templates/${currentSkillId}` : '/api/skill-templates';
                const method = isEditMode ? 'PUT' : 'POST';
                
                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert(isEditMode ? '功法更新成功' : '功法创建成功');
                    const modal = bootstrap.Modal.getInstance(document.getElementById('skillModal'));
                    modal.hide();
                    loadSkills(currentPage);
                } else {
                    alert('保存失败: ' + result.error);
                }
            } catch (error) {
                console.error('保存功法出错:', error);
                alert('保存功法出错: ' + error.message);
            }
        }

        // 添加属性输入框
        function addAttribute(type) {
            const container = document.getElementById(type === 'base' ? 'baseAttributesContainer' : 'levelGrowthContainer');
            const div = document.createElement('div');
            div.className = 'attribute-input';
            div.innerHTML = `
                <div class="row">
                    <div class="col-5">
                        <select class="form-select attribute-type">
                            <option value="hp">生命值</option>
                            <option value="attack">攻击力</option>
                            <option value="defense">防御力</option>
                            <option value="speed">速度</option>
                            <option value="crit">暴击率</option>
                            <option value="crit_damage">暴击伤害</option>
                            <option value="dao_rule">大道法则</option>
                            <option value="penetration">破防</option>
                        </select>
                    </div>
                    <div class="col-5">
                        <input type="number" class="form-control attribute-value" placeholder="${type === 'base' ? '数值' : '每级成长'}">
                    </div>
                    <div class="col-2">
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeAttribute(this)">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            `;
            container.appendChild(div);
        }

        // 移除属性输入框
        function removeAttribute(button) {
            button.closest('.attribute-input').remove();
        }

        // 清空属性
        function clearAttributes() {
            document.getElementById('baseAttributesContainer').innerHTML = '';
            document.getElementById('levelGrowthContainer').innerHTML = '';
        }

        // 获取属性数据
        function getAttributes(type) {
            const container = document.getElementById(type === 'base' ? 'baseAttributesContainer' : 'levelGrowthContainer');
            const attributes = {};
            
            container.querySelectorAll('.attribute-input').forEach(input => {
                const typeSelect = input.querySelector('.attribute-type');
                const valueInput = input.querySelector('.attribute-value');
                
                if (typeSelect.value && valueInput.value) {
                    attributes[typeSelect.value] = parseFloat(valueInput.value);
                }
            });
            
            return attributes;
        }

        // 加载属性数据到界面
        function loadAttributes(attributes, type) {
            const container = document.getElementById(type === 'base' ? 'baseAttributesContainer' : 'levelGrowthContainer');
            container.innerHTML = '';
            
            Object.entries(attributes || {}).forEach(([key, value]) => {
                addAttribute(type);
                const lastInput = container.lastElementChild;
                lastInput.querySelector('.attribute-type').value = key;
                lastInput.querySelector('.attribute-value').value = value;
            });
            
            // 如果没有属性，至少添加一个空的输入框
            if (Object.keys(attributes || {}).length === 0) {
                addAttribute(type);
            }
        }
    </script>
</body>
</html> 