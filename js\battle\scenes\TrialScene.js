/**
 * 试炼场景
 * 处理玩家参与的各种试炼挑战
 */
import BaseScene from '../../scenes/BaseScene';
import Button from '../../ui/Button';
import game from '../../../game';

class TrialScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager) {
    super(ctx, screenWidth, screenHeight, sceneManager);
    
    // 场景资源
    this.resources = null;
    
    // 当前选择的试炼关卡
    this.selectedStage = null;
    
    // UI元素
    this.backButton = null;
    this.stageButtons = [];
    
    // 初始化UI
    this.initUI();
  }
  
  /**
   * 场景显示回调
   * @param {Object} params 参数
   */
  onShow(params) {
    console.log('试炼场景显示', params);
    
    // 清空UI元素
    this.clearUIElements();
    
    // 重新加载资源
    this.resources = game.resourceLoader.resources;
    
    // 初始化UI
    this.initUI();
    
    // 重置选中状态
    this.selectedStage = null;
    
    // 设置场景为可见
    this.visible = true;
  }
  
  /**
   * 初始化UI
   */
  initUI() {
    // 初始化返回按钮
    this.initBackButton();
    
    // 初始化关卡按钮
    this.initStageButtons();
  }
  
  /**
   * 初始化返回按钮
   */
  initBackButton() {
    const buttonWidth = 80;
    const buttonHeight = 40;
    const margin = 10;
    
    this.backButton = new Button(
      this.ctx,
      margin,
      margin,
      buttonWidth,
      buttonHeight,
      '返回',
      null,
      null,
      () => {
        this.sceneManager.showScene('main');
      }
    );
    
    this.addUIElement(this.backButton);
  }
  
  /**
   * 初始化关卡按钮
   */
  initStageButtons() {
    // 简单的试炼关卡列表
    const stages = [
      { id: 'stage1', name: '初级试炼', level: 1, enemies: [{ id: 1001, name: '测试敌人', hp: 100, attack: 10, defense: 5, speed: 5 }] },
      { id: 'stage2', name: '中级试炼', level: 5, enemies: [{ id: 1002, name: '精英敌人', hp: 200, attack: 15, defense: 10, speed: 7 }] },
      { id: 'stage3', name: '高级试炼', level: 10, enemies: [{ id: 1003, name: '首领敌人', hp: 500, attack: 25, defense: 20, speed: 10 }] }
    ];
    
    const buttonWidth = 180;
    const buttonHeight = 60;
    const margin = 20;
    const startY = 100;
    
    stages.forEach((stage, index) => {
      const button = new Button(
        this.ctx,
        (this.screenWidth - buttonWidth) / 2,
        startY + index * (buttonHeight + margin),
        buttonWidth,
        buttonHeight,
        `${stage.name} (Lv.${stage.level})`,
        null,
        null,
        () => {
          this.onStageSelected(stage);
        }
      );
      
      this.stageButtons.push(button);
      this.addUIElement(button);
    });
  }
  
  /**
   * 关卡选择处理
   * @param {Object} stage 选中的关卡
   */
  onStageSelected(stage) {
    console.log('选择关卡:', stage);
    
    this.selectedStage = stage;
    
    // 弹出确认对话框
    wx.showModal({
      title: '开始挑战',
      content: `确定要挑战${stage.name}吗？`,
      success: res => {
        if (res.confirm) {
          this.startBattle(stage);
        }
      }
    });
  }
  
  /**
   * 开始战斗
   * @param {Object} stage 关卡数据
   */
  startBattle(stage) {
    console.log('开始挑战关卡:', stage);
    
    // 获取玩家角色
    const player = game.gameStateManager.getPlayer();
    if (!player) {
      wx.showToast({
        title: '没有可用角色',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    // 创建用于战斗的玩家单位
    const playerUnit = {
      id: 1,
      name: player.name || '张青云',
      hp: player.hp || 220,
      maxHp: player.maxHp || 220,
      attack: player.attack || 20,
      defense: player.defense || 10,
      speed: player.speed || 10
    };
    
    // 获取敌人单位
    const enemyUnits = stage.enemies.map(enemy => ({
      ...enemy,
      maxHp: enemy.hp
    }));
    
    // 跳转到战斗场景
    this.sceneManager.showScene('battle', {
      playerUnits: [playerUnit],
      enemyUnits: enemyUnits,
      onComplete: (result, rewards) => {
        this.handleBattleResult(result, rewards);
      }
    });
  }
  
  /**
   * 处理战斗结果
   * @param {string} result 战斗结果
   * @param {Object} rewards 奖励
   */
  handleBattleResult(result, rewards) {
    console.log('试炼战斗结果:', result, '奖励:', rewards);
    
    if (result === 'victory') {
      // 如果胜利，发放奖励
      rewards = rewards || {
        exp: Math.floor(30 + Math.random() * 20),
        lingshi: Math.floor(50 + Math.random() * 50)
      };
      
      // 使用giveReward云函数发放奖励
      this.sendRewardsToPlayer(rewards);
      
      // 显示胜利提示
      wx.showToast({
        title: '挑战成功！',
        icon: 'success',
        duration: 2000,
        complete: () => {
          setTimeout(() => {
            // 返回试炼场景
            if (this.sceneManager) {
              this.sceneManager.showScene('trial');
            }
          }, 2000);
        }
      });
    } else if (result === 'defeat') {
      // 如果失败，显示失败信息并返回试炼场景
      wx.showToast({
        title: '挑战失败',
        icon: 'none',
        duration: 2000,
        complete: () => {
          setTimeout(() => {
            // 返回试炼场景
            if (this.sceneManager) {
              this.sceneManager.showScene('trial');
            }
          }, 2000);
        }
      });
    } else {
      // 其他情况，返回试炼场景
      wx.showToast({
        title: '战斗结束',
        icon: 'none',
        duration: 2000,
        complete: () => {
          setTimeout(() => {
            // 返回试炼场景
            if (this.sceneManager) {
              this.sceneManager.showScene('trial');
            }
          }, 2000);
        }
      });
    }
  }
  
  /**
   * 发送奖励到玩家账号
   * @param {Object} rewards 奖励数据
   */
  sendRewardsToPlayer(rewards) {
    if (!rewards) {
      console.warn('奖励数据为空');
      return;
    }
    
    // 检查云环境是否初始化
    if (!game.cloudInited) {
      console.error('云环境未初始化，无法发放奖励');
      
      // 尝试使用本地方式添加奖励
      this.addRewardsLocally(rewards);
      return;
    }
    
    // 获取玩家OPENID
    const openid = game.user?.openid;
    if (!openid) {
      console.error('未找到玩家OPENID，无法发放奖励');
      
      // 尝试使用本地方式添加奖励
      this.addRewardsLocally(rewards);
      return;
    }
    
    console.log(`尝试使用云函数给玩家 ${openid} 发放奖励:`, rewards);
    
    // 调用云函数发放奖励
    wx.showLoading({
      title: '发放奖励中...',
      mask: true
    });
    
    game.cloud.callFunction({
      name: 'giveReward',
      data: {
        targetOpenid: openid,
        rewards: rewards,
        reason: '试炼胜利'
      },
      success: res => {
        wx.hideLoading();
        console.log('奖励发放成功:', res);
        
        if (res.result && res.result.success) {
          wx.showToast({
            title: '奖励已发放',
            icon: 'success',
            duration: 2000
          });
        } else {
          console.error('云函数返回失败:', res);
          // 尝试使用本地方式添加奖励
          this.addRewardsLocally(rewards);
        }
      },
      fail: err => {
        wx.hideLoading();
        console.error('调用云函数发放奖励失败:', err);
        
        // 显示错误信息
        wx.showToast({
          title: '奖励发放失败',
          icon: 'none',
          duration: 2000
        });
        
        // 尝试使用本地方式添加奖励
        this.addRewardsLocally(rewards);
      }
    });
  }
  
  /**
   * 本地添加奖励（当云函数失败时的备选方案）
   * @param {Object} rewards 奖励数据
   */
  addRewardsLocally(rewards) {
    console.log('尝试本地添加奖励:', rewards);
    
    try {
      // 添加灵石
      if (rewards.lingshi) {
        game.gameStateManager.addResources({ lingshi: rewards.lingshi });
      }
      
      // 添加仙玉
      if (rewards.xianyu) {
        game.gameStateManager.addResources({ xianyu: rewards.xianyu });
      }
      
      // 添加经验值
      if (rewards.exp) {
        const player = game.gameStateManager.getPlayer();
        if (player) {
          player.exp = (player.exp || 0) + rewards.exp;
          game.gameStateManager.setPlayer(player);
        }
      }
      
      // 添加物品
      if (rewards.items && Array.isArray(rewards.items)) {
        rewards.items.forEach(item => {
          game.gameStateManager.addItem(item);
        });
      }
      
      // 添加装备
      if (rewards.equipments && Array.isArray(rewards.equipments)) {
        rewards.equipments.forEach(equipment => {
          game.gameStateManager.addEquipment(equipment);
        });
      }
      
      // 保存状态
      game.gameStateManager.saveGameState();
      
      console.log('本地添加奖励成功');
      wx.showToast({
        title: '奖励已添加',
        icon: 'success',
        duration: 2000
      });
    } catch (error) {
      console.error('本地添加奖励失败:', error);
      wx.showToast({
        title: '添加奖励失败',
        icon: 'none',
        duration: 2000
      });
    }
  }
  
  /**
   * 绘制场景
   */
  drawScene() {
    // 绘制背景
    this.drawBackground();
    
    // 绘制标题
    this.drawTitle();
    
    // 绘制关卡描述
    if (this.selectedStage) {
      this.drawStageDescription(this.selectedStage);
    }
  }
  
  /**
   * 绘制背景
   */
  drawBackground() {
    // 绘制背景图
    if (this.resources && this.resources.trialBg) {
      this.ctx.drawImage(
        this.resources.trialBg,
        0,
        0,
        this.screenWidth,
        this.screenHeight
      );
    } else {
      // 绘制默认背景
      const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
      gradient.addColorStop(0, '#000066');
      gradient.addColorStop(1, '#003366');
      
      this.ctx.fillStyle = gradient;
      this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
    }
  }
  
  /**
   * 绘制标题
   */
  drawTitle() {
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'top';
    this.ctx.fillText('修仙试炼', this.screenWidth / 2, 40);
  }
  
  /**
   * 绘制关卡描述
   * @param {Object} stage 关卡数据
   */
  drawStageDescription(stage) {
    const startY = 300;
    
    // 绘制关卡名称
    this.ctx.font = 'bold 18px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'top';
    this.ctx.fillText(`${stage.name} (Lv.${stage.level})`, this.screenWidth / 2, startY);
    
    // 绘制敌人信息
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#cccccc';
    this.ctx.textAlign = 'center';
    
    stage.enemies.forEach((enemy, index) => {
      this.ctx.fillText(`${enemy.name} - HP: ${enemy.hp} ATK: ${enemy.attack}`, 
        this.screenWidth / 2, 
        startY + 30 + index * 24);
    });
  }
}

export default TrialScene; 