/**
 * 主线关卡场景
 * 提供章节选择和关卡挑战功能
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import { STORY_CHAPTERS, getStageEnemies, getStageRewards } from '../config/StoryConfig';
import game from '../../game';

class StoryScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager, resources) {
    super(ctx, screenWidth, screenHeight, sceneManager);

    // 场景资源
    this.resources = resources || {};

    // 当前选中的底部导航项
    this.selectedTabIndex = 0; // 主页对应的导航索引

    // 主线关卡配置 - 使用新的配置系统
    this.chapters = STORY_CHAPTERS.map(config => ({
      id: config.id,
      name: config.name,
      description: config.description,
      environment: config.environment,
      bgColor: config.bgColor,
      levels: Array.from({ length: 10 }, (_, levelIndex) => {
        return {
          id: levelIndex + 1,
          name: `第${levelIndex + 1}层`,
          isBoss: levelIndex === 9, // 第10层是Boss
          enemies: getStageEnemies(config.id, levelIndex + 1),
          reward: getStageRewards(config.id, levelIndex + 1)
        };
      })
    }));

    // 当前选中的章节
    this.currentChapter = null;

    // 返回按钮
    this.backButton = null;

    // 是否正在显示关卡列表（第二层）
    this.showingLevels = false;
  }

  // 初始化UI
  initUI() {
    // 清空UI元素
    this.clearUIElements();

    // 创建返回按钮
    this.createBackButton();

    if (this.showingLevels && this.currentChapter) {
      // 显示关卡列表
      const chapter = this.chapters.find(c => c.id === this.currentChapter);
      if (chapter) {
        this.createLevelButtons(chapter);
      }
    } else {
      // 显示章节列表
      this.createChapterButtons();
    }
  }

  // 创建返回按钮
  createBackButton() {
    const buttonWidth = 80;
    const buttonHeight = 40;
    const margin = 10;

    this.backButton = new Button(
      this.ctx,
      margin,
      margin,
      buttonWidth,
      buttonHeight,
      '返回',
      null,
      null,
      () => {
        console.log('点击返回按钮');
        if (this.showingLevels) {
          // 如果正在显示关卡列表，返回到章节列表
          this.showingLevels = false;
          this.currentChapter = null;
          this.clearUIElements();
          this.initUI();
        } else {
          // 否则返回到主页面，携带from参数，指示来源场景
          this.sceneManager.showScene('main', { from: 'story' });
        }
      }
    );

    this.addUIElement(this.backButton);
  }

  // 创建章节按钮
  createChapterButtons() {
    const headerHeight = 80;
    const topMargin = 140;
    const chapterButtonWidth = this.screenWidth * 0.85;
    const chapterButtonHeight = 60;
    const chapterMargin = 15;

    // 清除之前的按钮
    if (this.chapterButtons) {
      this.chapterButtons.forEach(button => {
        this.removeUIElement(button);
      });
    }

    if (this.levelButtons) {
      this.levelButtons.forEach(button => {
        this.removeUIElement(button);
      });
      this.levelButtons = [];
    }

    // 创建章节按钮
    this.chapterButtons = [];
    for (let i = 0; i < this.chapters.length; i++) {
      const chapter = this.chapters[i];

      // 获取该章节最高已通关层数，确定按钮是否可用
      const progress = this.getChapterProgress(chapter.id);
      // 第一关始终解锁，其他关卡需要前一关通关所有层数
      const isUnlocked = i === 0 || (i > 0 && this.getChapterProgress(i) >= 10);

      // 创建按钮文本
      let buttonText = `${chapter.name}`;
      if (progress > 0) {
        buttonText += ` (${progress}/10)`;
      }
      if (!isUnlocked) {
        buttonText += ' 🔒';
      }

      const button = new Button(
        this.ctx,
        (this.screenWidth - chapterButtonWidth) / 2,
        topMargin + i * (chapterButtonHeight + chapterMargin),
        chapterButtonWidth,
        chapterButtonHeight,
        buttonText,
        null,
        isUnlocked ? null : 'rgba(100, 100, 100, 0.7)',
        () => {
          if (isUnlocked) {
            // 选中章节，显示关卡列表
            this.selectChapter(chapter.id);
          } else {
            wx.showToast({
              title: '请先通关前一关卡',
              icon: 'none',
              duration: 2000
            });
          }
        }
      );

      this.chapterButtons.push(button);
      this.addUIElement(button);
    }
  }

  // 选择章节，显示关卡列表
  selectChapter(chapterId) {
    this.currentChapter = chapterId;
    this.showingLevels = true;

    // 清空UI元素并重新初始化，显示关卡列表
    this.clearUIElements();
    this.initUI();
  }

  // 创建指定章节的关卡层数按钮
  createLevelButtons(chapter) {
    if (!chapter) return;

    const headerHeight = 80;
    const levelStartY = 140;
    const levelButtonWidth = this.screenWidth * 0.85;
    const levelButtonHeight = 55;
    const levelMargin = 12;

    this.levelButtons = [];

    // 获取当前章节最高已通关层数
    const chapterProgress = this.getChapterProgress(chapter.id);

    for (let i = 0; i < chapter.levels.length; i++) {
      const level = chapter.levels[i];
      // 第一层始终解锁，其他层数需要前一层通关
      const isUnlocked = i === 0 || i < chapterProgress;

      // 创建按钮文本
      let buttonText = `${level.name}`;
      if (level.isBoss) {
        buttonText += ' 👑'; // Boss标识
      }
      
      // 显示奖励信息
      const rewards = level.reward;
      buttonText += ` (灵石:${rewards.lingshi} 经验:${rewards.exp}`;
      if (rewards.xianyu > 0) {
        buttonText += ` 仙玉:${rewards.xianyu}`;
      }
      buttonText += ')';

      if (!isUnlocked) {
        buttonText += ' 🔒';
      }

      const button = new Button(
        this.ctx,
        (this.screenWidth - levelButtonWidth) / 2,
        levelStartY + i * (levelButtonHeight + levelMargin),
        levelButtonWidth,
        levelButtonHeight,
        buttonText,
        null,
        isUnlocked ? (level.isBoss ? 'rgba(255, 215, 0, 0.3)' : null) : 'rgba(100, 100, 100, 0.7)',
        () => {
          if (isUnlocked) {
            this.startLevel(chapter.id, level.id);
          } else {
            wx.showToast({
              title: '请先通关前一层',
              icon: 'none',
              duration: 2000
            });
          }
        }
      );

      this.levelButtons.push(button);
      this.addUIElement(button);
    }
  }

  // 底部导航栏点击事件
  onTabSelected(index) {
    this.selectedTabIndex = index;
    
    // 根据选中的导航项切换场景
    switch (index) {
      case 0: // 主页
        this.sceneManager.showScene('main');
        break;
      case 1: // 角色
        this.sceneManager.showScene('characterDetail', { characterId: 1 });
        break;
      case 2: // 洞府
        this.sceneManager.showScene('dongfu');
        break;
      case 3: // 试炼
        this.sceneManager.showScene('trial');
        break;
      case 4: // 背包
        this.sceneManager.showScene('backpack');
        break;
    }
  }

  // 获取章节进度
  getChapterProgress(chapterId) {
    if (!game.gameStateManager) return 0;
    
    const player = game.gameStateManager.getPlayer();
    if (!player) return 0;

    // 确保存在storyProgress对象
    if (!player.storyProgress) {
      player.storyProgress = {};
    }

    // 返回章节进度，未设置则返回0
    return player.storyProgress[`chapter_${chapterId}`] || 0;
  }

  // 开始关卡
  startLevel(chapterId, levelId) {
    const chapter = this.chapters.find(c => c.id === chapterId);
    if (!chapter) {
      console.error('章节未找到:', chapterId);
      return;
    }

    const level = chapter.levels.find(l => l.id === levelId);
    if (!level) {
      console.error('关卡未找到:', levelId);
      return;
    }

    console.log(`开始关卡: 章节${chapterId} 关卡${levelId}`);

    // 创建战斗角色数据
    const playerCharacter = this.createPlayerCharacterForBattle();
    const enemyCharacter = this.createEnemyCharacterForBattle(level);

    // 创建地点配置（适配IdleBattleScene格式）
    const baseEnemy = {
      name: level.enemyName || '敌人',
      level: level.level || 1,
      hpBase: level.enemyHp || 100,
      attackBase: level.enemyAttack || 10,
      defenseBase: level.enemyDefense || 5,
      attackSpeed: level.enemyAttackSpeed || 10
    };

    const locationConfig = {
      id: `story_${chapter.id}_${level.id}`,
      name: level.name,
      level: level.level || 1,
      description: `${chapter.name} - ${level.name}`,

      // IdleBattleScene期望的敌人配置格式
      normalEnemy: baseEnemy,
      eliteEnemy: {
        name: `精英${baseEnemy.name}`,
        hpMultiplier: 10,
        attackMultiplier: 2,
        defenseMultiplier: 1.5,
        attackSpeed: baseEnemy.attackSpeed * 1.2
      },
      bossEnemy: {
        name: `Boss${baseEnemy.name}`,
        hpMultiplier: 100,
        attackMultiplier: 5,
        defenseMultiplier: 3,
        attackSpeed: baseEnemy.attackSpeed * 1.5
      },

      rewards: level.rewards || {}
    };

    // 使用IdleBattleScene战斗系统
    this.sceneManager.showScene('idleBattle', {
      playerCharacter: playerCharacter,
      locationConfig: locationConfig,
      storyMode: true, // 标记为主线关卡模式，使用30秒限制
      stageData: level,
      chapter: chapter,
      level: level,
      onComplete: (result) => {
        this.handleStoryBattleComplete(result, chapter, level);
      }
    });
  }

  /**
   * 为战斗创建敌人角色对象
   * @param {Object} level 关卡数据
   * @returns {Object} 敌人角色对象
   */
  createEnemyCharacterForBattle(level) {
    if (!level.enemies || level.enemies.length === 0) {
      console.error('关卡敌人数据为空');
      return this.createDefaultEnemyCharacter();
    }

    // 使用第一个敌人作为战斗对象
    const enemy = level.enemies[0];
    
    return {
      id: enemy.id,
      name: enemy.name,
      level: enemy.level,
      
      // 战斗属性
      hp: enemy.hp,
      maxHp: enemy.maxHp,
      attack: enemy.attack,
      defense: enemy.defense,
      attackSpeed: (enemy.speed || 50) / 50.0, // 将速度转换为攻击速度倍率
      critical: enemy.critical || 0.05,
      criticalDamage: enemy.criticalDamage || 1.5,
      
      // 技能
      skills: enemy.skills || [],
      
      // 状态
      isAlive: true,
      type: 'enemy',
      isBoss: enemy.isBoss || false
    };
  }

  /**
   * 创建默认敌人角色对象（备用）
   */
  createDefaultEnemyCharacter() {
    return {
      id: 'default_enemy',
      name: '神秘敌人',
      level: 1,
      hp: 800,
      maxHp: 800,
      attack: 80,
      defense: 40,
      attackSpeed: 1.0,
      critical: 0.05,
      criticalDamage: 1.5,
      skills: [],
      isAlive: true,
      type: 'enemy',
      isBoss: false
    };
  }

  /**
   * 为战斗创建玩家角色对象
   */
  createPlayerCharacterForBattle() {
    if (!game.gameStateManager) {
      console.error('游戏状态管理器未初始化');
      return this.createDefaultPlayerCharacter();
    }

    const player = game.gameStateManager.getPlayer();
    const characters = game.gameStateManager.getCharacters();
    const mainCharacter = characters.find(char => char.id === 1);

    if (!mainCharacter) {
      console.warn('主角色未找到，使用默认角色');
      return this.createDefaultPlayerCharacter();
    }

    // 获取角色属性
    const attributes = mainCharacter.getAttributes ? mainCharacter.getAttributes() : {
      hp: 1000,
      attack: 100,
      defense: 50,
      speed: 50
    };

    const battleCharacter = {
      id: mainCharacter.id || 'player_1',
      name: mainCharacter.name || player.nickname || '修仙者',
      level: mainCharacter.level || 1,

      // 战斗属性
      hp: attributes.hp || 1000,
      maxHp: attributes.hp || 1000,
      attack: attributes.attack || 100,
      defense: attributes.defense || 50,
      attackSpeed: 1.0, // 基础攻击速度
      critical: 0.05, // 基础暴击率
      criticalDamage: 1.5, // 基础暴击伤害

      // 技能
      skills: this.getPlayerSkills(mainCharacter),

      // 状态
      isAlive: true,
      type: 'player',

      // 添加getAttributes方法以兼容IdleBattleScene
      getAttributes: function() {
        return {
          hp: this.hp,
          maxHp: this.maxHp,
          attack: this.attack,
          defense: this.defense,
          attackSpeed: this.attackSpeed,
          critical: this.critical,
          criticalDamage: this.criticalDamage
        };
      }
    };

    return battleCharacter;
  }

  /**
   * 获取玩家技能列表
   * @param {Object} character 角色对象
   * @returns {Array} 技能列表
   */
  getPlayerSkills(character) {
    const skills = [];
    
    // 添加基础攻击技能
    skills.push({
      id: 'basic_attack',
      name: '基础攻击',
      type: 'damage',
      cooldown: 3000, // 3秒冷却
      castTime: 500, // 0.5秒吟唱
      damage: 120,
      damageMultiplier: 1.2,
      description: '基础攻击技能'
    });

    // 添加治疗技能
    skills.push({
      id: 'heal',
      name: '治疗术',
      type: 'heal',
      cooldown: 8000, // 8秒冷却
      castTime: 1000, // 1秒吟唱
      healAmount: 200,
      description: '恢复生命值'
    });

    // 添加强力攻击技能
    skills.push({
      id: 'power_strike',
      name: '重击',
      type: 'damage',
      cooldown: 5000, // 5秒冷却
      castTime: 800, // 0.8秒吟唱
      damage: 200,
      damageMultiplier: 1.8,
      description: '强力攻击技能'
    });

    return skills;
  }

  /**
   * 创建默认玩家角色对象（备用）
   */
  createDefaultPlayerCharacter() {
    const defaultCharacter = {
      id: 'default_player',
      name: '修仙者',
      level: 1,
      hp: 1000,
      maxHp: 1000,
      attack: 100,
      defense: 50,
      attackSpeed: 1.0,
      critical: 0.05,
      criticalDamage: 1.5,
      skills: this.getPlayerSkills({}),
      isAlive: true,
      type: 'player',

      // 添加getAttributes方法以兼容IdleBattleScene
      getAttributes: function() {
        return {
          hp: this.hp,
          maxHp: this.maxHp,
          attack: this.attack,
          defense: this.defense,
          attackSpeed: this.attackSpeed,
          critical: this.critical,
          criticalDamage: this.criticalDamage
        };
      }
    };

    return defaultCharacter;
  }

  /**
   * 处理IdleBattleScene战斗完成
   */
  handleStoryBattleComplete(result, chapter, level) {
    console.log('主线关卡战斗完成，结果：', result);

    // IdleBattleScene返回的结果格式：{ victory: boolean, enemyKilled: number, playerAlive: boolean }
    if (result && result.victory && result.playerAlive) {
      console.log('战斗胜利，开始处理关卡进度和奖励');

      // 更新关卡进度
      this.updateLevelProgress(chapter.id, level.id);

      // 发放奖励
      this.giveRewards(level.reward);

      // 显示成功提示
      wx.showToast({
        title: `通关 ${level.name}`,
        icon: 'success',
        duration: 2000
      });

      // 延迟返回主线关卡界面
      setTimeout(() => {
        this.sceneManager.showScene('story');
      }, 2000);

    } else {
      // 战斗失败，直接返回
      console.log('战斗失败，返回主线关卡界面');

      wx.showToast({
        title: '挑战失败',
        icon: 'none',
        duration: 2000
      });

      // 延迟返回主线关卡界面
      setTimeout(() => {
        this.sceneManager.showScene('story');
      }, 2000);
    }
  }

  /**
   * 处理战斗完成（旧版本，保留兼容性）
   */
  handleBattleComplete(result, chapter, level) {
    console.log('主线关卡战斗完成，结果：', result);

    // 检查战斗结果
    if (result && (result.victory === true || result === 'victory')) {
      console.log('战斗胜利，开始处理关卡进度和奖励');

      // 更新关卡进度
      this.updateLevelProgress(chapter.id, level.id);

      // 发放奖励
      this.giveRewards(level.reward);

      // 显示成功提示
      wx.showToast({
        title: `通关 ${level.name}`,
        icon: 'success',
        duration: 2000
      });

      // 延迟返回主线关卡界面
      setTimeout(() => {
        this.sceneManager.showScene('story');
      }, 2000);

    } else {
      // 战斗失败，直接返回
      console.log('战斗失败，返回主线关卡界面');

      wx.showToast({
        title: '挑战失败',
        icon: 'none',
        duration: 2000
      });

      // 延迟返回主线关卡界面
      setTimeout(() => {
        this.sceneManager.showScene('story');
      }, 2000);
    }
  }

  // 发放奖励
  giveRewards(rewards) {
    if (!game.gameStateManager) return;

    const player = game.gameStateManager.getPlayer();
    if (!player) return;

    console.log('发放奖励:', rewards);

    // 发放灵石
    if (rewards.lingshi > 0) {
      player.resources.lingshi = (player.resources.lingshi || 0) + rewards.lingshi;
    }

    // 发放仙玉
    if (rewards.xianyu > 0) {
      player.resources.xianyu = (player.resources.xianyu || 0) + rewards.xianyu;
    }

    // 发放经验（给主角色）
    if (rewards.exp > 0) {
      const characters = game.gameStateManager.getCharacters();
      const mainCharacter = characters.find(char => char.id === 1);
      if (mainCharacter) {
        mainCharacter.addExp(rewards.exp);
        console.log(`主角色获得经验: ${rewards.exp}`);
      }
    }

    // 保存游戏状态
    game.gameStateManager.setPlayer(player);
    game.gameStateManager.saveGameState();

    console.log('奖励发放完成');
  }

  // 更新关卡进度
  updateLevelProgress(chapterId, levelId) {
    if (!game.gameStateManager) return;

    const player = game.gameStateManager.getPlayer();
    if (!player) return;

    // 确保存在storyProgress对象
    if (!player.storyProgress) {
      player.storyProgress = {};
    }

    // 当前章节的进度
    const currentProgress = player.storyProgress[`chapter_${chapterId}`] || 0;

    // 更新当前章节进度
    if (levelId > currentProgress) {
      player.storyProgress[`chapter_${chapterId}`] = levelId;
      console.log(`更新章节${chapterId}进度为${levelId}`);

      // 如果完成了所有层数，解锁下一章节
      if (levelId >= 10) {
        // 确保下一章节存在
        if (chapterId < this.chapters.length) {
          // 将下一章节的进度设置为0，表示已解锁但未通关任何层数
          if (!player.storyProgress[`chapter_${chapterId + 1}`]) {
            player.storyProgress[`chapter_${chapterId + 1}`] = 0;
            console.log(`解锁章节${chapterId + 1}`);
            
            wx.showToast({
              title: `解锁新章节: ${this.chapters[chapterId].name}`,
              icon: 'success',
              duration: 3000
            });
          }
        }
      }

      // 保存游戏状态
      game.gameStateManager.setPlayer(player);
      game.gameStateManager.saveGameState();
    }
  }

  // 场景显示回调
  onShow(params) {
    // 重置状态，默认显示章节列表
    if (params && params.showLevels) {
      // 如果有明确的参数指示显示关卡列表，则保持当前状态
    } else {
      this.showingLevels = false;
      this.currentChapter = null;
    }

    // 清空UI元素
    this.clearUIElements();

    // 初始化UI
    this.initUI();

    // 更新选中的导航项
    this.selectedTabIndex = 0;
  }

  // 处理触摸结束事件
  handleTouchEnd(x, y) {
    // 检查底部导航栏点击
    const tabBarHeight = 80;
    const tabBarY = this.screenHeight - tabBarHeight;
    
    if (y >= tabBarY) {
      const tabWidth = this.screenWidth / 5;
      const tabIndex = Math.floor(x / tabWidth);
      
      if (tabIndex >= 0 && tabIndex < 5) {
        this.onTabSelected(tabIndex);
        return true; // 表示事件已处理
      }
    }

    // 调用父类的触摸处理
    return super.handleTouchEnd(x, y);
  }

  // 绘制场景
  drawScene() {
    // 绘制背景
    this.drawBackground();

    // 绘制顶部导航栏
    this.drawHeader();

    // 根据当前状态绘制不同内容
    if (this.showingLevels && this.currentChapter) {
      // 绘制关卡信息
      this.drawLevelInfo();
    } else {
      // 绘制章节选择区域
      this.drawChapterSelection();
    }

    // 绘制底部导航栏
    this.drawTabBar();
  }

  // 绘制背景
  drawBackground() {
    // 如果有背景图资源，使用背景图
    if (this.resources && this.resources.mainBg) {
      try {
        // 确保背景图覆盖整个屏幕，包括底部导航栏区域
        const bgWidth = this.screenWidth;
        const bgHeight = this.screenHeight; // 使用屏幕高度确保完全覆盖

        this.ctx.drawImage(
          this.resources.mainBg,
          0,
          0,
          bgWidth,
          bgHeight
        );
      } catch (error) {
        console.error('绘制背景图失败', error);
        this.drawDefaultBackground();
      }
    } else {
      // 如果没有背景图资源，使用渐变色背景
      this.drawDefaultBackground();
    }
  }

  // 绘制默认背景
  drawDefaultBackground() {
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, '#1a2a6c');
    gradient.addColorStop(0.5, '#b21f1f');
    gradient.addColorStop(1, '#fdbb2d');

    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }

  // 绘制顶部导航栏
  drawHeader() {
    const headerHeight = 80;
    const safeAreaHeight = 40;

    // 绘制安全区域
    this.ctx.fillStyle = '#000000';
    this.ctx.fillRect(0, 0, this.screenWidth, safeAreaHeight);

    // 绘制顶部导航栏背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, safeAreaHeight, this.screenWidth, headerHeight - safeAreaHeight);

    // 绘制标题
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('主线剧情', this.screenWidth / 2, safeAreaHeight + 30);

    // 绘制玩家信息
    const player = game.gameStateManager.getPlayer();
    if (player) {
      // 绘制玩家昵称和等级
      this.ctx.font = '16px Arial';
      this.ctx.textAlign = 'left';
      this.ctx.fillText(`${player.nickname} Lv.${player.level}`, 20, safeAreaHeight + 60);

      // 绘制资源信息
      this.ctx.textAlign = 'right';
      this.ctx.fillText(`仙玉: ${player.resources.xianyu}`, this.screenWidth - 20, safeAreaHeight + 60);
    }
  }

  // 绘制章节选择区域
  drawChapterSelection() {
    const headerHeight = 120; // 包含安全区域的总高度
    const infoY = headerHeight + 20;

    // 绘制章节选择提示
    this.ctx.font = '20px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('选择章节', this.screenWidth / 2, infoY);

    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#cccccc';
    this.ctx.fillText('体验精彩的修仙剧情', this.screenWidth / 2, infoY + 30);
  }

  // 绘制关卡信息
  drawLevelInfo() {
    const headerHeight = 120; // 包含安全区域的总高度
    const infoY = headerHeight + 20;

    if (this.currentChapter) {
      // 绘制当前章节信息
      this.ctx.font = '20px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(this.currentChapter.name, this.screenWidth / 2, infoY);

      this.ctx.font = '16px Arial';
      this.ctx.fillStyle = '#cccccc';
      this.ctx.fillText(this.currentChapter.description, this.screenWidth / 2, infoY + 30);
    }
  }

  // 绘制底部导航栏
  drawTabBar() {
    const tabBarHeight = 80;
    const tabBarY = this.screenHeight - tabBarHeight;
    const tabWidth = this.screenWidth / 5;

    // 导航栏数据
    const tabData = [
      { icon: '🏠', text: '主页', index: 0 },
      { icon: '👤', text: '角色', index: 1 },
      { icon: '🏔️', text: '洞府', index: 2 },
      { icon: '⚔️', text: '试炼', index: 3 },
      { icon: '🎒', text: '背包', index: 4 }
    ];

    // 绘制每个导航按钮
    for (let i = 0; i < tabData.length; i++) {
      const tab = tabData[i];
      const tabX = i * tabWidth;
      const centerX = tabX + tabWidth / 2;
      const isSelected = this.selectedTabIndex === i;

      // 绘制按钮背景（圆角矩形）
      const buttonRadius = 25;
      const buttonY = tabBarY + 10;
      const buttonHeight = 60;

      // 使用BaseScene的drawRoundRect方法
      const fillStyle = isSelected ? 'rgba(255, 215, 0, 0.3)' : 'rgba(255, 255, 255, 0.1)';
      this.drawRoundRect(tabX + 5, buttonY, tabWidth - 10, buttonHeight, buttonRadius, fillStyle);

      // 绘制图标
      const iconSize = isSelected ? 32 : 28;
      this.ctx.font = `${iconSize}px Arial`;
      this.ctx.textAlign = 'center';
      this.ctx.fillText(tab.icon, centerX, tabBarY + 35);

      // 绘制文字
      this.ctx.font = '12px Arial';
      this.ctx.fillStyle = isSelected ? '#FFD700' : '#FFFFFF';
      this.ctx.fillText(tab.text, centerX, tabBarY + 55);
    }
  }
}

// 使用CommonJS格式导出，确保与项目导入方式兼容
module.exports = StoryScene;