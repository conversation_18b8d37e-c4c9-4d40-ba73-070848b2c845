<execution>
  <constraint>
    ## UI优化技术限制
    - **微信小游戏Canvas限制**：Canvas绘制性能受设备GPU能力约束
    - **触摸事件限制**：微信小游戏触摸事件处理存在平台差异
    - **字体渲染限制**：不支持自定义字体，需使用系统字体
    - **动画性能约束**：复杂动画可能导致帧率下降和卡顿
    - **内存使用限制**：UI资源占用内存需严格控制
  </constraint>

  <rule>
    ## UI优化强制规则
    - **响应式设计必需**：UI必须适配不同屏幕尺寸和分辨率
    - **性能优先原则**：UI动画和效果不得影响游戏主要功能性能
    - **一致性要求**：所有UI组件必须保持统一的视觉风格
    - **可访问性规范**：UI元素必须有足够的触摸区域和视觉反馈
    - **内存管理严格**：UI资源必须及时释放，避免内存泄漏
  </rule>

  <guideline>
    ## UI优化指导原则
    - **简洁美观**：追求简洁而不简单的UI设计
    - **用户友好**：优化操作流程，减少用户学习成本
    - **性能平衡**：在视觉效果和性能之间找到最佳平衡点
    - **渐进增强**：基础功能优先，逐步添加视觉增强效果
  </guideline>

  <process>
    ## UI优化执行流程

    ### Phase 1: UI布局优化
    - 响应式布局设计和实现
    - 安全区域适配处理
    - 多分辨率适配方案

    ### Phase 2: 渲染性能优化
    - 减少重绘和重排操作
    - 使用离屏Canvas优化
    - 实现脏矩形更新机制

    ### Phase 3: 交互体验优化
    - 触摸反馈和状态变化
    - 防误触和手势识别
    - 动画过渡和视觉反馈

    ### Phase 4: 资源和内存优化
    - UI资源压缩和合并
    - 懒加载和按需渲染
    - 内存使用监控和优化
  </process>

  <criteria>
    ## UI优化质量标准
    - ✅ UI响应时间小于100ms
    - ✅ 适配所有主流设备屏幕
    - ✅ UI动画流畅不卡顿
    - ✅ 内存使用合理可控
    - ✅ 视觉效果美观统一
  </criteria>
</execution> 