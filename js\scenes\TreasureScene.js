/**
 * 古宝场景类
 * 展示和管理玩家的古宝系统
 */

import BaseScene from './BaseScene.js';
import Button from '../ui/Button.js';
import AppContext from '../utils/AppContext.js';

class TreasureScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager) {
    super(ctx, screenWidth, screenHeight, sceneManager);
    
    // 古宝管理器引用
    this.treasureManager = null;
    
    // 界面状态
    this.currentCategory = 'all'; // 当前选中的分类
    this.selectedTreasure = null; // 当前选中的古宝
    this.showUpgradeDialog = false; // 是否显示升级对话框
    this.showStarDialog = false; // 是否显示升星对话框
    
    // 滚动相关
    this.scrollY = 0;
    this.maxScrollY = 0;
    
    // 分页
    this.currentPage = 0;
    this.itemsPerPage = 12; // 每页显示12个古宝（3x4网格）
    
    // UI元素
    this.categoryButtons = [];
    this.treasureButtons = [];
    this.actionButtons = [];
    
    this.initUI();
  }

  initUI() {
    this.clearUIElements();
    
    // 获取古宝管理器
    this.treasureManager = AppContext.game.treasureManager;
    
    // 创建顶部分类选项卡
    this.createCategoryTabs();
    
    // 创建返回按钮
    this.createBackButton();
    
    // 创建操作按钮
    this.createActionButtons();
    
    // 加载古宝数据
    this.loadTreasureData();
  }

  /**
   * 创建分类选项卡
   */
  createCategoryTabs() {
    const tabWidth = this.screenWidth / 4;
    const tabHeight = 50;
    const tabY = 80; // 在标题栏下方
    
    const categories = [
      { id: 'all', name: '全部' },
      { id: 'weapon', name: '武器' },
      { id: 'artifact', name: '法宝' },
      { id: 'talisman', name: '护符' }
    ];
    
    this.categoryButtons = categories.map((category, index) => {
      const button = new Button(
        this.ctx,
        index * tabWidth,
        tabY,
        tabWidth,
        tabHeight,
        category.name,
        null,
        null,
        () => this.switchCategory(category.id)
      );
      
      // 设置按钮样式
      button.isSelected = (category.id === this.currentCategory);
      this.addUIElement(button);
      return button;
    });
  }

  /**
   * 创建返回按钮
   */
  createBackButton() {
    this.backButton = new Button(
      this.ctx,
      20,
      20,
      80,
      40,
      '返回',
      null,
      null,
      () => this.sceneManager.showScene('main')
    );
    this.addUIElement(this.backButton);
  }

  /**
   * 创建操作按钮
   */
  createActionButtons() {
    const buttonWidth = 100;
    const buttonHeight = 40;
    const bottomY = this.screenHeight - 60;
    
    // 详情按钮
    this.detailButton = new Button(
      this.ctx,
      20,
      bottomY,
      buttonWidth,
      buttonHeight,
      '详情',
      null,
      null,
      () => this.showTreasureDetail()
    );
    
    // 升级按钮
    this.upgradeButton = new Button(
      this.ctx,
      140,
      bottomY,
      buttonWidth,
      buttonHeight,
      '升级',
      null,
      null,
      () => this.showUpgradeDialog = true
    );
    
    // 升星按钮
    this.starButton = new Button(
      this.ctx,
      260,
      bottomY,
      buttonWidth,
      buttonHeight,
      '升星',
      null,
      null,
      () => this.showStarDialog = true
    );
    
    this.actionButtons = [this.detailButton, this.upgradeButton, this.starButton];
    this.actionButtons.forEach(button => this.addUIElement(button));
    
    // 初始状态下隐藏操作按钮
    this.updateActionButtons();
  }

  /**
   * 切换分类
   */
  switchCategory(categoryId) {
    if (this.currentCategory === categoryId) return;
    
    this.currentCategory = categoryId;
    this.selectedTreasure = null;
    this.currentPage = 0;
    this.scrollY = 0;
    
    // 更新分类按钮状态
    this.categoryButtons.forEach(button => {
      button.isSelected = false;
    });
    const selectedButton = this.categoryButtons.find(button => 
      button.text === this.getCategoryName(categoryId)
    );
    if (selectedButton) {
      selectedButton.isSelected = true;
    }
    
    this.updateActionButtons();
  }

  /**
   * 获取分类名称
   */
  getCategoryName(categoryId) {
    const nameMap = {
      'all': '全部',
      'weapon': '武器',
      'artifact': '法宝',
      'talisman': '护符'
    };
    return nameMap[categoryId] || '全部';
  }

  /**
   * 加载古宝数据
   */
  loadTreasureData() {
    if (!this.treasureManager) return;
    
    // 从管理器获取古宝数据
    const allTreasures = this.treasureManager.getPlayerTreasures();
    this.treasures = this.treasureManager.getTreasuresByCategory(this.currentCategory);
    
    console.log(`加载古宝数据，当前分类：${this.currentCategory}，数量：${this.treasures.length}`);
  }

  /**
   * 显示古宝详情
   */
  showTreasureDetail() {
    if (!this.selectedTreasure) return;
    
    const config = this.treasureManager.getTreasureConfig(this.selectedTreasure.treasure_id);
    if (!config) return;
    
    console.log('古宝详情:', {
      name: config.name,
      level: this.selectedTreasure.level,
      star: this.selectedTreasure.star,
      attributes: this.treasureManager.calculateTreasureAttributes(this.selectedTreasure)
    });
    
    // TODO: 可以添加详情弹窗
  }

  /**
   * 更新操作按钮状态
   */
  updateActionButtons() {
    const hasSelection = this.selectedTreasure !== null;
    
    this.actionButtons.forEach(button => {
      button.visible = hasSelection;
    });
    
    if (hasSelection) {
      // 检查升星按钮状态
      const canUpgradeStar = this.treasureManager.canUpgradeStar(this.selectedTreasure);
      this.starButton.enabled = canUpgradeStar.canUpgrade;
    }
  }

  /**
   * 处理触摸开始
   */
  handleTouchStart(x, y) {
    super.handleTouchStart(x, y);
    
    // 检查古宝网格点击
    this.checkTreasureGridClick(x, y);
  }

  /**
   * 检查古宝网格点击
   */
  checkTreasureGridClick(x, y) {
    const gridStartY = 140; // 网格开始Y坐标
    const cellWidth = this.screenWidth / 3; // 3列
    const cellHeight = 120; // 单元格高度
    const margin = 10;
    
    if (y < gridStartY) return;
    
    const col = Math.floor(x / cellWidth);
    const adjustedY = y - gridStartY + this.scrollY;
    const row = Math.floor(adjustedY / cellHeight);
    const index = row * 3 + col;
    
    if (index >= 0 && index < this.treasures.length) {
      this.selectedTreasure = this.treasures[index];
      this.updateActionButtons();
      console.log('选中古宝:', this.selectedTreasure.treasure_id);
    }
  }

  /**
   * 处理触摸移动（滚动）
   */
  handleTouchMove(x, y) {
    if (this.isDragging) {
      const deltaY = y - this.lastY;
      this.scrollY = Math.max(0, Math.min(this.maxScrollY, this.scrollY - deltaY));
      this.lastY = y;
    }
  }

  /**
   * 场景更新
   */
  updateScene() {
    super.updateScene();
    
    // 重新加载数据（如果需要）
    if (this.needsDataRefresh) {
      this.loadTreasureData();
      this.needsDataRefresh = false;
    }
    
    // 计算最大滚动距离
    const visibleRows = Math.floor((this.screenHeight - 200) / 120);
    const totalRows = Math.ceil(this.treasures.length / 3);
    this.maxScrollY = Math.max(0, (totalRows - visibleRows) * 120);
  }

  /**
   * 场景绘制
   */
  drawScene() {
    super.drawScene();
    
    // 绘制背景
    this.drawBackground();
    
    // 绘制标题
    this.drawTitle();
    
    // 绘制古宝网格
    this.drawTreasureGrid();
    
    // 绘制古宝详情
    if (this.selectedTreasure) {
      this.drawTreasureDetail();
    }
    
    // 绘制对话框
    if (this.showUpgradeDialog) {
      this.drawUpgradeDialog();
    }
    
    if (this.showStarDialog) {
      this.drawStarDialog();
    }
  }

  /**
   * 绘制背景
   */
  drawBackground() {
    this.ctx.fillStyle = '#1a1a2e';
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }

  /**
   * 绘制标题
   */
  drawTitle() {
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = 'bold 24px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('古宝系统', this.screenWidth / 2, 50);
  }

  /**
   * 绘制古宝网格
   */
  drawTreasureGrid() {
    const gridStartY = 140;
    const cellWidth = this.screenWidth / 3;
    const cellHeight = 120;
    const margin = 10;
    
    this.ctx.save();
    // 设置裁剪区域，防止滚动时越界
    this.ctx.beginPath();
    this.ctx.rect(0, gridStartY, this.screenWidth, this.screenHeight - gridStartY - 80);
    this.ctx.clip();
    
    for (let i = 0; i < this.treasures.length; i++) {
      const treasure = this.treasures[i];
      const col = i % 3;
      const row = Math.floor(i / 3);
      
      const x = col * cellWidth + margin;
      const y = gridStartY + row * cellHeight - this.scrollY + margin;
      const width = cellWidth - margin * 2;
      const height = cellHeight - margin * 2;
      
      // 跳过不在可见区域的项目
      if (y + height < gridStartY || y > this.screenHeight) continue;
      
      this.drawTreasureCell(treasure, x, y, width, height);
    }
    
    this.ctx.restore();
  }

  /**
   * 绘制单个古宝单元格
   */
  drawTreasureCell(treasure, x, y, width, height) {
    const config = this.treasureManager.getTreasureConfig(treasure.treasure_id);
    if (!config) return;
    
    const isSelected = this.selectedTreasure && this.selectedTreasure._id === treasure._id;
    const rarityConfig = this.treasureManager.rarityConfig[config.rarity];
    
    // 绘制背景
    this.ctx.fillStyle = isSelected ? '#444466' : '#2a2a3e';
    this.ctx.fillRect(x, y, width, height);
    
    // 绘制稀有度边框
    this.ctx.strokeStyle = rarityConfig.border;
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(x, y, width, height);
    
    // 绘制古宝图标区域（模拟）
    this.ctx.fillStyle = rarityConfig.color;
    this.ctx.fillRect(x + 10, y + 10, width - 60, height - 40);
    
    // 绘制古宝名称
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = '12px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(config.name, x + 5, y + height - 20);
    
    // 绘制等级
    this.ctx.fillStyle = '#ffdd44';
    this.ctx.font = 'bold 10px Arial';
    this.ctx.fillText(`Lv.${treasure.level}`, x + width - 40, y + 15);
    
    // 绘制星级
    this.drawStars(x + width - 40, y + 25, treasure.star);
    
    // 绘制自动生效标识
    this.ctx.fillStyle = '#44ff44';
    this.ctx.font = 'bold 8px Arial';
    this.ctx.fillText('生效中', x + 5, y + 15);
  }

  /**
   * 绘制星级
   */
  drawStars(x, y, starCount) {
    const starSize = 6;
    const starSpacing = 8;
    
    for (let i = 0; i < 5; i++) {
      this.ctx.fillStyle = i < starCount ? '#ffdd44' : '#666666';
      this.ctx.fillRect(x + i * starSpacing, y, starSize, starSize);
    }
  }

  /**
   * 绘制古宝详情
   */
  drawTreasureDetail() {
    const detail = this.treasureManager.getTreasureDetail(this.selectedTreasure);
    if (!detail) return;
    
    const panelX = 20;
    const panelY = this.screenHeight - 200;
    const panelWidth = this.screenWidth - 40;
    const panelHeight = 100;
    
    // 绘制详情面板背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    this.ctx.fillRect(panelX, panelY, panelWidth, panelHeight);
    
    this.ctx.strokeStyle = detail.rarity.border;
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(panelX, panelY, panelWidth, panelHeight);
    
    // 绘制古宝信息
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = 'bold 16px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(detail.name, panelX + 10, panelY + 25);
    
    // 绘制属性信息
    this.ctx.font = '12px Arial';
    let attrY = panelY + 45;
    for (const [attrName, value] of Object.entries(detail.attributes)) {
      const displayValue = typeof value === 'number' ? 
        (value < 1 ? (value * 100).toFixed(1) + '%' : Math.floor(value).toString()) : 
        value.toString();
      this.ctx.fillText(`${attrName}: ${displayValue}`, panelX + 10, attrY);
      attrY += 15;
      if (attrY > panelY + panelHeight - 10) break; // 避免超出面板
    }
  }

  /**
   * 绘制升级对话框
   */
  drawUpgradeDialog() {
    if (!this.selectedTreasure) return;
    
    const dialogWidth = this.screenWidth * 0.8;
    const dialogHeight = this.screenHeight * 0.6;
    const dialogX = (this.screenWidth - dialogWidth) / 2;
    const dialogY = (this.screenHeight - dialogHeight) / 2;
    
    // 绘制遮罩
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
    
    // 绘制对话框
    this.ctx.fillStyle = '#2a2a3e';
    this.ctx.fillRect(dialogX, dialogY, dialogWidth, dialogHeight);
    this.ctx.strokeStyle = '#666666';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(dialogX, dialogY, dialogWidth, dialogHeight);
    
    // 标题
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = 'bold 18px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('古宝升级', this.screenWidth / 2, dialogY + 30);
    
    // 关闭按钮
    this.ctx.fillStyle = '#ff4444';
    this.ctx.fillRect(dialogX + dialogWidth - 30, dialogY + 5, 25, 25);
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = 'bold 16px Arial';
    this.ctx.fillText('×', dialogX + dialogWidth - 17, dialogY + 22);
  }

  /**
   * 绘制升星对话框
   */
  drawStarDialog() {
    if (!this.selectedTreasure) return;
    
    const dialogWidth = this.screenWidth * 0.8;
    const dialogHeight = this.screenHeight * 0.6;
    const dialogX = (this.screenWidth - dialogWidth) / 2;
    const dialogY = (this.screenHeight - dialogHeight) / 2;
    
    // 绘制遮罩
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
    
    // 绘制对话框
    this.ctx.fillStyle = '#2a2a3e';
    this.ctx.fillRect(dialogX, dialogY, dialogWidth, dialogHeight);
    this.ctx.strokeStyle = '#666666';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(dialogX, dialogY, dialogWidth, dialogHeight);
    
    // 标题
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = 'bold 18px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('古宝升星', this.screenWidth / 2, dialogY + 30);
    
    // 显示升星消耗
    const canUpgrade = this.treasureManager.canUpgradeStar(this.selectedTreasure);
    if (canUpgrade.cost) {
      let costY = dialogY + 80;
      this.ctx.font = '14px Arial';
      this.ctx.textAlign = 'left';
      this.ctx.fillText('升星消耗：', dialogX + 20, costY);
      
      costY += 25;
      for (const [resourceType, amount] of Object.entries(canUpgrade.cost)) {
        this.ctx.fillText(`${resourceType}: ${amount}`, dialogX + 40, costY);
        costY += 20;
      }
    }
    
    // 关闭按钮
    this.ctx.fillStyle = '#ff4444';
    this.ctx.fillRect(dialogX + dialogWidth - 30, dialogY + 5, 25, 25);
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = 'bold 16px Arial';
    this.ctx.fillText('×', dialogX + dialogWidth - 17, dialogY + 22);
  }

  /**
   * 场景显示时调用
   */
  onShow(params) {
    super.onShow(params);
    this.needsDataRefresh = true;
    
    // 重新初始化UI，因为onHide时会清空所有UI元素
    this.initUI();
  }

  /**
   * 场景隐藏时调用
   */
  onHide() {
    super.onHide();
    this.selectedTreasure = null;
    this.showUpgradeDialog = false;
    this.showStarDialog = false;
  }
}

export default TreasureScene; 