/**
 * 计算古宝效果云函数
 * 计算玩家所有古宝的属性加成
 */

const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 古宝基础属性配置
const treasureBaseAttributes = {
  'ancient_sword': { attack: 120, hp: 80, crit: 0.05 },
  'dragon_blade': { attack: 100, hp: 120, speed: 10 },
  'phoenix_sword': { attack: 90, hp: 60, crit: 0.08, speed: 15 },
  'jade_seal': { hp: 200, defense: 50, dao_rule: 20 },
  'chaos_bell': { hp: 250, defense: 80, dao_rule: 30 },
  'dragon_scale': { hp: 300, defense: 100, crit_defense: 0.15 },
  'phoenix_feather': { hp: 150, crit: 0.10, speed: 20 },
  'void_pendant': { dao_rule: 50, hp: 100, crit: 0.05 },
  'life_jade': { hp: 400, healing: 0.20, defense: 60 }
};

// 等级成长系数
const levelGrowthRates = {
  'ancient_sword': { attack: 25, hp: 15, crit: 0.001 },
  'dragon_blade': { attack: 20, hp: 18, speed: 2 },
  'phoenix_sword': { attack: 18, hp: 12, crit: 0.0015, speed: 3 },
  'jade_seal': { hp: 35, defense: 8, dao_rule: 3 },
  'chaos_bell': { hp: 40, defense: 12, dao_rule: 4 },
  'dragon_scale': { hp: 50, defense: 15, crit_defense: 0.003 },
  'phoenix_feather': { hp: 25, crit: 0.002, speed: 4 },
  'void_pendant': { dao_rule: 8, hp: 20, crit: 0.001 },
  'life_jade': { hp: 60, healing: 0.004, defense: 10 }
};

exports.main = async (event, context) => {
  try {
    // 获取微信上下文，自动获取用户openid
    const wxContext = cloud.getWXContext();
    const openid = wxContext.OPENID;
    
    console.log('calculateTreasureEffects云函数调用:', {
      openid: openid ? '已获取' : '未获取',
      timestamp: new Date().toISOString()
    });
    
    if (!openid) {
      return {
        success: false,
        message: '用户身份验证失败'
      };
    }

    // 获取玩家所有古宝
    const treasuresResult = await db.collection('player_treasures')
      .where({
        player_id: openid
      })
      .get();

    const playerTreasures = treasuresResult.data;
    
    // 计算总属性加成
    const totalAttributes = {
      attack: 0,
      hp: 0,
      defense: 0,
      speed: 0,
      crit: 0,
      crit_defense: 0,
      dao_rule: 0,
      healing: 0
    };

    // 套装效果统计
    const setCount = {};
    
    for (const treasure of playerTreasures) {
      const treasureId = treasure.treasure_id;
      const level = treasure.level || 1;
      const star = treasure.star || 0;
      
      // 获取基础属性
      const baseAttrs = treasureBaseAttributes[treasureId] || {};
      const growthRates = levelGrowthRates[treasureId] || {};
      
      // 计算等级加成
      for (const [attr, baseValue] of Object.entries(baseAttrs)) {
        const growthRate = growthRates[attr] || 0;
        const totalValue = baseValue + (growthRate * (level - 1));
        totalAttributes[attr] = (totalAttributes[attr] || 0) + totalValue;
      }
      
      // 计算星级加成 (每星增加基础属性的10%)
      for (const [attr, baseValue] of Object.entries(baseAttrs)) {
        const starBonus = baseValue * 0.1 * star;
        totalAttributes[attr] = (totalAttributes[attr] || 0) + starBonus;
      }
      
      // 统计套装
      const category = getTreasureCategory(treasureId);
      setCount[category] = (setCount[category] || 0) + 1;
    }
    
    // 计算套装效果
    const setEffects = calculateSetEffects(setCount);
    
    // 应用套装效果
    for (const [attr, bonus] of Object.entries(setEffects)) {
      totalAttributes[attr] = (totalAttributes[attr] || 0) + bonus;
    }

    return {
      success: true,
      data: {
        totalAttributes,
        setEffects,
        treasureCount: playerTreasures.length,
        calculation_time: new Date()
      }
    };

  } catch (error) {
    console.error('计算古宝效果失败:', error);
    return {
      success: false,
      message: '计算失败，请重试'
    };
  }
};

// 获取古宝分类
function getTreasureCategory(treasureId) {
  const weaponIds = ['ancient_sword', 'dragon_blade', 'phoenix_sword'];
  const artifactIds = ['jade_seal', 'chaos_bell'];
  const talismanIds = ['dragon_scale', 'phoenix_feather', 'void_pendant', 'life_jade'];
  
  if (weaponIds.includes(treasureId)) return 'weapon';
  if (artifactIds.includes(treasureId)) return 'artifact';
  if (talismanIds.includes(treasureId)) return 'talisman';
  return 'unknown';
}

// 计算套装效果
function calculateSetEffects(setCount) {
  const setEffects = {};
  
  // 武器套装效果
  if (setCount.weapon >= 2) {
    setEffects.attack = (setEffects.attack || 0) + 100;
    setEffects.crit = (setEffects.crit || 0) + 0.05;
  }
  if (setCount.weapon >= 3) {
    setEffects.attack = (setEffects.attack || 0) + 200;
    setEffects.speed = (setEffects.speed || 0) + 20;
  }
  
  // 法宝套装效果
  if (setCount.artifact >= 2) {
    setEffects.hp = (setEffects.hp || 0) + 200;
    setEffects.dao_rule = (setEffects.dao_rule || 0) + 30;
  }
  
  // 护符套装效果
  if (setCount.talisman >= 2) {
    setEffects.defense = (setEffects.defense || 0) + 50;
    setEffects.crit_defense = (setEffects.crit_defense || 0) + 0.08;
  }
  if (setCount.talisman >= 3) {
    setEffects.hp = (setEffects.hp || 0) + 300;
    setEffects.healing = (setEffects.healing || 0) + 0.10;
  }
  
  return setEffects;
} 