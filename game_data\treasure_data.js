/**
 * 古宝系统静态数据配置
 * 包含所有古宝的基础属性、升级成长、升星配置等
 */

const treasureData = [
  // ========== 武器类古宝 ==========
  {
    id: 'ancient_sword', // 唯一ID
    name: '轩辕剑',
    type: 'attack',      // 类型: attack/defense/special
    category: 'weapon',  // 分类: weapon/artifact/talisman
    rarity: 5,           // 稀有度(1-5星)
    base_attributes: {   // 基础属性
      attack: 120,
      hp: 80,
      crit: 0.05
    },
    upgrade_growth: {    // 每级成长
      attack: 25,
      hp: 15,
      crit: 0.001
    },
    star_upgrade: [      // 升星配置(0-5星)
      { 
        star: 1, 
        cost: { lingshi: 1000, spirit_stone: 5 },
        attribute_bonus: { attack: 30 } 
      },
      { 
        star: 2, 
        cost: { lingshi: 3000, spirit_stone: 15 },
        attribute_bonus: { crit: 0.03 } 
      },
      { 
        star: 3, 
        cost: { lingshi: 8000, spirit_stone: 30 },
        attribute_bonus: { attack: 50, hp: 100 } 
      },
      { 
        star: 4, 
        cost: { lingshi: 20000, spirit_stone: 60 },
        attribute_bonus: { crit: 0.05, attack: 80 } 
      },
      { 
        star: 5, 
        cost: { lingshi: 50000, spirit_stone: 120 },
        attribute_bonus: { attack: 150, hp: 200, crit: 0.08 } 
      }
    ],
    description: '上古黄帝所用神剑，蕴含帝王之气，可斩妖除魔',
    icon: 'icons/treasure/ancient_sword.png'
  },
  {
    id: 'dragon_blade',
    name: '青龙偃月刀',
    type: 'attack',
    category: 'weapon',
    rarity: 4,
    base_attributes: {
      attack: 100,
      hp: 120,
      speed: 10
    },
    upgrade_growth: {
      attack: 20,
      hp: 18,
      speed: 2
    },
    star_upgrade: [
      { 
        star: 1, 
        cost: { lingshi: 800, spirit_stone: 3 },
        attribute_bonus: { attack: 25 } 
      },
      { 
        star: 2, 
        cost: { lingshi: 2400, spirit_stone: 10 },
        attribute_bonus: { speed: 8 } 
      },
      { 
        star: 3, 
        cost: { lingshi: 6000, spirit_stone: 20 },
        attribute_bonus: { attack: 40, hp: 80 } 
      },
      { 
        star: 4, 
        cost: { lingshi: 15000, spirit_stone: 45 },
        attribute_bonus: { speed: 15, attack: 60 } 
      },
      { 
        star: 5, 
        cost: { lingshi: 35000, spirit_stone: 90 },
        attribute_bonus: { attack: 120, hp: 150, speed: 25 } 
      }
    ],
    description: '关圣大帝所持神兵，刀锋带有青龙之力',
    icon: 'icons/treasure/dragon_blade.png'
  },
  {
    id: 'phoenix_sword',
    name: '凤凰羽剑',
    type: 'attack',
    category: 'weapon',
    rarity: 4,
    base_attributes: {
      attack: 90,
      hp: 60,
      crit: 0.08,
      speed: 15
    },
    upgrade_growth: {
      attack: 18,
      hp: 12,
      crit: 0.0015,
      speed: 3
    },
    star_upgrade: [
      { 
        star: 1, 
        cost: { lingshi: 800, spirit_stone: 3 },
        attribute_bonus: { crit: 0.02 } 
      },
      { 
        star: 2, 
        cost: { lingshi: 2400, spirit_stone: 10 },
        attribute_bonus: { attack: 30 } 
      },
      { 
        star: 3, 
        cost: { lingshi: 6000, spirit_stone: 20 },
        attribute_bonus: { crit: 0.03, speed: 10 } 
      },
      { 
        star: 4, 
        cost: { lingshi: 15000, spirit_stone: 45 },
        attribute_bonus: { attack: 50, crit: 0.04 } 
      },
      { 
        star: 5, 
        cost: { lingshi: 35000, spirit_stone: 90 },
        attribute_bonus: { attack: 100, crit: 0.10, speed: 30 } 
      }
    ],
    description: '由凤凰羽毛锻造的神剑，轻盈锋利，攻击时带有火焰效果',
    icon: 'icons/treasure/phoenix_sword.png'
  },

  // ========== 法宝类古宝 ==========
  {
    id: 'jade_seal',
    name: '传国玉玺',
    type: 'special',
    category: 'artifact',
    rarity: 5,
    base_attributes: {
      hp: 200,
      defense: 50,
      dao_rule: 20
    },
    upgrade_growth: {
      hp: 35,
      defense: 8,
      dao_rule: 3
    },
    star_upgrade: [
      { 
        star: 1, 
        cost: { lingshi: 1200, spirit_stone: 8 },
        attribute_bonus: { hp: 50 } 
      },
      { 
        star: 2, 
        cost: { lingshi: 3600, spirit_stone: 20 },
        attribute_bonus: { dao_rule: 10 } 
      },
      { 
        star: 3, 
        cost: { lingshi: 9000, spirit_stone: 40 },
        attribute_bonus: { defense: 25, hp: 150 } 
      },
      { 
        star: 4, 
        cost: { lingshi: 22000, spirit_stone: 80 },
        attribute_bonus: { dao_rule: 20, defense: 40 } 
      },
      { 
        star: 5, 
        cost: { lingshi: 55000, spirit_stone: 150 },
        attribute_bonus: { hp: 300, defense: 80, dao_rule: 50 } 
      }
    ],
    description: '秦始皇传承的皇权象征，蕴含帝王威严，可增强大道法则领悟',
    icon: 'icons/treasure/jade_seal.png'
  },
  {
    id: 'chaos_bell',
    name: '混沌钟',
    type: 'special',
    category: 'artifact',
    rarity: 5,
    base_attributes: {
      hp: 150,
      defense: 80,
      penetration: 30
    },
    upgrade_growth: {
      hp: 25,
      defense: 12,
      penetration: 5
    },
    star_upgrade: [
      { 
        star: 1, 
        cost: { lingshi: 1200, spirit_stone: 8 },
        attribute_bonus: { defense: 20 } 
      },
      { 
        star: 2, 
        cost: { lingshi: 3600, spirit_stone: 20 },
        attribute_bonus: { penetration: 15 } 
      },
      { 
        star: 3, 
        cost: { lingshi: 9000, spirit_stone: 40 },
        attribute_bonus: { hp: 100, defense: 30 } 
      },
      { 
        star: 4, 
        cost: { lingshi: 22000, spirit_stone: 80 },
        attribute_bonus: { penetration: 25, defense: 50 } 
      },
      { 
        star: 5, 
        cost: { lingshi: 55000, spirit_stone: 150 },
        attribute_bonus: { hp: 250, defense: 100, penetration: 60 } 
      }
    ],
    description: '开天辟地时的混沌法宝，具有镇压一切的威能',
    icon: 'icons/treasure/chaos_bell.png'
  },
  {
    id: 'eight_trigrams',
    name: '太极八卦盘',
    type: 'special',
    category: 'artifact',
    rarity: 4,
    base_attributes: {
      hp: 100,
      defense: 40,
      dao_rule: 25,
      speed: 5
    },
    upgrade_growth: {
      hp: 20,
      defense: 6,
      dao_rule: 4,
      speed: 1
    },
    star_upgrade: [
      { 
        star: 1, 
        cost: { lingshi: 1000, spirit_stone: 5 },
        attribute_bonus: { dao_rule: 8 } 
      },
      { 
        star: 2, 
        cost: { lingshi: 3000, spirit_stone: 15 },
        attribute_bonus: { defense: 15 } 
      },
      { 
        star: 3, 
        cost: { lingshi: 7500, spirit_stone: 30 },
        attribute_bonus: { dao_rule: 15, speed: 8 } 
      },
      { 
        star: 4, 
        cost: { lingshi: 18000, spirit_stone: 60 },
        attribute_bonus: { hp: 80, dao_rule: 20 } 
      },
      { 
        star: 5, 
        cost: { lingshi: 40000, spirit_stone: 120 },
        attribute_bonus: { hp: 150, defense: 60, dao_rule: 40 } 
      }
    ],
    description: '蕴含太极阴阳之理的神奇法宝，可助修炼者领悟天地大道',
    icon: 'icons/treasure/eight_trigrams.png'
  },

  // ========== 护符类古宝 ==========
  {
    id: 'dragon_scale',
    name: '真龙鳞甲',
    type: 'defense',
    category: 'talisman',
    rarity: 5,
    base_attributes: {
      hp: 300,
      defense: 100,
      crit_defense: 0.15
    },
    upgrade_growth: {
      hp: 50,
      defense: 15,
      crit_defense: 0.002
    },
    star_upgrade: [
      { 
        star: 1, 
        cost: { lingshi: 1500, spirit_stone: 10 },
        attribute_bonus: { hp: 80 } 
      },
      { 
        star: 2, 
        cost: { lingshi: 4500, spirit_stone: 25 },
        attribute_bonus: { defense: 30 } 
      },
      { 
        star: 3, 
        cost: { lingshi: 11000, spirit_stone: 50 },
        attribute_bonus: { crit_defense: 0.05, hp: 200 } 
      },
      { 
        star: 4, 
        cost: { lingshi: 25000, spirit_stone: 100 },
        attribute_bonus: { defense: 60, crit_defense: 0.08 } 
      },
      { 
        star: 5, 
        cost: { lingshi: 60000, spirit_stone: 180 },
        attribute_bonus: { hp: 400, defense: 120, crit_defense: 0.20 } 
      }
    ],
    description: '真龙脱落的逆鳞制成的护甲，防御力惊人',
    icon: 'icons/treasure/dragon_scale.png'
  },
  {
    id: 'phoenix_feather',
    name: '不死鸟羽',
    type: 'defense',
    category: 'talisman',
    rarity: 4,
    base_attributes: {
      hp: 180,
      defense: 60,
      hp_regen: 0.05
    },
    upgrade_growth: {
      hp: 30,
      defense: 10,
      hp_regen: 0.001
    },
    star_upgrade: [
      { 
        star: 1, 
        cost: { lingshi: 1200, spirit_stone: 6 },
        attribute_bonus: { hp_regen: 0.02 } 
      },
      { 
        star: 2, 
        cost: { lingshi: 3600, spirit_stone: 18 },
        attribute_bonus: { hp: 60 } 
      },
      { 
        star: 3, 
        cost: { lingshi: 9000, spirit_stone: 36 },
        attribute_bonus: { defense: 25, hp_regen: 0.03 } 
      },
      { 
        star: 4, 
        cost: { lingshi: 20000, spirit_stone: 75 },
        attribute_bonus: { hp: 120, hp_regen: 0.05 } 
      },
      { 
        star: 5, 
        cost: { lingshi: 45000, spirit_stone: 135 },
        attribute_bonus: { hp: 250, defense: 80, hp_regen: 0.12 } 
      }
    ],
    description: '不死鸟的羽毛制成的护符，具有强大的再生能力',
    icon: 'icons/treasure/phoenix_feather.png'
  },
  {
    id: 'void_pendant',
    name: '虚空吊坠',
    type: 'special',
    category: 'talisman',
    rarity: 4,
    base_attributes: {
      hp: 120,
      defense: 30,
      speed: 20,
      dodge: 0.08
    },
    upgrade_growth: {
      hp: 20,
      defense: 5,
      speed: 3,
      dodge: 0.0015
    },
    star_upgrade: [
      { 
        star: 1, 
        cost: { lingshi: 1000, spirit_stone: 5 },
        attribute_bonus: { dodge: 0.03 } 
      },
      { 
        star: 2, 
        cost: { lingshi: 3000, spirit_stone: 15 },
        attribute_bonus: { speed: 10 } 
      },
      { 
        star: 3, 
        cost: { lingshi: 7500, spirit_stone: 30 },
        attribute_bonus: { dodge: 0.05, speed: 15 } 
      },
      { 
        star: 4, 
        cost: { lingshi: 18000, spirit_stone: 60 },
        attribute_bonus: { hp: 100, dodge: 0.08 } 
      },
      { 
        star: 5, 
        cost: { lingshi: 40000, spirit_stone: 120 },
        attribute_bonus: { hp: 200, speed: 40, dodge: 0.15 } 
      }
    ],
    description: '来自虚空的神秘吊坠，佩戴者可获得闪避能力',
    icon: 'icons/treasure/void_pendant.png'
  },
  {
    id: 'life_jade',
    name: '生命玉佩',
    type: 'defense',
    category: 'talisman',
    rarity: 3,
    base_attributes: {
      hp: 250,
      defense: 25,
      hp_regen: 0.03
    },
    upgrade_growth: {
      hp: 40,
      defense: 4,
      hp_regen: 0.0008
    },
    star_upgrade: [
      { 
        star: 1, 
        cost: { lingshi: 600, spirit_stone: 2 },
        attribute_bonus: { hp: 50 } 
      },
      { 
        star: 2, 
        cost: { lingshi: 1800, spirit_stone: 8 },
        attribute_bonus: { hp_regen: 0.01 } 
      },
      { 
        star: 3, 
        cost: { lingshi: 4500, spirit_stone: 18 },
        attribute_bonus: { hp: 100, defense: 15 } 
      },
      { 
        star: 4, 
        cost: { lingshi: 11000, spirit_stone: 35 },
        attribute_bonus: { hp_regen: 0.02, hp: 150 } 
      },
      { 
        star: 5, 
        cost: { lingshi: 25000, spirit_stone: 70 },
        attribute_bonus: { hp: 300, defense: 40, hp_regen: 0.05 } 
      }
    ],
    description: '蕴含生命之力的玉佩，可以持续恢复生命力',
    icon: 'icons/treasure/life_jade.png'
  }
];

// 古宝套装效果配置
const treasureSetEffects = {
  // 龙族套装 (轩辕剑 + 真龙鳞甲)
  dragon_set: {
    name: '龙族霸主',
    treasures: ['ancient_sword', 'dragon_scale'],
    effects: [
      {
        count: 2,
        description: '龙族血脉觉醒：攻击力+15%，生命值+20%',
        attributes: {
          attack_percent: 0.15,
          hp_percent: 0.20
        }
      }
    ]
  },
  // 凤凰套装 (凤凰羽剑 + 不死鸟羽)
  phoenix_set: {
    name: '不死凤凰',
    treasures: ['phoenix_sword', 'phoenix_feather'],
    effects: [
      {
        count: 2,
        description: '凤凰涅槃：暴击率+10%，生命回复+8%',
        attributes: {
          crit_percent: 0.10,
          hp_regen_percent: 0.08
        }
      }
    ]
  },
  // 帝王套装 (传国玉玺 + 轩辕剑)
  emperor_set: {
    name: '帝王威仪',
    treasures: ['jade_seal', 'ancient_sword'],
    effects: [
      {
        count: 2,
        description: '帝王霸气：大道法则+30%，全属性+10%',
        attributes: {
          dao_rule_percent: 0.30,
          all_attributes_percent: 0.10
        }
      }
    ]
  }
};

// 古宝稀有度配置
const rarityConfig = {
  1: { name: '普通', color: '#888888', border: '#666666' },
  2: { name: '优秀', color: '#4CAF50', border: '#2E7D32' },
  3: { name: '稀有', color: '#2196F3', border: '#1565C0' },
  4: { name: '史诗', color: '#9C27B0', border: '#6A1B9A' },
  5: { name: '传说', color: '#FF9800', border: '#E65100' }
};

// 古宝类型配置
const typeConfig = {
  attack: { name: '攻击型', icon: 'attack_icon.png' },
  defense: { name: '防御型', icon: 'defense_icon.png' },
  special: { name: '特殊型', icon: 'special_icon.png' }
};

// 古宝分类配置
const categoryConfig = {
  weapon: { name: '武器', icon: 'weapon_category.png' },
  artifact: { name: '法宝', icon: 'artifact_category.png' },
  talisman: { name: '护符', icon: 'talisman_category.png' }
};

export default {
  treasures: treasureData,
  setEffects: treasureSetEffects,
  rarityConfig,
  typeConfig,
  categoryConfig
}; 