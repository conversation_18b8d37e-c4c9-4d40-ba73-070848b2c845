/**
 * 修仙六道后台管理系统配置文件
 * 环境配置和数据表映射
 */

module.exports = {
  // 服务器配置
  server: {
    port: process.env.PORT || 3000,
    env: process.env.NODE_ENV || 'development'
  },

  // 微信云开发环境配置
  // 重要：必须与游戏端使用相同的环境ID
  cloud: {
    envId: process.env.WECHAT_ENV_ID || 'cloud1-9gzbxxbff827656f',
    oldEnvId: 'prod-3g8ag9vk8ca8b6dc' // 旧的配置，需要迁移
  },

  // 游戏数据表配置（基于游戏端的实际表名）
  tables: {
    // 玩家相关数据表
    players: 'players',           // 玩家基础数据
    playerRes: 'player_res',      // 玩家资源数据
    playerTreasures: 'player_treasures',  // 玩家古宝装备数据
    playerSkill: 'player_skill',  // 玩家技能数据
    
    // 模板数据表（管理端数据）
    skillTemplates: 'skill_temp',
    treasureTemplates: 'treasure_tmp', 
    swordHeartTemplates: 's_heart_temp',
    itemTemplates: 'item_temp',
    treasureSets: 'treasure_set',
    
    // 游戏系统数据表
    mails: 'mails',              // 邮件数据
    playerMails: 'player_mails', // 玩家邮件关系表
    mailTemplates: 'mail_temp',  // 邮件模板表
    gachaPools: 'gacha_pools',   // 抽取池配置
    adminLogs: 'admin_logs'      // 管理员操作日志
  },

  // 调试配置
  debug: {
    useMockData: process.env.USE_MOCK_DB === 'true',
    logLevel: process.env.LOG_LEVEL || 'info'
  },

  // 安全配置
  security: {
    adminSecretKey: process.env.ADMIN_SECRET_KEY || 'xiuxian6-admin-secret-2024',
    jwtSecret: process.env.JWT_SECRET || 'xiuxian6-jwt-secret-2024'
  }
}; 