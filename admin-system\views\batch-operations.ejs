<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - 修仙六道后台管理</title>
    
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .operation-card {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            border: 1px solid #e9ecef;
        }
        .operation-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .btn-operation {
            width: 100%;
            padding: 15px;
            font-size: 16px;
            font-weight: 500;
        }
        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            background: #e7f3ff;
            border-color: #0056b3;
        }
        .upload-area.dragover {
            background: #e7f3ff;
            border-color: #0056b3;
        }
        .progress-container {
            display: none;
            margin-top: 20px;
        }
        .operation-log {
            max-height: 300px;
            overflow-y: auto;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse" style="min-height: 100vh;">
                <div class="position-sticky pt-3">
                    <h5 class="text-white text-center mb-4">修仙后台</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item"><a class="nav-link text-light" href="/"><i class="bi bi-speedometer2 me-2"></i>数据总览</a></li>
                        <li class="nav-item"><a class="nav-link text-light" href="/dashboard"><i class="bi bi-graph-up me-2"></i>数据统计</a></li>
                        <li class="nav-item"><a class="nav-link text-light" href="/players"><i class="bi bi-people me-2"></i>玩家管理</a></li>
                        <li class="nav-item"><a class="nav-link text-light" href="/player-resources"><i class="bi bi-gem me-2"></i>玩家资源</a></li>
                        <li class="nav-item"><a class="nav-link text-light" href="/player-equipment"><i class="bi bi-shield-check me-2"></i>装备技能</a></li>
                        <li class="nav-item"><a class="nav-link text-light" href="/mails"><i class="bi bi-envelope-fill me-2"></i>邮件管理</a></li>
                        <li class="nav-item"><a class="nav-link text-light" href="/logs"><i class="bi bi-clipboard-data me-2"></i>系统监控</a></li>
                        <li class="nav-item"><a class="nav-link text-light active" href="/batch-operations"><i class="bi bi-gear-fill me-2"></i>批量操作</a></li>
                    </ul>
                </div>
            </nav>
            
            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">批量操作和数据管理工具</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshOperationHistory()">
                                <i class="fas fa-sync-alt"></i> 刷新历史
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-info" onclick="showHelp()">
                                <i class="fas fa-question-circle"></i> 帮助
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 批量操作工具栏 -->
                <div class="row mb-4">
                    <!-- 数据导入导出 -->
                    <div class="col-lg-4 mb-4">
                        <div class="card operation-card h-100">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-exchange-alt"></i> 数据导入导出
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="card-text text-muted">支持玩家数据、模板数据的批量导入导出</p>
                                
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#importModal">
                                        <i class="fas fa-upload"></i> 数据导入
                                    </button>
                                    <button class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#exportModal">
                                        <i class="fas fa-download"></i> 数据导出
                                    </button>
                                    <button class="btn btn-outline-success" onclick="downloadTemplate()">
                                        <i class="fas fa-file-excel"></i> 下载模板
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 批量修改 -->
                    <div class="col-lg-4 mb-4">
                        <div class="card operation-card h-100">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-edit"></i> 批量修改
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="card-text text-muted">批量修改玩家属性、资源、状态等信息</p>
                                
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#batchUpdateModal">
                                        <i class="fas fa-users-cog"></i> 批量修改玩家
                                    </button>
                                    <button class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#batchResourceModal">
                                        <i class="fas fa-coins"></i> 批量修改资源
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="showBatchStatusUpdate()">
                                        <i class="fas fa-user-slash"></i> 批量状态管理
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据管理 -->
                    <div class="col-lg-4 mb-4">
                        <div class="card operation-card h-100">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-database"></i> 数据管理
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="card-text text-muted">数据备份、清理、统计分析等工具</p>
                                
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-warning" onclick="createBackup()">
                                        <i class="fas fa-archive"></i> 创建备份
                                    </button>
                                    <button class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#dataAnalysisModal">
                                        <i class="fas fa-chart-bar"></i> 数据分析
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="showDataCleanup()">
                                        <i class="fas fa-broom"></i> 数据清理
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作历史和日志 -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-history"></i> 批量操作历史
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>操作时间</th>
                                                <th>操作类型</th>
                                                <th>操作人</th>
                                                <th>影响记录数</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="operationHistoryTableBody">
                                            <tr>
                                                <td colspan="6" class="text-center text-muted">暂无操作历史</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <!-- 分页导航 -->
                                <nav aria-label="操作历史分页" class="mt-3">
                                    <ul class="pagination justify-content-center" id="operationHistoryPagination">
                                        <!-- 分页按钮将通过JavaScript动态生成 -->
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 数据导入模态框 -->
    <div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="importModalLabel">数据导入</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="importType" class="form-label">导入类型</label>
                        <select class="form-select" id="importType">
                            <option value="players">玩家数据</option>
                            <option value="resources">玩家资源</option>
                            <option value="skills">功法模板</option>
                            <option value="treasures">古宝模板</option>
                        </select>
                    </div>
                    
                    <div class="upload-area" id="uploadArea">
                        <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                        <h5>拖拽文件到这里或点击选择文件</h5>
                        <p class="text-muted">支持CSV、Excel格式，文件大小不超过10MB</p>
                        <input type="file" id="importFile" class="d-none" accept=".csv,.xlsx,.xls">
                        <button type="button" class="btn btn-primary" onclick="document.getElementById('importFile').click()">
                            <i class="fas fa-folder-open"></i> 选择文件
                        </button>
                    </div>
                    
                    <div class="progress-container" id="importProgress">
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <small class="text-muted mt-2 d-block">正在导入数据...</small>
                    </div>
                    
                    <div class="mt-3">
                        <h6>导入选项</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="skipDuplicates" checked>
                            <label class="form-check-label" for="skipDuplicates">跳过重复数据</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="validateData" checked>
                            <label class="form-check-label" for="validateData">验证数据格式</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="startImport()" id="startImportBtn">开始导入</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据导出模态框 -->
    <div class="modal fade" id="exportModal" tabindex="-1" aria-labelledby="exportModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exportModalLabel">数据导出</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="exportType" class="form-label">导出类型</label>
                            <select class="form-select" id="exportType">
                                <option value="players">玩家数据</option>
                                <option value="resources">玩家资源</option>
                                <option value="equipment">玩家装备</option>
                                <option value="skills">玩家技能</option>
                                <option value="mails">邮件记录</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="exportFormat" class="form-label">导出格式</label>
                            <select class="form-select" id="exportFormat">
                                <option value="csv">CSV</option>
                                <option value="excel">Excel</option>
                                <option value="json">JSON</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <h6>导出条件</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <label for="exportStartDate" class="form-label">开始日期</label>
                                <input type="date" class="form-control" id="exportStartDate">
                            </div>
                            <div class="col-md-6">
                                <label for="exportEndDate" class="form-label">结束日期</label>
                                <input type="date" class="form-control" id="exportEndDate">
                            </div>
                        </div>
                        <div class="mt-2">
                            <label for="exportLimit" class="form-label">最大记录数</label>
                            <input type="number" class="form-control" id="exportLimit" value="10000" min="1" max="100000">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-success" onclick="startExport()">开始导出</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量修改玩家模态框 -->
    <div class="modal fade" id="batchUpdateModal" tabindex="-1" aria-labelledby="batchUpdateModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="batchUpdateModalLabel">批量修改玩家</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="batchPlayerIds" class="form-label">玩家ID列表（一行一个或逗号分隔）</label>
                        <textarea class="form-control" id="batchPlayerIds" rows="4" placeholder="输入要修改的玩家ID"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <label for="batchLevel" class="form-label">等级</label>
                            <input type="number" class="form-control" id="batchLevel" placeholder="不修改请留空">
                        </div>
                        <div class="col-md-4">
                            <label for="batchRealm" class="form-label">境界</label>
                            <input type="text" class="form-control" id="batchRealm" placeholder="不修改请留空">
                        </div>
                        <div class="col-md-4">
                            <label for="batchStatus" class="form-label">状态</label>
                            <select class="form-select" id="batchStatus">
                                <option value="">不修改</option>
                                <option value="active">正常</option>
                                <option value="banned">封禁</option>
                                <option value="suspended">暂停</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-warning" onclick="executeBatchUpdate()">执行修改</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let currentPage = 1;
        const pageSize = 10;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadOperationHistory();
            initializeUploadArea();
        });

        // 初始化上传区域
        function initializeUploadArea() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('importFile');

            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    fileInput.files = files;
                    handleFileSelect(files[0]);
                }
            });

            fileInput.addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    handleFileSelect(e.target.files[0]);
                }
            });
        }

        // 处理文件选择
        function handleFileSelect(file) {
            const uploadArea = document.getElementById('uploadArea');
            uploadArea.innerHTML = `
                <i class="fas fa-file fa-2x text-success mb-2"></i>
                <h6>${file.name}</h6>
                <p class="text-muted">文件大小: ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearFileSelection()">
                    <i class="fas fa-times"></i> 移除
                </button>
            `;
        }

        // 清除文件选择
        function clearFileSelection() {
            document.getElementById('importFile').value = '';
            const uploadArea = document.getElementById('uploadArea');
            uploadArea.innerHTML = `
                <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                <h5>拖拽文件到这里或点击选择文件</h5>
                <p class="text-muted">支持CSV、Excel格式，文件大小不超过10MB</p>
                <button type="button" class="btn btn-primary" onclick="document.getElementById('importFile').click()">
                    <i class="fas fa-folder-open"></i> 选择文件
                </button>
            `;
        }

        // 开始导入
        async function startImport() {
            const fileInput = document.getElementById('importFile');
            const importType = document.getElementById('importType').value;
            
            if (!fileInput.files.length) {
                showAlert('danger', '请先选择要导入的文件');
                return;
            }

            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('type', importType);
            formData.append('skipDuplicates', document.getElementById('skipDuplicates').checked);
            formData.append('validateData', document.getElementById('validateData').checked);

            const progressContainer = document.getElementById('importProgress');
            const progressBar = progressContainer.querySelector('.progress-bar');
            
            progressContainer.style.display = 'block';
            document.getElementById('startImportBtn').disabled = true;

            try {
                const response = await fetch('/api/batch/import', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();
                
                if (data.success) {
                    progressBar.style.width = '100%';
                    showAlert('success', `导入完成！成功导入 ${data.imported} 条记录`);
                    loadOperationHistory();
                    
                    setTimeout(() => {
                        const modal = bootstrap.Modal.getInstance(document.getElementById('importModal'));
                        modal.hide();
                        progressContainer.style.display = 'none';
                        progressBar.style.width = '0%';
                        clearFileSelection();
                    }, 2000);
                } else {
                    throw new Error(data.message || '导入失败');
                }
            } catch (error) {
                console.error('导入失败:', error);
                showAlert('danger', '导入失败: ' + error.message);
            } finally {
                document.getElementById('startImportBtn').disabled = false;
            }
        }

        // 开始导出
        async function startExport() {
            const exportType = document.getElementById('exportType').value;
            const exportFormat = document.getElementById('exportFormat').value;
            const startDate = document.getElementById('exportStartDate').value;
            const endDate = document.getElementById('exportEndDate').value;
            const limit = document.getElementById('exportLimit').value;

            const params = new URLSearchParams({
                type: exportType,
                format: exportFormat,
                limit: limit
            });

            if (startDate) params.append('startDate', startDate);
            if (endDate) params.append('endDate', endDate);

            try {
                showAlert('info', '正在准备导出，请稍候...');
                
                const response = await fetch(`/api/batch/export?${params.toString()}`);
                
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `${exportType}_export_${new Date().toISOString().slice(0, 10)}.${exportFormat === 'excel' ? 'xlsx' : exportFormat}`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    
                    showAlert('success', '导出完成！');
                    loadOperationHistory();
                    
                    const modal = bootstrap.Modal.getInstance(document.getElementById('exportModal'));
                    modal.hide();
                } else {
                    const data = await response.json();
                    throw new Error(data.message || '导出失败');
                }
            } catch (error) {
                console.error('导出失败:', error);
                showAlert('danger', '导出失败: ' + error.message);
            }
        }

        // 加载操作历史
        async function loadOperationHistory() {
            try {
                const response = await fetch(`/api/batch/history?page=${currentPage}&pageSize=${pageSize}`);
                const data = await response.json();
                
                if (data.success) {
                    renderOperationHistory(data.data.operations);
                    renderOperationHistoryPagination(data.data.pagination);
                } else {
                    throw new Error(data.message || '加载操作历史失败');
                }
            } catch (error) {
                console.error('加载操作历史失败:', error);
                // 显示模拟数据
                renderMockOperationHistory();
            }
        }

        // 渲染操作历史
        function renderOperationHistory(operations) {
            const tbody = document.getElementById('operationHistoryTableBody');
            
            if (operations.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">暂无操作历史</td></tr>';
                return;
            }

            tbody.innerHTML = operations.map(op => `
                <tr>
                    <td>${new Date(op.created_at).toLocaleString('zh-CN')}</td>
                    <td><span class="badge bg-info">${op.operation_type}</span></td>
                    <td>${op.operator || '系统'}</td>
                    <td><span class="badge bg-secondary">${op.affected_count || 0}</span></td>
                    <td>
                        <span class="badge bg-${op.status === 'success' ? 'success' : op.status === 'failed' ? 'danger' : 'warning'}">
                            ${op.status === 'success' ? '成功' : op.status === 'failed' ? '失败' : '处理中'}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="viewOperationDetail('${op._id}')">
                            <i class="fas fa-eye"></i> 详情
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // 显示模拟操作历史
        function renderMockOperationHistory() {
            const mockOperations = [
                {
                    created_at: new Date(Date.now() - 3600000).toISOString(),
                    operation_type: '数据导入',
                    operator: 'admin',
                    affected_count: 150,
                    status: 'success'
                },
                {
                    created_at: new Date(Date.now() - 7200000).toISOString(),
                    operation_type: '批量修改',
                    operator: 'admin',
                    affected_count: 23,
                    status: 'success'
                }
            ];
            
            renderOperationHistory(mockOperations);
        }

        // 显示提示信息
        function showAlert(type, message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }

        // 其他功能的占位函数
        function downloadTemplate() {
            showAlert('info', '正在准备模板下载...');
            // 这里添加下载模板的逻辑
        }

        function executeBatchUpdate() {
            const playerIds = document.getElementById('batchPlayerIds').value;
            if (!playerIds.trim()) {
                showAlert('danger', '请输入要修改的玩家ID');
                return;
            }
            
            showAlert('info', '正在执行批量修改...');
            // 这里添加批量修改的逻辑
        }

        function refreshOperationHistory() {
            loadOperationHistory();
            showAlert('success', '操作历史已刷新');
        }

        function showHelp() {
            showAlert('info', '帮助文档正在加载...');
        }
    </script>
</body>
</html>
</body>
</html> 