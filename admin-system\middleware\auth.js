/**
 * 认证和权限控制中间件
 * 实现管理员身份验证和操作权限控制
 */

const jwt = require('jsonwebtoken');
const crypto = require('crypto');

// 管理员配置 - 实际项目中应该存储在数据库中
const ADMIN_USERS = {
  admin: {
    id: 'admin',
    username: 'admin',
    password: crypto.createHash('sha256').update('admin123').digest('hex'), // 默认密码admin123
    role: 'super_admin',
    permissions: ['*'], // 超级管理员拥有所有权限
    created_at: new Date('2024-01-01'),
    last_login: null
  },
  operator: {
    id: 'operator',
    username: 'operator', 
    password: crypto.createHash('sha256').update('operator123').digest('hex'), // 默认密码operator123
    role: 'operator',
    permissions: ['read', 'create', 'update'], // 运营人员权限
    created_at: new Date('2024-01-01'),
    last_login: null
  }
};

const JWT_SECRET = process.env.JWT_SECRET || 'xiuxian6-admin-secret-key-2024';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

/**
 * 生成JWT Token
 * @param {Object} user 用户对象
 * @returns {string} JWT Token
 */
function generateToken(user) {
  const payload = {
    userId: user.id,
    username: user.username,
    role: user.role,
    permissions: user.permissions,
    iat: Math.floor(Date.now() / 1000)
  };
  
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
}

/**
 * 验证JWT Token
 * @param {string} token JWT Token
 * @returns {Object|null} 解码后的用户信息
 */
function verifyToken(token) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    console.error('JWT验证失败:', error.message);
    return null;
  }
}

/**
 * 用户登录验证
 * @param {string} username 用户名
 * @param {string} password 密码
 * @returns {Object|null} 登录成功返回用户信息，失败返回null
 */
function authenticateUser(username, password) {
  const user = ADMIN_USERS[username];
  if (!user) {
    return null;
  }
  
  const hashedPassword = crypto.createHash('sha256').update(password).digest('hex');
  if (user.password !== hashedPassword) {
    return null;
  }
  
  // 更新最后登录时间
  user.last_login = new Date();
  
  return {
    id: user.id,
    username: user.username,
    role: user.role,
    permissions: user.permissions
  };
}

/**
 * 检查用户是否拥有特定权限
 * @param {Array} userPermissions 用户权限列表
 * @param {string} requiredPermission 需要的权限
 * @returns {boolean} 是否拥有权限
 */
function hasPermission(userPermissions, requiredPermission) {
  if (!userPermissions || !Array.isArray(userPermissions)) {
    return false;
  }
  
  // 超级管理员拥有所有权限
  if (userPermissions.includes('*')) {
    return true;
  }
  
  // 检查是否拥有特定权限
  return userPermissions.includes(requiredPermission);
}

/**
 * JWT认证中间件
 * 验证请求的JWT Token
 */
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
  
  if (!token) {
    return res.status(401).json({
      success: false,
      error: 'Access token required',
      code: 'MISSING_TOKEN'
    });
  }
  
  const decoded = verifyToken(token);
  if (!decoded) {
    return res.status(403).json({
      success: false,
      error: 'Invalid or expired token',
      code: 'INVALID_TOKEN'
    });
  }
  
  // 将用户信息添加到请求对象
  req.user = decoded;
  next();
}

/**
 * 权限检查中间件生成器
 * @param {string} permission 需要的权限
 * @returns {Function} 中间件函数
 */
function requirePermission(permission) {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }
    
    if (!hasPermission(req.user.permissions, permission)) {
      return res.status(403).json({
        success: false,
        error: `Permission denied. Required: ${permission}`,
        code: 'PERMISSION_DENIED',
        required_permission: permission,
        user_permissions: req.user.permissions
      });
    }
    
    next();
  };
}

/**
 * 管理员角色检查中间件
 */
function requireAdmin(req, res, next) {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required',
      code: 'AUTH_REQUIRED'
    });
  }
  
  if (req.user.role !== 'super_admin') {
    return res.status(403).json({
      success: false,
      error: 'Admin privileges required',
      code: 'ADMIN_REQUIRED',
      user_role: req.user.role
    });
  }
  
  next();
}

/**
 * 可选认证中间件
 * 如果提供了token则验证，否则继续（适用于某些可选认证的接口）
 */
function optionalAuth(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (token) {
    const decoded = verifyToken(token);
    if (decoded) {
      req.user = decoded;
    }
  }
  
  next();
}

module.exports = {
  generateToken,
  verifyToken,
  authenticateUser,
  hasPermission,
  authenticateToken,
  requirePermission,
  requireAdmin,
  optionalAuth,
  ADMIN_USERS
}; 