/**
 * 场景管理器类
 * 负责管理游戏中的所有场景
 * 版本: v2.0 - 添加仙友系统场景支持和内存管理优化
 */

// 引入各个场景
import IndexScene from '../scenes/IndexScene';
import MainScene from '../scenes/MainScene';
import CharacterScene from '../scenes/CharacterScene';
import CharacterDetailScene from '../scenes/CharacterDetailScene';
import DongfuScene from '../scenes/DongfuScene';
import TrialScene from '../scenes/TrialScene';
import BackpackScene from '../scenes/BackpackScene';
import AppContext from '../utils/AppContext';
// 引入新的功法场景 - 修改为ES6方式导入
import SkillScene from '../scenes/SkillScene';
const FunctionDetailScene = require('../scenes/FunctionDetailScene');
const FunctionScene = require('../scenes/FunctionScene');
// 引入挂机修炼场景和主线关卡场景(使用CommonJS方式)
const IdleScene = require('../scenes/IdleScene');
const StoryScene = require('../scenes/StoryScene');
// 引入角色相关场景
// 引入装备相关场景
import EquipmentSelectScene from '../scenes/EquipmentSelectScene';
import ForgeScene from '../scenes/ForgeScene';
// 引入新增场景：静室、突破和丹房 - 修改丹房为CommonJS方式导入
import JingshiScene from '../scenes/JingshiScene';
import BreakthroughScene from '../scenes/BreakthroughScene';
const DanfangScene = require('../scenes/DanfangScene');
// 引入功法相关场景
import SkillUpgradeScene from '../scenes/SkillUpgradeScene';
import SkillAdvancementScene from '../scenes/SkillAdvancementScene';
// 引入充值和VIP相关场景
import RechargeScene from '../scenes/RechargeScene';
import VIPPrivilegeScene from '../scenes/VIPPrivilegeScene';
// 引入剑心相关场景
import SwordHeartScene from '../scenes/SwordHeartScene';
import SwordHeartDetailScene from '../scenes/SwordHeartDetailScene';

// 引入竞技场相关场景
import ArenaScene from '../scenes/ArenaScene';
import ArenaBattleResultScene from '../scenes/ArenaBattleResultScene';

// 引入剑骨相关场景
import SwordBoneScene from '../scenes/SwordBoneScene';

// 引入技能装备场景
import SkillEquipScene from '../scenes/SkillEquipScene';

// 引入挂机战斗场景
import IdleBattleScene from '../scenes/IdleBattleScene';

// 引入邮箱场景
import MailScene from '../scenes/MailScene';

// 引入古宝场景
import TreasureScene from '../scenes/TreasureScene';
import TreasureDrawScene from '../scenes/TreasureDrawScene';

// 引入仙友系统场景
import XianyouScene from '../scenes/XianyouScene';
// import XianyouDetailScene from '../scenes/XianyouDetailScene';
// import XianyouDrawScene from '../scenes/XianyouDrawScene';
// import GiftScene from '../scenes/GiftScene';



class SceneManager {
  constructor(ctx, screenWidth, screenHeight, uiManager) {
    this.ctx = ctx;
    this.screenWidth = screenWidth;
    this.screenHeight = screenHeight;
    this.uiManager = uiManager;
    if (!AppContext.game) throw new Error("Game not initialized");

    // 保存游戏实例引用，便于场景访问
    this.game = AppContext.game;

    // 场景列表
    this.scenes = {};

    // 当前场景
    this.currentScene = null;

    // 场景历史栈（用于返回功能）
    this.sceneHistory = [];

    // 内存管理配置
    this.memoryConfig = {
      maxCachedScenes: 8,        // 最大缓存场景数
      preloadScenes: ['main'],   // 预加载场景
      enableLazyLoad: true       // 启用懒加载
    };

    // 场景加载状态
    this.sceneLoadStatus = {};

    // 初始化所有场景
    this.initScenes();

    console.log('SceneManager v2.0 初始化完成，支持仙友系统场景');
  }

  // 初始化所有场景
  initScenes() {
    // 创建所有场景实例
    this.scenes.index = new IndexScene(this.ctx, this.screenWidth, this.screenHeight, this, AppContext.game.resourceLoader.resources);
    this.scenes.main = new MainScene(this.ctx, this.screenWidth, this.screenHeight, this, AppContext.game.resourceLoader.resources);
    this.scenes.character = new CharacterScene(this.ctx, this.screenWidth, this.screenHeight, this);
    this.scenes.characterDetail = new CharacterDetailScene(this.ctx, this.screenWidth, this.screenHeight, this);
    this.scenes.dongfu = new DongfuScene(this.ctx, this.screenWidth, this.screenHeight, this, AppContext.game.resourceLoader.resources);
    this.scenes.trial = new TrialScene(this.ctx, this.screenWidth, this.screenHeight, this, AppContext.game.resourceLoader.resources);
    this.scenes.backpack = new BackpackScene(this.ctx, this.screenWidth, this.screenHeight, this, AppContext.game.resourceLoader.resources);

    try {
      // 注册新的功法场景
      this.scenes.skill = new SkillScene(this.ctx, this.screenWidth, this.screenHeight, this);
      console.log('新功法场景注册成功');
    } catch (error) {
      console.error('注册新功法场景失败:', error);
    }

    try {
      // 注册旧的功法场景（保持兼容性）
      this.scenes.function = new FunctionScene(this.ctx, this.screenWidth, this.screenHeight, this, AppContext.game.resourceLoader.resources);
      console.log('旧功法场景注册成功');
    } catch (error) {
      console.error('注册旧功法场景失败:', error);
    }

    try {
      // 注册功法详情场景
      this.scenes.functionDetail = new FunctionDetailScene(this.ctx, this.screenWidth, this.screenHeight, this, AppContext.game.resourceLoader.resources);
      console.log('功法详情场景注册成功');
    } catch (error) {
      console.error('注册功法详情场景失败:', error);
    }

    try {
      // 注册静室场景
      this.scenes.jingshi = new JingshiScene(this.ctx, this.screenWidth, this.screenHeight, this, AppContext.game.resourceLoader.resources);
      console.log('静室场景注册成功');
    } catch (error) {
      console.error('注册静室场景失败:', error);
    }

    try {
      // 注册突破场景
      this.scenes.breakthrough = new BreakthroughScene(this.ctx, this.screenWidth, this.screenHeight, this, AppContext.game.resourceLoader.resources);
      console.log('突破场景注册成功');
    } catch (error) {
      console.error('注册突破场景失败:', error);
    }

    try {
      // 注册丹房场景
      this.scenes.danfang = new DanfangScene(this.ctx, this.screenWidth, this.screenHeight, this, AppContext.game.resourceLoader.resources);
      console.log('丹房场景注册成功');
    } catch (error) {
      console.error('注册丹房场景失败:', error);
    }

    try {
      // 注册挂机游历场景
      this.scenes.idle = new IdleScene(this.ctx, this.screenWidth, this.screenHeight, this, AppContext.game.resourceLoader.resources);
      console.log('挂机游历场景注册成功');
    } catch (error) {
      console.error('注册挂机游历场景失败:', error);
    }

    try {
      // 注册主线关卡场景
      this.scenes.story = new StoryScene(this.ctx, this.screenWidth, this.screenHeight, this, AppContext.game.resourceLoader.resources);
      console.log('主线关卡场景注册成功');
    } catch (error) {
      console.error('注册主线关卡场景失败:', error);
    }



    try {
      // 注册装备选择场景
      this.scenes.equipmentSelect = new EquipmentSelectScene(this.ctx, this.screenWidth, this.screenHeight, this);
      console.log('装备选择场景注册成功');
    } catch (error) {
      console.error('注册装备选择场景失败:', error);
    }

    try {
      // 注册装备打造场景
      this.scenes.forge = new ForgeScene(this.ctx, this.screenWidth, this.screenHeight, this);
      console.log('装备打造场景注册成功');
    } catch (error) {
      console.error('注册装备打造场景失败:', error);
    }

    try {
      // 注册功法升级场景
      this.scenes.skillUpgrade = new SkillUpgradeScene(this.ctx, this.screenWidth, this.screenHeight, this);
      console.log('功法升级场景注册成功');
    } catch (error) {
      console.error('注册功法升级场景失败:', error);
    }

    try {
      // 注册功法进阶场景
      this.scenes.skillAdvancement = new SkillAdvancementScene(this.ctx, this.screenWidth, this.screenHeight, this);
      console.log('功法进阶场景注册成功');
    } catch (error) {
      console.error('注册功法进阶场景失败:', error);
    }

    try {
      // 注册充值场景
      this.scenes.recharge = new RechargeScene(this.ctx, this.screenWidth, this.screenHeight, this);
      console.log('充值场景注册成功');
    } catch (error) {
      console.error('注册充值场景失败:', error);
    }

    try {
      // 注册VIP特权场景
      this.scenes.vipPrivilege = new VIPPrivilegeScene(this.ctx, this.screenWidth, this.screenHeight, this);
      console.log('VIP特权场景注册成功');
    } catch (error) {
      console.error('注册VIP特权场景失败:', error);
    }

    try {
      // 注册剑心场景
      this.scenes.swordHeart = new SwordHeartScene(this.ctx, this.screenWidth, this.screenHeight, this);
      console.log('剑心场景注册成功');
    } catch (error) {
      console.error('注册剑心场景失败:', error);
    }

    try {
      // 注册剑心详情场景
      this.scenes.swordHeartDetail = new SwordHeartDetailScene(this.ctx, this.screenWidth, this.screenHeight, this);
      console.log('剑心详情场景注册成功');
    } catch (error) {
      console.error('注册剑心详情场景失败:', error);
    }



    try {
      // 注册竞技场场景
      this.scenes.arena = new ArenaScene(this.ctx, this.screenWidth, this.screenHeight, this);
      console.log('竞技场场景注册成功');
    } catch (error) {
      console.error('注册竞技场场景失败:', error);
    }

    try {
      // 注册竞技场战斗结果场景
      this.scenes.arenaBattleResult = new ArenaBattleResultScene(this.ctx, this.screenWidth, this.screenHeight, this);
      console.log('竞技场战斗结果场景注册成功');
    } catch (error) {
      console.error('注册竞技场战斗结果场景失败:', error);
    }

    try {
      // 注册剑骨场景
      this.scenes.swordBone = new SwordBoneScene(this.ctx, this.screenWidth, this.screenHeight, this);
      console.log('剑骨场景注册成功');
    } catch (error) {
      console.error('注册剑骨场景失败:', error);
    }

    try {
      // 注册技能装备场景
      this.scenes.skillEquip = new SkillEquipScene(this.ctx, this.screenWidth, this.screenHeight, this);
      console.log('技能装备场景注册成功');
    } catch (error) {
      console.error('注册技能装备场景失败:', error);
    }

    try {
      // 注册挂机战斗场景
      this.scenes.idleBattle = new IdleBattleScene(this.ctx, this.screenWidth, this.screenHeight, this);
      console.log('挂机战斗场景注册成功');
    } catch (error) {
      console.error('注册挂机战斗场景失败:', error);
    }



    try {
      // 注册邮箱场景
      this.scenes.mail = new MailScene(this.ctx, this.screenWidth, this.screenHeight, this);
      console.log('邮箱场景注册成功');
    } catch (error) {
      console.error('注册邮箱场景失败:', error);
    }

    try {
      // 注册古宝场景
      this.scenes.treasure = new TreasureScene(this.ctx, this.screenWidth, this.screenHeight, this);
      console.log('古宝场景注册成功');
    } catch (error) {
      console.error('注册古宝场景失败:', error);
    }

    try {
      // 注册古宝抽取场景
      this.scenes.treasureDraw = new TreasureDrawScene(this.ctx, this.screenWidth, this.screenHeight, this);
      console.log('古宝抽取场景注册成功');
    } catch (error) {
      console.error('注册古宝抽取场景失败:', error);
    }

    // 预留仙友系统场景注册
    this.initXianyouScenes();

    console.log('所有场景初始化完成');
  }

  /**
   * 初始化仙友系统相关场景
   */
  initXianyouScenes() {
    console.log('初始化仙友系统场景...');
    
    // 注册仙友主场景
    try {
      this.scenes.xianyou = new XianyouScene(this.ctx, this.screenWidth, this.screenHeight, this, AppContext.game.resourceLoader.resources);
      this.sceneLoadStatus.xianyou = 'loaded';
      console.log('仙友系统场景注册成功');
    } catch (error) {
      console.error('注册仙友系统场景失败:', error);
      this.sceneLoadStatus.xianyou = 'failed';
    }
    
    // 标记其他仙友相关场景为待实现状态
    this.sceneLoadStatus.xianyouDetail = 'pending';
    this.sceneLoadStatus.xianyouDraw = 'pending';
    this.sceneLoadStatus.gift = 'pending';

    // TODO: 后续实现其他仙友相关场景
    /*
    try {
      this.scenes.xianyouDetail = new XianyouDetailScene(this.ctx, this.screenWidth, this.screenHeight, this);
      this.scenes.xianyouDraw = new XianyouDrawScene(this.ctx, this.screenWidth, this.screenHeight, this);
      this.scenes.gift = new GiftScene(this.ctx, this.screenWidth, this.screenHeight, this);
      
      this.sceneLoadStatus.xianyouDetail = 'loaded';
      this.sceneLoadStatus.xianyouDraw = 'loaded';
      this.sceneLoadStatus.gift = 'loaded';
      
      console.log('所有仙友系统场景注册成功');
    } catch (error) {
      console.error('注册其他仙友系统场景失败:', error);
      this.sceneLoadStatus.xianyouDetail = 'failed';
      this.sceneLoadStatus.xianyouDraw = 'failed';
      this.sceneLoadStatus.gift = 'failed';
    }
    */
  }

  // 注册场景
  registerScene(sceneName, scene) {
    if (!sceneName || !scene) {
      console.error('注册场景失败: 场景名或场景对象无效');
      return false;
    }

    this.scenes[sceneName] = scene;
    console.log(`场景 ${sceneName} 已注册`);
    return true;
  }

  // 显示场景
  showScene(sceneName, params) {
    try {
      // 检查场景是否存在
      if (!this.scenes[sceneName]) {
        console.error(`场景 ${sceneName} 不存在！`);
        return null;
      }

      const targetScene = this.scenes[sceneName];

      // 检查场景是否实现了必要的方法
      if (typeof targetScene.show !== 'function') {
        console.error(`场景 ${sceneName} 未实现 show 方法！`);
        return null;
      }

      if (typeof targetScene.render !== 'function') {
        console.error(`场景 ${sceneName} 未实现 render 方法！`);
        return null;
      }

      // 记录场景切换历史
      if (this.currentScene) {
        this.addToHistory(this.getCurrentSceneName());
      }

      // 如果有当前场景，先隐藏
      if (this.currentScene) {
        this.hideCurrentScene();
      }

      // 显示新场景
      this.currentScene = targetScene;
      
      // 使用 show 方法并传递参数
      const showResult = this.currentScene.show(params);
      if (!showResult) {
        console.warn(`场景 ${sceneName} 的 show 方法返回了 false，可能表示初始化失败`);
      }

      // 触发场景切换事件
      this.onSceneChanged(sceneName, params);

      // 内存管理
      this.manageMemory();

      console.log(`场景切换成功: ${sceneName}`);

      // 返回场景实例
      return this.currentScene;

    } catch (error) {
      console.error(`显示场景 ${sceneName} 时发生错误:`, error);
      this.handleSceneError(sceneName, error);
      return null;
    }
  }

  /**
   * 隐藏当前场景
   */
  hideCurrentScene() {
    if (this.currentScene) {
      try {
        if (typeof this.currentScene.hide === 'function') {
          this.currentScene.hide();
        } else {
          console.warn('当前场景未实现 hide 方法');
        }
      } catch (error) {
        console.error('隐藏当前场景时发生错误:', error);
      }
    }
  }

  /**
   * 获取当前场景名称
   */
  getCurrentSceneName() {
    if (!this.currentScene) return null;
    
    // 通过遍历scenes对象找到当前场景的名称
    for (const [name, scene] of Object.entries(this.scenes)) {
      if (scene === this.currentScene) {
        return name;
      }
    }
    return null;
  }

  /**
   * 添加到场景历史
   */
  addToHistory(sceneName) {
    if (sceneName && sceneName !== this.sceneHistory[this.sceneHistory.length - 1]) {
      this.sceneHistory.push(sceneName);
      
      // 限制历史记录长度
      if (this.sceneHistory.length > 10) {
        this.sceneHistory.shift();
      }
    }
  }

  /**
   * 返回上一个场景
   */
  goBack() {
    if (this.sceneHistory.length > 0) {
      const previousScene = this.sceneHistory.pop();
      this.showScene(previousScene);
      return true;
    }
    return false;
  }

  /**
   * 场景切换事件处理
   */
  onSceneChanged(sceneName, params) {
    // 触发全局事件
    if (this.game && this.game.gameStateManager) {
      this.game.gameStateManager.emit('sceneChanged', {
        sceneName,
        params,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 内存管理
   */
  manageMemory() {
    if (!this.memoryConfig.enableLazyLoad) return;

    try {
      // 获取所有已加载的场景
      const loadedScenes = Object.keys(this.scenes).length;
      
      if (loadedScenes > this.memoryConfig.maxCachedScenes) {
        console.log('场景数量超过限制，进行内存清理...');
        this.cleanupMemory();
      }
    } catch (error) {
      console.error('内存管理失败:', error);
    }
  }

  /**
   * 清理内存
   */
  cleanupMemory() {
    // 这里可以实现场景的卸载逻辑
    // 暂时只记录日志，具体实现根据需要添加
    console.log('执行内存清理（暂未实现具体清理逻辑）');
  }

  /**
   * 场景错误处理
   */
  handleSceneError(sceneName, error) {
    console.error(`场景 ${sceneName} 发生错误:`, error);
    
    // 尝试恢复到主场景
    if (sceneName !== 'main' && this.scenes.main) {
      console.log('尝试恢复到主场景...');
      setTimeout(() => {
        this.showScene('main');
      }, 1000);
    }
  }

  // 获取当前场景
  getCurrentScene() {
    return this.currentScene;
  }

  // 获取指定场景
  getScene(sceneName) {
    return this.scenes[sceneName] || null;
  }

  // 触摸开始事件处理
  onTouchStart(x, y) {
    if (this.currentScene && typeof this.currentScene.handleTouch === 'function') {
      return this.currentScene.handleTouch('touchstart', x, y);
    } else if (this.currentScene && typeof this.currentScene.onTouchStart === 'function') {
      return this.currentScene.onTouchStart(x, y);
    }
    return false;
  }

  // 触摸移动事件处理
  onTouchMove(x, y) {
    if (this.currentScene && typeof this.currentScene.handleTouch === 'function') {
      return this.currentScene.handleTouch('touchmove', x, y);
    } else if (this.currentScene && typeof this.currentScene.onTouchMove === 'function') {
      return this.currentScene.onTouchMove(x, y);
    }
    return false;
  }

  // 触摸结束事件处理
  onTouchEnd(x, y) {
    if (this.currentScene && typeof this.currentScene.handleTouch === 'function') {
      return this.currentScene.handleTouch('touchend', x, y);
    } else if (this.currentScene && typeof this.currentScene.onTouchEnd === 'function') {
      return this.currentScene.onTouchEnd(x, y);
    }
    return false;
  }

  // 更新逻辑
  update() {
    if (this.currentScene && typeof this.currentScene.update === 'function') {
      this.currentScene.update();
    }
  }

  // 渲染场景
  render() {
    if (this.currentScene && typeof this.currentScene.render === 'function') {
      this.currentScene.render();
    }
  }
}

export default SceneManager;
