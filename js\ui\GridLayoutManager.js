/**
 * 网格布局管理器
 * 用于智能排布按钮和UI元素
 */
class GridLayoutManager {
  constructor(config) {
    this.containerX = config.x || 0;
    this.containerY = config.y || 0;
    this.containerWidth = config.width;
    this.containerHeight = config.height;
    
    this.padding = config.padding || 16;
    this.spacing = config.spacing || 12;
    this.columns = config.columns || 3;
    
    // 计算每个网格的尺寸
    this.gridWidth = (this.containerWidth - this.padding * 2 - this.spacing * (this.columns - 1)) / this.columns;
    this.gridHeight = config.gridHeight || this.gridWidth;
    
    // 当前页面和总页数
    this.currentPage = 0;
    this.totalPages = 0;
    
    // 存储所有元素
    this.elements = [];
    this.visibleElements = [];
    
    // 每页显示的元素数量
    this.elementsPerPage = this.calculateElementsPerPage();
    
    // 分页指示器
    this.showPageIndicator = config.showPageIndicator !== false;
    this.pageIndicatorHeight = 30;
  }
  
  /**
   * 计算每页可显示的元素数量
   */
  calculateElementsPerPage() {
    const availableHeight = this.containerHeight - this.padding * 2;
    const indicatorSpace = this.showPageIndicator ? this.pageIndicatorHeight + this.spacing : 0;
    const contentHeight = availableHeight - indicatorSpace;
    
    const rows = Math.floor((contentHeight + this.spacing) / (this.gridHeight + this.spacing));
    return Math.max(1, rows * this.columns);
  }
  
  /**
   * 添加元素到网格
   */
  addElement(element, config = {}) {
    const elementConfig = {
      element: element,
      priority: config.priority || 0, // 优先级，数字越大越靠前
      category: config.category || 'default',
      visible: config.visible !== false
    };
    
    this.elements.push(elementConfig);
    this.updateLayout();
  }
  
  /**
   * 批量添加元素
   */
  addElements(elements) {
    elements.forEach(elementConfig => {
      this.addElement(elementConfig.element, elementConfig);
    });
  }
  
  /**
   * 移除元素
   */
  removeElement(element) {
    this.elements = this.elements.filter(e => e.element !== element);
    this.updateLayout();
  }
  
  /**
   * 更新布局
   */
  updateLayout() {
    // 过滤可见元素并按优先级排序
    this.visibleElements = this.elements
      .filter(e => e.visible)
      .sort((a, b) => b.priority - a.priority);
    
    // 计算总页数
    this.totalPages = Math.ceil(this.visibleElements.length / this.elementsPerPage);
    
    // 确保当前页面在有效范围内
    this.currentPage = Math.min(this.currentPage, Math.max(0, this.totalPages - 1));
    
    // 重新计算所有元素的位置
    this.recalculatePositions();
  }
  
  /**
   * 重新计算元素位置
   */
  recalculatePositions() {
    const startIndex = this.currentPage * this.elementsPerPage;
    const endIndex = Math.min(startIndex + this.elementsPerPage, this.visibleElements.length);
    
    for (let i = 0; i < this.visibleElements.length; i++) {
      const elementConfig = this.visibleElements[i];
      const element = elementConfig.element;
      
      if (i >= startIndex && i < endIndex) {
        // 计算在当前页面中的相对位置
        const relativeIndex = i - startIndex;
        const row = Math.floor(relativeIndex / this.columns);
        const col = relativeIndex % this.columns;
        
        // 计算实际位置
        const x = this.containerX + this.padding + col * (this.gridWidth + this.spacing);
        const y = this.containerY + this.padding + row * (this.gridHeight + this.spacing);
        
        // 更新元素位置和大小
        if (element.setPosition) {
          element.setPosition(x, y);
        } else {
          element.x = x;
          element.y = y;
        }
        
        if (element.setSize) {
          element.setSize(this.gridWidth, this.gridHeight);
        } else {
          element.width = this.gridWidth;
          element.height = this.gridHeight;
        }
        
        // 设置为可见
        if (element.setVisible) {
          element.setVisible(true);
        } else {
          element.visible = true;
        }
      } else {
        // 隐藏不在当前页面的元素
        if (element.setVisible) {
          element.setVisible(false);
        } else {
          element.visible = false;
        }
      }
    }
  }
  
  /**
   * 切换到指定页面
   */
  goToPage(pageIndex) {
    if (pageIndex >= 0 && pageIndex < this.totalPages) {
      this.currentPage = pageIndex;
      this.recalculatePositions();
      return true;
    }
    return false;
  }
  
  /**
   * 下一页
   */
  nextPage() {
    return this.goToPage(this.currentPage + 1);
  }
  
  /**
   * 上一页
   */
  prevPage() {
    return this.goToPage(this.currentPage - 1);
  }
  
  /**
   * 处理触摸事件
   */
  handleTouch(x, y, eventType) {
    // 检查分页指示器区域
    if (this.showPageIndicator && this.totalPages > 1) {
      const indicatorY = this.containerY + this.containerHeight - this.pageIndicatorHeight;
      
      if (y >= indicatorY && y <= indicatorY + this.pageIndicatorHeight) {
        if (eventType === 'touchend') {
          // 点击左半部分上一页，右半部分下一页
          const midX = this.containerX + this.containerWidth / 2;
          if (x < midX) {
            this.prevPage();
          } else {
            this.nextPage();
          }
        }
        return true;
      }
    }
    
    // 传递触摸事件给当前页面的元素
    const startIndex = this.currentPage * this.elementsPerPage;
    const endIndex = Math.min(startIndex + this.elementsPerPage, this.visibleElements.length);
    
    for (let i = startIndex; i < endIndex; i++) {
      const element = this.visibleElements[i].element;
      
      if (element && element.visible !== false) {
        let handled = false;
        
        switch (eventType) {
          case 'touchstart':
            if (element.onTouchStart) {
              handled = element.onTouchStart(x, y);
            } else if (element.handleTouchStart) {
              handled = element.handleTouchStart(x, y);
            }
            break;
            
          case 'touchmove':
            if (element.onTouchMove) {
              handled = element.onTouchMove(x, y);
            } else if (element.handleTouchMove) {
              handled = element.handleTouchMove(x, y);
            }
            break;
            
          case 'touchend':
            if (element.onTouchEnd) {
              handled = element.onTouchEnd(x, y);
            } else if (element.handleTouchEnd) {
              handled = element.handleTouchEnd(x, y);
            }
            break;
        }
        
        if (handled) {
          return true;
        }
      }
    }
    
    return false;
  }
  
  /**
   * 更新动画
   */
  update(deltaTime) {
    this.visibleElements.forEach(elementConfig => {
      const element = elementConfig.element;
      if (element && element.update && element.visible !== false) {
        element.update(deltaTime);
      }
    });
  }
  
  /**
   * 渲染网格和元素
   */
  render(ctx) {
    // 渲染当前页面的元素
    const startIndex = this.currentPage * this.elementsPerPage;
    const endIndex = Math.min(startIndex + this.elementsPerPage, this.visibleElements.length);
    
    for (let i = startIndex; i < endIndex; i++) {
      const element = this.visibleElements[i].element;
      if (element && element.visible !== false) {
        // 优先使用render方法，其次是draw方法
        if (element.render) {
          element.render(ctx);
        } else if (element.draw) {
          element.draw(ctx);
        } else if (element.drawButton) {
          // 兼容旧的按钮绘制方法
          element.drawButton(ctx);
        }
      }
    }
    
    // 渲染分页指示器
    if (this.showPageIndicator && this.totalPages > 1) {
      this.renderPageIndicator(ctx);
    }
  }
  
  /**
   * 渲染分页指示器
   */
  renderPageIndicator(ctx) {
    const indicatorY = this.containerY + this.containerHeight - this.pageIndicatorHeight;
    const indicatorCenterX = this.containerX + this.containerWidth / 2;
    
    ctx.save();
    
    // 绘制背景
    ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    ctx.fillRect(
      this.containerX,
      indicatorY,
      this.containerWidth,
      this.pageIndicatorHeight
    );
    
    // 绘制页面点
    const dotSize = 8;
    const dotSpacing = 16;
    const totalWidth = this.totalPages * dotSize + (this.totalPages - 1) * (dotSpacing - dotSize);
    const startX = indicatorCenterX - totalWidth / 2;
    
    for (let i = 0; i < this.totalPages; i++) {
      const dotX = startX + i * dotSpacing;
      const dotY = indicatorY + this.pageIndicatorHeight / 2;
      
      ctx.beginPath();
      ctx.arc(dotX, dotY, dotSize / 2, 0, Math.PI * 2);
      
      if (i === this.currentPage) {
        ctx.fillStyle = '#FFFFFF';
      } else {
        ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
      }
      
      ctx.fill();
    }
    
    // 绘制左右箭头提示
    ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    if (this.currentPage > 0) {
      ctx.fillText('‹', this.containerX + 20, indicatorY + this.pageIndicatorHeight / 2);
    }
    
    if (this.currentPage < this.totalPages - 1) {
      ctx.fillText('›', this.containerX + this.containerWidth - 20, indicatorY + this.pageIndicatorHeight / 2);
    }
    
    ctx.restore();
  }
  
  /**
   * 设置容器尺寸
   */
  setContainer(x, y, width, height) {
    this.containerX = x;
    this.containerY = y;
    this.containerWidth = width;
    this.containerHeight = height;
    
    // 重新计算网格尺寸
    this.gridWidth = (this.containerWidth - this.padding * 2 - this.spacing * (this.columns - 1)) / this.columns;
    
    // 重新计算每页元素数量
    this.elementsPerPage = this.calculateElementsPerPage();
    
    // 更新布局
    this.updateLayout();
  }
  
  /**
   * 获取当前页面信息
   */
  getPageInfo() {
    return {
      currentPage: this.currentPage,
      totalPages: this.totalPages,
      elementsOnCurrentPage: Math.min(
        this.elementsPerPage,
        this.visibleElements.length - this.currentPage * this.elementsPerPage
      ),
      totalElements: this.visibleElements.length
    };
  }
}

export default GridLayoutManager; 