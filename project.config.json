{"description": "项目配置文件", "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "__usePrivacyCheck__": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "cloud": true, "packNpmRelationList": [], "ignoreUploadUnusedFiles": true}, "compileType": "game", "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "cloudfunctionRoot": "cloudfunctions/", "cloudbaseRoot": "", "cloudfunctionTemplateRoot": "cloudfunctionTemplate/", "cloud": true, "miniprogramRoot": "", "srcMiniprogramRoot": "", "cloudbaseInfo": {"containerDebugEnvs": [], "cloudbaseEnvironmentId": "cloud1-9gzbxxbff827656f"}, "packOptions": {"ignore": [], "include": []}, "appid": "wx38dbd9a27dc9fefb"}