/**
 * 古宝获取云函数
 * 处理玩家获得新古宝的逻辑
 */

const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 古宝静态数据 (从静态数据文件复制重要部分)
const validTreasureIds = [
  'ancient_sword', 'dragon_blade', 'phoenix_sword',
  'jade_seal', 'chaos_bell', 'eight_trigrams',
  'dragon_scale', 'phoenix_feather', 'void_pendant', 'life_jade'
];

exports.main = async (event, context) => {
  const { treasureId } = event;
  const { OPENID } = cloud.getWXContext();

  try {
    console.log('古宝获取请求:', { treasureId, openid: OPENID });

    // 1. 验证参数
    if (!treasureId) {
      return {
        success: false,
        error: 'INVALID_PARAMS',
        message: '古宝ID不能为空'
      };
    }

    // 2. 验证古宝ID有效性
    if (!validTreasureIds.includes(treasureId)) {
      return {
        success: false,
        error: 'TREASURE_NOT_FOUND',
        message: '古宝不存在'
      };
    }

    // 3. 检查玩家是否已拥有该古宝
    const existingTreasure = await db.collection('player_treasures')
      .where({
        player_id: OPENID,
        treasure_id: treasureId
      })
      .get();

    if (existingTreasure.data.length > 0) {
      return {
        success: false,
        error: 'TREASURE_ALREADY_OWNED',
        message: '已拥有该古宝'
      };
    }

    // 4. 创建新的古宝记录
    const newTreasureData = {
      player_id: OPENID,
      treasure_id: treasureId,
      level: 1,
      star: 0,
      exp: 0,
      is_equipped: false,
      created_at: new Date(),
      updated_at: new Date()
    };

    const addResult = await db.collection('player_treasures').add({
      data: newTreasureData
    });

    // 5. 返回新古宝的完整数据
    const newTreasure = {
      _id: addResult._id,
      ...newTreasureData
    };

    // 记录获取日志
    await db.collection('player_logs').add({
      data: {
        player_id: OPENID,
        action: 'acquire_treasure',
        details: {
          treasure_id: treasureId,
          treasure_record_id: addResult._id
        },
        timestamp: new Date()
      }
    });

    console.log('古宝获取成功:', newTreasure);

    return {
      success: true,
      data: {
        treasure: newTreasure,
        message: '古宝获取成功'
      }
    };

  } catch (error) {
    console.error('古宝获取失败:', error);
    return {
      success: false,
      error: 'SERVER_ERROR',
      message: '服务器错误，请稍后重试'
    };
  }
}; 