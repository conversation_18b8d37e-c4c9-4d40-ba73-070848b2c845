// 云函数入口文件 - 通用数据服务
const cloudbase = require("@cloudbase/node-sdk")

// 指定云开发环境 ID
const app = cloudbase.init({
  env: "cloud1-9gzbxxbff827656f",
})

// 引入微信云开发SDK获取用户身份
const cloud = require('wx-server-sdk')
cloud.init({
  env: 'cloud1-9gzbxxbff827656f'
})

// 支持的数据表映射（根据优化后的数据库设计）
const TABLE_MAPPING = {
  // 核心数据表
  'players': 'players',
  'player_res': 'player_res',
  'characters': 'characters', 
  'player_treasures': 'player_treasures',
  'player_skills': 'player_skills',
  'player_items': 'player_items',
  'sword_hearts': 'sword_hearts',
  'sword_bones': 'sword_bones',
  
  // 系统功能表
  'player_dongf': 'player_dongf',
  'player_arena': 'player_arena',
  'player_idle': 'player_idle',
  'p_skill_cul': 'p_skill_cul',
  
  // 交易和记录表
  'recharge_rec': 'recharge_rec',
  'gacha_records': 'gacha_records',
  'battle_records': 'battle_records',
  
  // 邮件和通知表
  'mail_temp': 'mail_temp',
  'player_mails': 'player_mails',
  
  // 系统配置表
  'game_configs': 'game_configs'
}

// 需要验证openid的表（玩家相关数据）
const OPENID_REQUIRED_TABLES = [
  'players', 'player_res', 'characters', 'player_treasures', 
  'player_skills', 'player_items', 'sword_hearts', 'sword_bones',
  'player_dongf', 'player_arena', 'player_idle', 'p_skill_cul',
  'recharge_rec', 'gacha_records', 'battle_records', 'player_mails'
]

// 云函数入口函数
exports.main = async (event, context) => {
  console.log('playerService云函数被调用')
  console.log('接收到的参数:', JSON.stringify(event))
  
  const { action, table, data, query, options } = event
  
  // 参数验证
  if (!action) {
    return {
      success: false,
      error: '缺少操作类型(action)参数',
      code: 'MISSING_ACTION'
    }
  }
  
  if (!table) {
    return {
      success: false,
      error: '缺少数据表(table)参数',
      code: 'MISSING_TABLE'
    }
  }
  
  // 验证表名是否支持
  if (!TABLE_MAPPING[table]) {
    return {
      success: false,
      error: `不支持的数据表: ${table}`,
      code: 'UNSUPPORTED_TABLE'
    }
  }
  
  // 获取用户身份
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  // 对于需要验证openid的表，检查用户身份
  if (OPENID_REQUIRED_TABLES.includes(table) && !openid) {
    return {
      success: false,
      error: '用户身份验证失败',
      code: 'UNAUTHORIZED'
    }
  }
  
  try {
    const models = app.models
    const tableName = TABLE_MAPPING[table]
    const model = models[tableName]
    
    if (!model) {
      return {
        success: false,
        error: `数据表 ${tableName} 不存在`,
        code: 'TABLE_NOT_FOUND'
      }
    }
    
    let result
    
    switch (action) {
      case 'create':
        result = await handleCreate(model, data, openid, table)
        break
        
      case 'update':
        result = await handleUpdate(model, data, query, openid, table)
        break
        
      case 'delete':
        result = await handleDelete(model, query, openid, table)
        break
        
      case 'find':
        result = await handleFind(model, query, options, openid, table)
        break
        
      case 'findOne':
        result = await handleFindOne(model, query, openid, table)
        break
        
      case 'count':
        result = await handleCount(model, query, openid, table)
        break
        
      default:
        return {
          success: false,
          error: `不支持的操作类型: ${action}`,
          code: 'UNSUPPORTED_ACTION'
        }
    }
    
    return {
      success: true,
      data: result,
      timestamp: Date.now()
    }
    
  } catch (error) {
    console.error('数据操作失败:', error)
    
    return {
      success: false,
      error: error.message || '数据操作失败',
      code: error.code || 'DATABASE_ERROR',
      timestamp: Date.now()
    }
  }
}

// 创建记录
async function handleCreate(model, data, openid, table) {
  if (!data) {
    throw new Error('缺少要创建的数据')
  }
  
  // 为需要openid的表自动添加openid
  if (OPENID_REQUIRED_TABLES.includes(table)) {
    data._openid = openid
  }
  
  // 添加时间戳
  const now = Date.now()
  data.createdAt = now
  data.updatedAt = now
  
  const result = await model.create({
    data: data
  })
  
  return result.data
}

// 更新记录
async function handleUpdate(model, data, query, openid, table) {
  if (!data) {
    throw new Error('缺少要更新的数据')
  }
  
  if (!query) {
    throw new Error('缺少查询条件')
  }
  
  // 为需要openid的表自动添加openid到查询条件
  if (OPENID_REQUIRED_TABLES.includes(table)) {
    query._openid = openid
  }
  
  // 添加更新时间戳
  data.updatedAt = Date.now()
  
  const result = await model.where(query).update({
    data: data
  })
  
  return {
    updated: result.updated,
    matched: result.matched
  }
}

// 删除记录
async function handleDelete(model, query, openid, table) {
  if (!query) {
    throw new Error('缺少查询条件')
  }
  
  // 为需要openid的表自动添加openid到查询条件
  if (OPENID_REQUIRED_TABLES.includes(table)) {
    query._openid = openid
  }
  
  const result = await model.where(query).remove()
  
  return {
    deleted: result.deleted
  }
}

// 查询多条记录
async function handleFind(model, query = {}, options = {}, openid, table) {
  // 为需要openid的表自动添加openid到查询条件
  if (OPENID_REQUIRED_TABLES.includes(table)) {
    query._openid = openid
  }
  
  let dbQuery = model.where(query)
  
  // 处理排序
  if (options.orderBy) {
    dbQuery = dbQuery.orderBy(options.orderBy.field, options.orderBy.direction || 'asc')
  }
  
  // 处理分页
  if (options.limit) {
    dbQuery = dbQuery.limit(options.limit)
  }
  
  if (options.skip) {
    dbQuery = dbQuery.skip(options.skip)
  }
  
  const result = await dbQuery.find()
  
  return result.data
}

// 查询单条记录
async function handleFindOne(model, query = {}, openid, table) {
  // 为需要openid的表自动添加openid到查询条件
  if (OPENID_REQUIRED_TABLES.includes(table)) {
    query._openid = openid
  }
  
  const result = await model.where(query).limit(1).find()
  
  return result.data.length > 0 ? result.data[0] : null
}

// 统计记录数
async function handleCount(model, query = {}, openid, table) {
  // 为需要openid的表自动添加openid到查询条件
  if (OPENID_REQUIRED_TABLES.includes(table)) {
    query._openid = openid
  }
  
  const result = await model.where(query).count()
  
  return {
    total: result.total
  }
}

 