/**
 * 主页面场景类
 * 游戏的主界面，包含顶部信息栏和底部导航栏
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import EnhancedButton from '../ui/EnhancedButton';
import GridLayoutManager from '../ui/GridLayoutManager';
import game from '../../game';
import TitleBar from '../ui/TitleBar.js';
// 导入静室场景以便访问静态方法
import JingshiScene from './JingshiScene';
// 导入云函数测试对话框
import CloudFunctionTestDialog from '../ui/CloudFunctionTestDialog.js';
// 导入数据库测试对话框
import DatabaseTestDialog from '../ui/DatabaseTestDialog.js';

class MainScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager,resources) {
    super(ctx, screenWidth, screenHeight, sceneManager);
    // 场景资源
    this.resources = resources || {};

    // 背景滚动位置
    this.bgScrollY = 0;
    this.isDragging = false;
    this.lastY = 0;

    // 当前选中的底部导航项
    this.selectedTabIndex = 0;

    // 顶部导航栏
    this.titleBar = null;

    // 云函数测试对话框
    this.cloudTestDialog = null;
    
    // 数据库测试对话框
    this.databaseTestDialog = null;

    // 网格布局管理器
    this.gridLayoutManager = null;

    // 功能按钮数组
    this.functionButtons = [];

    // 初始化UI
    this.initUI();
  }

  // 初始化UI
  initUI() {
    // 首先清空已有的UI元素
    this.clearUIElements();

    console.log('MainScene初始化UI');

    // 获取资源
    if (!this.resources) {
      this.resources = game.resourceLoader ? game.resourceLoader.resources || {} : {};
    }

    // 创建顶部安全区域和标题栏
    const safeAreaHeight = 40; // 安全区域高度，防止被摄像头和微信按钮遮挡
    const headerHeight = 80;
    const totalHeaderHeight = safeAreaHeight + headerHeight;
    
    this.titleBar = new TitleBar(
      this.ctx,
      0,
      safeAreaHeight, // 标题栏从安全区域下方开始
      this.screenWidth,
      headerHeight,
      this.resources
    );
    this.addUIElement(this.titleBar);

    // 底部导航栏现在通过drawTabBar方法直接绘制，不再创建UI元素

    // 初始化网格布局管理器
    this.initGridLayout();

    // 创建功能按钮（包含系统操作按钮）
    this.createFunctionButtons();

    console.log('MainScene UI初始化完成');
  }

  // 初始化网格布局管理器
  initGridLayout() {
    const safeAreaHeight = 40;
    const headerHeight = 80;
    const tabBarHeight = 80; // 更新为新的高度
    const totalHeaderHeight = safeAreaHeight + headerHeight;
    // 系统按钮现在集成到网格中，不需要单独预留空间
    
    const containerY = totalHeaderHeight + 20;
    const containerHeight = this.screenHeight - totalHeaderHeight - tabBarHeight - 40;
    
    this.gridLayoutManager = new GridLayoutManager({
      x: 15,
      y: containerY,
      width: this.screenWidth - 30,
      height: containerHeight,
      columns: 6, // 改为6列
      padding: 10,
      spacing: 8, // 减少间距
      gridHeight: 90, // 设置较小的高度适应圆形按钮
      showPageIndicator: true
    });
  }

  // 创建功能按钮
  createFunctionButtons() {
    // 定义所有功能按钮的配置，包含系统操作按钮
    const buttonConfigs = [
      // 系统操作按钮（高优先级）
      {
        text: '保存数据',
        icon: '💾',
        type: 'success',
        priority: 105,
        category: 'system',
        action: () => this.savePlayerData()
      },
      {
        text: '创建玩家',
        icon: '👤',
        type: 'primary',
        priority: 104,
        category: 'system',
        action: () => this.createPlayerData()
      },
      {
        text: '登录游戏',
        icon: '🔑',
        type: 'default',
        priority: 103,
        category: 'system',
        action: () => this.handleLoginButton()
      },
      {
        text: '云函数测试',
        icon: '🔧',
        type: 'warning',
        priority: 102,
        category: 'system',
        action: () => this.showCloudTestDialog()
      },
      {
        text: '数据库测试',
        icon: '🗄️',
        type: 'danger',
        priority: 101,
        category: 'system',
        action: () => this.showDatabaseTestDialog()
      },
      {
        text: '数据同步',
        icon: '🚀',
        type: 'info',
        priority: 100,
        category: 'system',
        action: () => this.triggerDataSync()
      },
      {
        text: '测试数据同步',
        icon: '🔄',
        type: 'warning',
        priority: 99,
        category: 'system',
        action: () => this.testDataSync()
      },
      {
        text: '安全测试',
        icon: '🛡️',
        type: 'success',
        priority: 98,
        category: 'system',
        action: () => this.testSecureOperations()
      },
      
      // 核心玩法功能
      {
        text: '主线关卡',
        icon: '⚔️',
        type: 'primary',
        priority: 100,
        category: 'gameplay',
        action: () => this.sceneManager.showScene('story')
      },
      {
        text: '角色详情',
        icon: '🧙',
        type: 'primary',
        priority: 95,
        category: 'character',
        action: () => this.sceneManager.showScene('characterDetail', { characterId: 1 })
      },
      
      // 修炼和提升系统
      {
        text: '挂机游历',
        icon: '🌍',
        type: 'green',
        priority: 75,
        category: 'gameplay',
        action: () => this.sceneManager.showScene('idle')
      },
      {
        text: '静室修炼',
        icon: '🧘',
        type: 'purple',
        priority: 72,
        category: 'cultivation',
        action: () => this.sceneManager.showScene('jingshi')
      },
      
      // 装备和打造系统
      {
        text: '装备打造',
        icon: '🔨',
        type: 'secondary',
        priority: 60,
        category: 'equipment',
        action: () => this.sceneManager.showScene('forge')
      },
      {
        text: '装备选择',
        icon: '🛡️',
        type: 'blue',
        priority: 58,
        category: 'equipment',
        action: () => this.sceneManager.showScene('equipmentSelect')
      },
      {
        text: '物品仓库',
        icon: '📦',
        type: 'secondary',
        priority: 56,
        category: 'inventory',
        action: () => this.sceneManager.showScene('inventory')
      },
      {
        text: '丹方炼制',
        icon: '⚗️',
        type: 'green',
        priority: 54,
        category: 'alchemy',
        action: () => this.sceneManager.showScene('danfang')
      },
      
      // 剑道系统
      {
        text: '剑心',
        icon: '💎',
        type: 'blue',
        priority: 50,
        category: 'enhancement',
        action: () => this.sceneManager.showScene('swordHeart')
      },
      {
        text: '剑心详情',
        icon: '💫',
        type: 'blue',
        priority: 48,
        category: 'enhancement',
        action: () => this.sceneManager.showScene('swordHeartDetail')
      },
      {
        text: '剑骨',
        icon: '🦴',
        type: 'secondary',
        priority: 46,
        category: 'enhancement',
        action: () => this.sceneManager.showScene('swordBone')
      },
      
      // PVP和挑战系统
      {
        text: '竞技场',
        icon: '🏟️',
        type: 'danger',
        priority: 40,
        category: 'pvp',
        action: () => this.sceneManager.showScene('arena')
      },
      {
        text: '挂机战斗',
        icon: '⚡',
        type: 'warning',
        priority: 38,
        category: 'battle',
        action: () => this.startIdleBattle()
      },
      
      // 抽卡和古宝系统
      {
        text: '古宝抽取',
        icon: '🎁',
        type: 'warning',
        priority: 35,
        category: 'gacha',
        action: () => this.sceneManager.showScene('treasureDraw')
      },
      {
        text: '古宝系统',
        icon: '💰',
        type: 'orange',
        priority: 32,
        category: 'treasure',
        action: () => this.sceneManager.showScene('treasure')
      },
      
      // 仙友系统
      {
        text: '仙友系统',
        icon: '👫',
        type: 'purple',
        priority: 30,
        category: 'xianyou',
        action: () => this.sceneManager.showScene('xianyou')
      },
      
      // 社交和系统功能
      {
        text: '邮箱',
        icon: '📧',
        type: 'blue',
        priority: 25,
        category: 'system',
        action: () => this.sceneManager.showScene('mail')
      },
      {
        text: '充值',
        icon: '💳',
        type: 'danger',
        priority: 20,
        category: 'payment',
        action: () => this.sceneManager.showScene('recharge')
      },
      {
        text: 'VIP特权',
        icon: '👑',
        type: 'warning',
        priority: 18,
        category: 'vip',
        action: () => this.sceneManager.showScene('vipPrivilege')
      },
      
      // 删除了功能大全和功能详情按钮
    ];

    // 创建按钮并添加到网格布局
    this.functionButtons = [];
    buttonConfigs.forEach(config => {
      // 暂时使用EnhancedButton，保持6列布局的小尺寸设计
      const button = new EnhancedButton({
        x: 0, // 位置由网格管理器控制
        y: 0,
        width: 60, // 更小的按钮适合6列布局
        height: 80,
        text: config.text,
        icon: config.icon,
        type: config.type,
        onClick: config.action
      });

      this.functionButtons.push(button);
      
      // 添加到网格布局管理器
      this.gridLayoutManager.addElement(button, {
        priority: config.priority,
        category: config.category,
        visible: true
      });
    });

    console.log(`创建了 ${this.functionButtons.length} 个功能按钮`);
  }

  // 原createSystemButtons方法已删除，系统按钮现在集成到createFunctionButtons中

  // 旧的createCloudTestButton方法已被整合到createSystemButtons中

  // 显示云函数测试对话框
  showCloudTestDialog() {
    if (this.cloudTestDialog) {
      return; // 防止重复打开
    }

    console.log('打开云函数测试对话框');
    this.cloudTestDialog = new CloudFunctionTestDialog(
      this.ctx,
      this.screenWidth,
      this.screenHeight,
      () => {
        console.log('关闭云函数测试对话框');
        // 从UI元素列表中移除
        this.removeUIElement(this.cloudTestDialog);
        this.cloudTestDialog = null;
      }
    );
    
    // 添加到UI元素列表
    this.addUIElement(this.cloudTestDialog);
  }
  
  // 显示数据库测试对话框
  showDatabaseTestDialog() {
    if (this.databaseTestDialog) {
      return; // 防止重复打开
    }

    console.log('打开数据库测试对话框');
    this.databaseTestDialog = new DatabaseTestDialog(
      this.ctx,
      this.screenWidth,
      this.screenHeight,
      () => {
        console.log('关闭数据库测试对话框');
        // 从UI元素列表中移除
        this.removeUIElement(this.databaseTestDialog);
        this.databaseTestDialog = null;
      }
    );
    
    // 添加到UI元素列表
    this.addUIElement(this.databaseTestDialog);
  }

  /**
   * 触发数据同步
   */
  async triggerDataSync() {
    try {
      console.log('手动触发数据同步...');
      
      wx.showLoading({
        title: '数据同步中...',
        mask: true
      });

      // 检查游戏状态管理器和数据同步管理器
      if (!game.gameStateManager) {
        throw new Error('游戏状态管理器未初始化');
      }

      if (!game.gameStateManager.dataSyncManager) {
        throw new Error('数据同步管理器未初始化');
      }

      // 检查用户登录状态
      if (!game.user || !game.user.openid) {
        wx.hideLoading();
        wx.showModal({
          title: '提示',
          content: '请先登录游戏后再进行数据同步',
          showCancel: false,
          confirmText: '确定'
        });
        return;
      }

      // 如果未初始化，先初始化
      if (!game.gameStateManager.dataSyncManager.isInitialized) {
        const initSuccess = await game.gameStateManager.dataSyncManager.initialize();
        if (!initSuccess) {
          throw new Error('数据同步初始化失败');
        }
      } else {
        // 如果已初始化，执行手动同步
        const syncSuccess = await game.gameStateManager.dataSyncManager.manualSync();
        if (!syncSuccess) {
          throw new Error('数据同步失败');
        }
      }

      wx.hideLoading();
      
      console.log('数据同步成功');

    } catch (error) {
      console.error('触发数据同步失败:', error);
      wx.hideLoading();
      
      wx.showModal({
        title: '同步失败',
        content: `数据同步失败: ${error.message}`,
        showCancel: false,
        confirmText: '确定'
      });
    }
  }

  /**
   * 初始化玩家数据系统（保留原方法以兼容）
   */
  async initializePlayerData() {
    try {
      console.log('开始初始化玩家数据系统...');
      
      wx.showLoading({
        title: '初始化中...',
        mask: true
      });

      // 检查游戏状态管理器和玩家数据管理器
      if (!game.gameStateManager) {
        throw new Error('游戏状态管理器未初始化');
      }

      if (!game.gameStateManager.playerDataManager) {
        throw new Error('玩家数据管理器未初始化');
      }

      // 调用玩家数据管理器的初始化方法
      const success = await game.gameStateManager.playerDataManager.initializePlayerData();

      wx.hideLoading();

      if (success) {
        wx.showToast({
          title: '数据初始化成功',
          icon: 'success',
          duration: 3000
        });
        
        console.log('玩家数据系统初始化成功');
      } else {
        throw new Error('数据初始化失败');
      }

    } catch (error) {
      console.error('初始化玩家数据失败:', error);
      wx.hideLoading();
      
      wx.showModal({
        title: '初始化失败',
        content: `数据初始化失败: ${error.message}`,
        showCancel: false,
        confirmText: '确定'
      });
    }
  }

  /**
   * 测试数据同步功能
   */
  async testDataSync() {
    try {
      console.log('开始测试数据同步功能...');
      
      if (!game.gameStateManager) {
        throw new Error('游戏状态管理器未初始化');
      }

      wx.showLoading({
        title: '测试中...',
        mask: true
      });

      // 模拟各种数据变化事件
      const testActions = [
        {
          name: '模拟资源变化',
          action: () => {
            // 模拟获得资源
            game.gameStateManager.addResources({
              xianyu: 100,
              lingshi: 200,
              swordIntent: 10
            });
          }
        },
        {
          name: '模拟等级提升',
          action: () => {
            // 模拟玩家升级
            const currentLevel = game.gameStateManager.state.player.level || 1;
            const newLevel = currentLevel + 1;
            const newExp = 0;
            const newPower = (game.gameStateManager.state.player.power || 0) + 100;
            
            game.gameStateManager.state.player.level = newLevel;
            game.gameStateManager.state.player.exp = newExp;
            game.gameStateManager.state.player.power = newPower;
            
            // 触发等级提升事件
            game.gameStateManager.emit('playerLevelUp', {
              newLevel: newLevel,
              newExp: newExp,
              newPower: newPower
            });
          }
        },
        {
          name: '模拟境界突破',
          action: () => {
            // 模拟境界突破
            const realms = ['炼气期一层', '炼气期二层', '炼气期三层', '筑基期一层'];
            const currentRealm = game.gameStateManager.state.player.cultivation_realm || '炼气期一层';
            const currentIndex = realms.indexOf(currentRealm);
            const newRealm = realms[Math.min(currentIndex + 1, realms.length - 1)];
            const newPower = (game.gameStateManager.state.player.power || 0) + 200;
            
            game.gameStateManager.state.player.cultivation_realm = newRealm;
            game.gameStateManager.state.player.power = newPower;
            
            // 触发境界突破事件
            game.gameStateManager.emit('realmBreakthrough', {
              newRealm: newRealm,
              newPower: newPower
            });
          }
        },
        {
          name: '模拟古宝获得',
          action: () => {
            // 模拟获得古宝
            game.gameStateManager.emit('treasureAcquired', {
              treasureId: 'treasure_' + Date.now(),
              name: '测试古宝',
              category: 'weapon',
              rarity: 3,
              level: 1,
              maxLevel: 100,
              star: 0,
              baseAttributes: { attack: 50 },
              currentAttributes: { attack: 50 },
              isEquipped: false
            });
          }
        }
      ];

      // 执行测试动作
      for (let i = 0; i < testActions.length; i++) {
        const testAction = testActions[i];
        console.log(`执行测试: ${testAction.name}`);
        
        try {
          testAction.action();
          console.log(`✓ ${testAction.name} 执行成功`);
        } catch (error) {
          console.error(`✗ ${testAction.name} 执行失败:`, error);
        }
        
        // 添加延迟
        await this.delay(500);
      }

      wx.hideLoading();
      
      wx.showToast({
        title: '数据同步测试完成',
        icon: 'success',
        duration: 3000
      });
      
      console.log('数据同步测试完成，请查看控制台日志和数据库');

    } catch (error) {
      console.error('测试数据同步失败:', error);
      wx.hideLoading();
      
      wx.showToast({
        title: '测试失败: ' + error.message,
        icon: 'none',
        duration: 3000
      });
    }
  }

  /**
   * 测试安全操作功能
   */
  async testSecureOperations() {
    try {
      console.log('开始测试安全操作功能...');
      
      if (!game.gameStateManager || !game.gameStateManager.secureGameManager) {
        wx.showToast({
          title: '安全管理器未初始化',
          icon: 'none',
          duration: 3000
        });
        return;
      }

      const secureManager = game.gameStateManager.secureGameManager;

      wx.showLoading({
        title: '安全测试中...',
        mask: true
      });

      // 测试序列
      const testActions = [
        {
          name: '初始化安全管理器',
          action: async () => {
            if (!secureManager.isInitialized) {
              await secureManager.initialize();
            }
            return '安全管理器已初始化';
          }
        },
        {
          name: '安全添加资源',
          action: async () => {
            const result = await secureManager.addResources({
              xianyu: 100,
              lingshi: 200
            }, 'security_test');
            return `添加成功: 仙玉+100, 灵石+200`;
          }
        },
        {
          name: '安全消耗资源',
          action: async () => {
            const result = await secureManager.consumeResources({
              lingshi: 50
            }, 'security_test');
            return `消耗成功: 灵石-50`;
          }
        },
        {
          name: '尝试安全升级',
          action: async () => {
            try {
              const result = await secureManager.levelUp();
              return `升级成功: ${result.oldLevel} → ${result.newLevel}`;
            } catch (error) {
              return `升级失败: ${error.message}`;
            }
          }
        },
        {
          name: '重新加载数据',
          action: async () => {
            await secureManager.loadPlayerDataFromServer();
            return '数据重新加载完成';
          }
        }
      ];

      // 执行测试
      const results = [];
      for (let i = 0; i < testActions.length; i++) {
        const testAction = testActions[i];
        console.log(`执行安全测试: ${testAction.name}`);
        
        try {
          const result = await testAction.action();
          results.push(`✓ ${testAction.name}: ${result}`);
          console.log(`✓ ${testAction.name}: ${result}`);
        } catch (error) {
          results.push(`✗ ${testAction.name}: ${error.message}`);
          console.error(`✗ ${testAction.name}:`, error);
        }
        
        // 添加延迟避免请求过于频繁
        await this.delay(1000);
      }

      wx.hideLoading();
      
      // 显示测试结果
      const successCount = results.filter(r => r.startsWith('✓')).length;
      const totalCount = results.length;
      
      wx.showModal({
        title: '安全测试完成',
        content: `成功: ${successCount}/${totalCount}\n\n${results.join('\n')}`,
        showCancel: false,
        confirmText: '确定'
      });
      
      console.log('安全操作测试完成:', results);

    } catch (error) {
      console.error('安全操作测试失败:', error);
      wx.hideLoading();
      
      wx.showToast({
        title: '测试失败: ' + error.message,
        icon: 'none',
        duration: 3000
      });
    }
  }

  /**
   * 启动挂机战斗
   */
  startIdleBattle() {
    try {
      // 检查游戏状态管理器
      if (!game.gameStateManager) {
        wx.showToast({
          title: '游戏未初始化',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      
      // 创建默认玩家角色对象
      const playerCharacter = this.createDefaultPlayerCharacter();
      
      // 创建默认地点配置
      const locationConfig = this.createDefaultLocationConfig();
      
      // 启动挂机战斗场景
      this.sceneManager.showScene('idleBattle', {
        playerCharacter: playerCharacter,
        locationConfig: locationConfig
      });
      
    } catch (error) {
      console.error('启动挂机战斗失败:', error);
      wx.showToast({
        title: '启动失败',
        icon: 'none',
        duration: 2000
      });
    }
  }
  
  /**
   * 创建默认玩家角色对象
   */
  createDefaultPlayerCharacter() {
    const playerData = game.gameStateManager.state.player || {};
    
    return {
      // 基础属性
      level: playerData.level || 1,
      name: playerData.nickname || '修仙者',
      
      // 战斗属性
      hp: 1000,
      maxHp: 1000,
      currentHp: 1000,
      attack: 100,
      defense: 50,
      attackSpeed: 1.0,
      
      // 状态
      isAlive: true,
      
      // 技能相关
      skills: [],
      
      // 获取属性方法
      getAttributes: function() {
        return {
          hp: this.hp,
          maxHp: this.maxHp,
          attack: this.attack,
          defense: this.defense,
          attackSpeed: this.attackSpeed
        };
      },
      
      // 获取技能槽方法
      getSkillInSlot: function(slotIndex) {
        // 返回默认技能或null
        if (slotIndex === 1) {
          return {
            id: 'basic_attack',
            name: '基础攻击',
            type: 'active',
            cooldown: 3000,
            lastUsed: 0,
            damage: 120,
            description: '基础攻击技能'
          };
        }
        return null;
      }
    };
  }
  
  /**
   * 创建默认地点配置
   */
  createDefaultLocationConfig() {
    return {
      id: 'bamboo_forest',
      name: '竹林',
      description: '新手修炼地点',
      
      // 普通敌人配置
      normalEnemy: {
        name: '竹林野兽',
        hpBase: 200,
        attackBase: 50,
        defenseBase: 20,
        attackSpeed: 1.0,
        skills: []
      },
      
      // 精英敌人配置
      eliteEnemy: {
        name: '竹林精英',
        hpMultiplier: 3,
        attackMultiplier: 1.5,
        defenseMultiplier: 1.5,
        attackSpeed: 1.2,
        skills: []
      },
      
      // Boss配置
      bossEnemy: {
        name: '竹林王者',
        hpMultiplier: 10,
        attackMultiplier: 2,
        defenseMultiplier: 2,
        attackSpeed: 1.5,
        skills: []
      },
      
      // 奖励配置
      rewards: {
        expBase: 10,
        coinBase: 5,
        dropRates: {
          herb: 0.3,
          material: 0.1
        }
      }
    };
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 创建底部导航栏按钮方法已移除，现在使用drawTabBar方法直接绘制

  // 底部导航栏选中回调
  onTabSelected(index) {
    // 如果点击的是当前选中的项，不做处理
    if (this.selectedTabIndex === index) {
      return;
    }

    // 更新选中的索引
    this.selectedTabIndex = index;

    // 根据索引切换场景
    switch (index) {
      case 0:
        // 主页，已经在主页，不需要切换
        break;
      case 1:
        // 角色页面 - 直接进入女剑仙角色详情页
        this.sceneManager.showScene('characterDetail', { characterId: 1 });
        break;
      case 2:
        // 洞府页面
        this.sceneManager.showScene('dongfu');
        break;
      case 3:
        // 试炼页面
        this.sceneManager.showScene('trial');
        break;
      case 4:
        // 背包页面
        this.sceneManager.showScene('backpack');
        break;
    }
  }

  // 场景显示时的回调
  onShow(params) {
    console.log('MainScene.onShow被调用，参数：', params);

    // 从其他场景返回时，设置正确的选中标签
    if (params && params.from) {
      switch (params.from) {
        case 'index':
          this.selectedTabIndex = 0;
          break;
        case 'character':
          this.selectedTabIndex = 1;
          break;
        case 'dongfu':
          this.selectedTabIndex = 2;
          break;
        case 'trial':
          this.selectedTabIndex = 3;
          break;
        case 'backpack':
          this.selectedTabIndex = 4;
          break;
      }
    }

    // 清空UI元素
    this.clearUIElements();

    // 重置对话框状态
    this.cloudTestDialog = null;
    this.databaseTestDialog = null;

    // 重置功能按钮数组
    this.functionButtons = [];

    // 初始化UI
    this.initUI();

    // 确保TitleBar显示最新的用户信息
    if (this.titleBar) {
      // 重置加载标志，让TitleBar尝试重新加载头像
      this.titleBar.triedLoadingAvatar = false;

      // 手动加载用户头像
      this.titleBar.loadAvatarImage();

      // 如果玩家已经登录，尝试更新TitleBar用户信息
      if (game.user && (game.user.avatarUrl || game.user.nickName)) {
        const userInfo = {
          nickName: game.user.nickName,
          avatarUrl: game.user.avatarUrl
        };
        this.updateTitleBar(userInfo);
      } else if (game.gameStateManager && game.gameStateManager.state.player) {
        // 或者从GameStateManager获取信息
        const player = game.gameStateManager.state.player;
        if (player.avatarUrl || player.nickname) {
          const userInfo = {
            nickName: player.nickname,
            avatarUrl: player.avatarUrl
          };
          this.updateTitleBar(userInfo);
        }
      }
    }

    // 设置为可见
    this.visible = true;
  }

  // 场景隐藏时的回调
  onHide() {
    // 清空UI元素
    this.clearUIElements();

    // 重置拖动状态
    this.isDragging = false;

    // 设置场景为不可见
    this.visible = false;

    console.log('MainScene隐藏');
  }

  // 子类实现的触摸开始事件处理
  handleTouchStart(x, y) {
    // 优先处理网格布局管理器的触摸事件
    if (this.gridLayoutManager) {
      const handled = this.gridLayoutManager.handleTouch(x, y, 'touchstart');
      if (handled) {
        return true;
      }
    }

    // 检查是否在可滚动区域内
    const safeAreaHeight = 40; // 安全区域高度
    const headerHeight = 80; // 顶部导航栏高度
    const totalHeaderHeight = safeAreaHeight + headerHeight;
    const tabBarHeight = 80; // 底部导航栏高度

    if (y > totalHeaderHeight && y < this.screenHeight - tabBarHeight) {
      this.isDragging = true;
      this.lastY = y;
      return true;
    }

    return false;
  }

  // 子类实现的触摸移动事件处理
  handleTouchMove(x, y) {
    // 处理网格布局管理器的触摸移动
    if (this.gridLayoutManager) {
      const handled = this.gridLayoutManager.handleTouch(x, y, 'touchmove');
      if (handled) {
        return true;
      }
    }

    if (this.isDragging) {
      // 计算滚动距离
      const deltaY = y - this.lastY;
      this.bgScrollY -= deltaY;

      // 限制滚动范围
      const maxScroll = 500; // 最大滚动距离
      this.bgScrollY = Math.max(0, Math.min(this.bgScrollY, maxScroll));

      this.lastY = y;
      return true;
    }

    return false;
  }

  // 子类实现的触摸结束事件处理
  handleTouchEnd(x, y) {
    // 处理网格布局管理器的触摸结束
    if (this.gridLayoutManager) {
      const handled = this.gridLayoutManager.handleTouch(x, y, 'touchend');
      if (handled) {
        return true;
      }
    }

    // 检查是否点击了底部导航栏
    const tabBarHeight = 80;
    const tabBarY = this.screenHeight - tabBarHeight;
    
    if (y >= tabBarY && y <= this.screenHeight) {
      const tabButtonWidth = this.screenWidth / 5;
      const tabIndex = Math.floor(x / tabButtonWidth);
      
      if (tabIndex >= 0 && tabIndex < 5) {
        this.onTabSelected(tabIndex);
        return true;
      }
    }

    if (this.isDragging) {
      this.isDragging = false;
      return true;
    }

    return false;
  }

  // 子类实现的更新逻辑
  updateScene(deltaTime) {
    // 如果已初始化，更新标题栏
    if (this.titleBar) {
      this.titleBar.update();
    }

    // 更新网格布局管理器（包含按钮动画）
    if (this.gridLayoutManager) {
      this.gridLayoutManager.update(deltaTime);
    }

    // 单独更新功能按钮的动画
    if (this.functionButtons) {
      this.functionButtons.forEach(button => {
        if (button && button.update) {
          button.update(deltaTime);
        }
      });
    }

    // 检查和更新静室修炼进度（只有在登录完成后才执行）
    if (typeof JingshiScene !== 'undefined' &&
        JingshiScene.meditationStarted &&
        game.loginManager &&
        game.loginManager.isLoginComplete()) {
      JingshiScene.checkAndUpdateMeditation();
    }
  }

  // 子类实现的绘制逻辑
  drawScene() {
    // 绘制背景
    this.drawBackground();

    // 绘制顶部安全区域（防止被摄像头和微信按钮遮挡）
    this.drawSafeArea();

    // 绘制网格布局中的功能按钮
    if (this.gridLayoutManager) {
      this.gridLayoutManager.render(this.ctx);
    }

    // 绘制底部导航栏
    this.drawTabBar();
  }

  // 绘制背景
  drawBackground() {
    // 如果有背景图资源，使用背景图
    if (this.resources && this.resources.mainBg) {
      try {
        // 确保背景图覆盖整个屏幕，包括底部导航栏区域
        const bgWidth = this.screenWidth;
        const bgHeight = this.screenHeight; // 使用屏幕高度确保完全覆盖

        this.ctx.drawImage(
          this.resources.mainBg,
          0,
          0,
          bgWidth,
          bgHeight
        );
      } catch (error) {
        console.error('绘制背景图失败', error);
        this.drawDefaultBackground();
      }
    } else {
      // 如果没有背景图资源，使用渐变色背景
      this.drawDefaultBackground();
    }
  }

  // 绘制默认背景
  drawDefaultBackground() {
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, '#1a2a6c');
    gradient.addColorStop(0.5, '#b21f1f');
    gradient.addColorStop(1, '#fdbb2d');

    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }

  // 绘制顶部安全区域
  drawSafeArea() {
    const safeAreaHeight = 40;
    
    // 绘制黑色安全区域
    this.ctx.fillStyle = '#000000';
    this.ctx.fillRect(0, 0, this.screenWidth, safeAreaHeight);
    
    // 可选：添加渐变效果让过渡更自然
    const gradient = this.ctx.createLinearGradient(0, safeAreaHeight - 10, 0, safeAreaHeight);
    gradient.addColorStop(0, 'rgba(0, 0, 0, 1)');
    gradient.addColorStop(1, 'rgba(0, 0, 0, 0)');
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, safeAreaHeight - 10, this.screenWidth, 10);
  }

  // 绘制圆角矩形的辅助方法
  drawRoundedRect(x, y, width, height, radius) {
    this.ctx.beginPath();
    this.ctx.moveTo(x + radius, y);
    this.ctx.lineTo(x + width - radius, y);
    this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    this.ctx.lineTo(x + width, y + height - radius);
    this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    this.ctx.lineTo(x + radius, y + height);
    this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    this.ctx.lineTo(x, y + radius);
    this.ctx.quadraticCurveTo(x, y, x + radius, y);
    this.ctx.closePath();
  }

  // 绘制顶部导航栏
  drawHeader() {
    // 该方法已被TitleBar组件替代，不再需要执行任何操作
    // 为了兼容性保留方法
  }

  // 旧的createMainButtons方法已被createFunctionButtons替代

  // 旧的drawMainButtons方法已不再需要，功能按钮现在通过网格布局管理器处理

  // 绘制底部导航栏
  drawTabBar() {
    const tabBarHeight = 80; // 增加高度以容纳更大的图标
    const tabBarY = this.screenHeight - tabBarHeight;

    // 不绘制背景，保持透明以显示背景图

    // 绘制导航按钮
    const tabButtonWidth = this.screenWidth / 5;
    const tabIcons = ['home', 'character', 'dongfu', 'trial', 'backpack'];
    const tabTexts = ['主页', '角色', '洞府', '试炼', '背包'];
    // 使用emoji图标作为更好的视觉效果
    const tabEmojis = ['🏠', '👤', '🏔️', '⚔️', '🎒'];

    for (let i = 0; i < 5; i++) {
      const x = i * tabButtonWidth;
      const y = tabBarY;
      const isSelected = i === this.selectedTabIndex;

      // 绘制按钮背景（类似按钮的圆角矩形）
      const buttonPadding = 8;
      const buttonX = x + buttonPadding;
      const buttonY = y + 5;
      const buttonWidth = tabButtonWidth - buttonPadding * 2;
      const buttonHeight = tabBarHeight - 10;
      const borderRadius = 12;

      // 绘制按钮背景
      this.ctx.fillStyle = isSelected ? 'rgba(255, 215, 0, 0.2)' : 'rgba(255, 255, 255, 0.1)';
      this.drawRoundedRect(buttonX, buttonY, buttonWidth, buttonHeight, borderRadius);
      this.ctx.fill();

      // 绘制按钮边框
      this.ctx.strokeStyle = isSelected ? 'rgba(255, 215, 0, 0.5)' : 'rgba(255, 255, 255, 0.3)';
      this.ctx.lineWidth = 1;
      this.drawRoundedRect(buttonX, buttonY, buttonWidth, buttonHeight, borderRadius);
      this.ctx.stroke();

      // 绘制图标
      const iconSize = isSelected ? 32 : 28; // 选中时图标更大
      const iconX = x + (tabButtonWidth - iconSize) / 2;
      const iconY = y + 8;

      // 绘制emoji图标
      this.ctx.font = `${iconSize}px Arial`;
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'top';
      this.ctx.fillStyle = isSelected ? '#FFD700' : '#ffffff'; // 选中时金色
      this.ctx.fillText(tabEmojis[i], iconX + iconSize / 2, iconY);

      // 绘制文本
      this.ctx.font = '11px Arial';
      this.ctx.fillStyle = isSelected ? '#FFD700' : '#ffffff'; // 选中时金色文字
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'top';
      this.ctx.fillText(tabTexts[i], x + tabButtonWidth / 2, y + iconSize + 16);
    }
  }

  // 绘制底部导航栏图标
  drawTabIcon(iconKey, x, y, size, tabType, isSelected) {
    if (this.resources && this.resources[iconKey]) {
      try {
        this.ctx.drawImage(
          this.resources[iconKey],
          x,
          y,
          size,
          size
        );
      } catch (error) {
        console.error(`绘制导航图标 ${iconKey} 失败`, error);
        this.drawDefaultTabIcon(x, y, size, tabType, isSelected);
      }
    } else {
      // 如果没有图标资源，绘制默认图标
      this.drawDefaultTabIcon(x, y, size, tabType, isSelected);
    }
  }

  // 绘制默认底部导航栏图标
  drawDefaultTabIcon(x, y, size, tabType, isSelected) {
    // 设置图标颜色
    this.ctx.fillStyle = isSelected ? '#ffffff' : '#cccccc';

    // 根据标签类型绘制不同的简单图形
    switch (tabType) {
      case 'home':
        // 绘制一个简单的房子图标
        this.ctx.beginPath();
        this.ctx.moveTo(x + size / 2, y);
        this.ctx.lineTo(x, y + size / 2);
        this.ctx.lineTo(x + size / 4, y + size / 2);
        this.ctx.lineTo(x + size / 4, y + size);
        this.ctx.lineTo(x + 3 * size / 4, y + size);
        this.ctx.lineTo(x + 3 * size / 4, y + size / 2);
        this.ctx.lineTo(x + size, y + size / 2);
        this.ctx.closePath();
        this.ctx.fill();
        break;

      case 'character':
        // 绘制一个简单的人物图标
        this.ctx.beginPath();
        this.ctx.arc(x + size / 2, y + size / 3, size / 4, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.beginPath();
        this.ctx.moveTo(x + size / 2, y + size / 2);
        this.ctx.lineTo(x + size / 4, y + size);
        this.ctx.lineTo(x + 3 * size / 4, y + size);
        this.ctx.closePath();
        this.ctx.fill();
        break;

      case 'dongfu':
        // 绘制一个简单的洞府图标（山洞）
        this.ctx.beginPath();
        this.ctx.moveTo(x, y + size);
        this.ctx.lineTo(x, y + size / 2);
        this.ctx.quadraticCurveTo(x + size / 2, y, x + size, y + size / 2);
        this.ctx.lineTo(x + size, y + size);
        this.ctx.closePath();
        this.ctx.fill();
        break;

      case 'trial':
        // 绘制一个简单的宝剑图标
        this.ctx.beginPath();
        this.ctx.moveTo(x + size / 2, y);
        this.ctx.lineTo(x + size / 3, y + size * 0.8);
        this.ctx.lineTo(x + size * 2 / 3, y + size * 0.8);
        this.ctx.closePath();
        this.ctx.fill();

        this.ctx.fillRect(x + size / 3, y + size * 0.8, size / 3, size * 0.2);
        break;

      case 'backpack':
        // 绘制一个简单的背包图标
        this.ctx.beginPath();
        this.ctx.moveTo(x + size * 0.2, y + size * 0.3);
        this.ctx.lineTo(x + size * 0.8, y + size * 0.3);
        this.ctx.lineTo(x + size * 0.8, y + size * 0.9);
        this.ctx.lineTo(x + size * 0.2, y + size * 0.9);
        this.ctx.closePath();
        this.ctx.fill();

        // 绘制背包提手
        this.ctx.beginPath();
        this.ctx.moveTo(x + size * 0.3, y + size * 0.3);
        this.ctx.quadraticCurveTo(x + size / 2, y + size * 0.1, x + size * 0.7, y + size * 0.3);
        this.ctx.stroke();
        break;
    }
  }

  /**
   * 处理登录按钮点击
   */
  handleLoginButton() {
    if (!game || !game.loginManager) {
      console.error('登录管理器未初始化');
      wx.showToast({
        title: '系统错误',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 检查当前登录状态
    const loginStatus = game.loginManager.getLoginStatus();

    if (loginStatus.isLoggedIn) {
      wx.showToast({
        title: '已登录',
        icon: 'success',
        duration: 2000
      });
      return;
    }

    // 开始手动登录流程
    game.loginManager.manualLogin();
  }

  /**
   * 添加登录方法（保留旧方法以兼容）
   */
  async loginUser() {
    if (!game || !game.cloudInited) {
      console.error('云环境未初始化，无法登录');
      wx.showToast({
        title: '云环境未初始化',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    console.log('尝试登录用户...');
    wx.showLoading({
      title: '登录中...',
      mask: true
    });

    // 即使用户已有openid，也应该从服务器获取最新数据
    if (game.user && game.user.openid) {
      console.log('用户已有openid，正在获取最新数据:', game.user.openid);
      this.loadUserDataFromCloud(game.user.openid);
      return;
    }

    // 直接检查本地存储中的授权信息
    try {
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo && userInfo.authorized) {
        console.log('用户已授权，直接登录');
        this.proceedToLogin();
        return;
      }
    } catch (err) {
      console.error('获取本地存储授权信息失败', err);
    }

    // 检查game对象中是否已有授权标记
    if (game.userAuthorized) {
      console.log('game.userAuthorized为true，直接登录');
      this.proceedToLogin();
      return;
    }

    // 使用wx.getSetting检查
    wx.getSetting({
      success: (res) => {
        const authSetting = res.authSetting;
        console.log('获取授权设置结果:', authSetting);

        // 检查'scope.userInfo'权限
        if (authSetting && authSetting['scope.userInfo'] === true) {
          console.log('用户已授权用户信息，直接登录');
          this.proceedToLogin();
        } else {
          // 未授权，显示授权请求
          wx.hideLoading();
          console.log('用户未授权，显示授权请求');
          this.requestUserInfo();
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('获取用户授权设置失败:', err);
        // 失败时也尝试直接请求授权
        this.requestUserInfo();
      }
    });
  }

  /**
   * 从云端加载用户数据
   */
  loadUserDataFromCloud(openid) {
    console.log('从云端加载用户数据，openid:', openid);

    // 查询用户是否已经在数据库中存在
    game.db.collection('xiuxian-player').where({
      openid: openid
    }).get().then(res => {
      wx.hideLoading();

      if (res.data && res.data.length > 0) {
        console.log('云端发现用户数据，载入用户数据');
        const userData = res.data[0];

        // 更新本地用户信息
        game.user = { ...game.user, ...userData };

        // 如果有gameState数据，载入到游戏状态
        if (userData.gameState) {
          console.log('从云端载入游戏数据:', userData.gameState);

          try {
            // 在加载前记录之前的状态，用于日志对比
            const previousResources = game.gameStateManager.state.player?.resources
              ? JSON.stringify(game.gameStateManager.state.player.resources)
              : 'undefined';

            // 加载游戏状态
            game.gameStateManager.loadGameState(userData.gameState);

            // 确保资源数据正确同步
            if (userData.gameState.player && userData.gameState.player.resources) {
              console.log('同步玩家资源数据:', userData.gameState.player.resources);

              // 确保player对象存在
              if (!game.gameStateManager.state.player) {
                game.gameStateManager.state.player = {};
              }

              // 直接强制更新资源数据
              game.gameStateManager.state.player.resources = { ...userData.gameState.player.resources };

              // 记录资源更新日志
              console.log('资源数据更新:', '之前:', previousResources, '之后:', JSON.stringify(game.gameStateManager.state.player.resources));

              // 触发玩家数据更新事件
              game.gameStateManager.emit('playerDataChanged', game.gameStateManager.state.player);
            }
          } catch (error) {
            console.error('载入游戏数据失败:', error);
          }
        }

        wx.showToast({
          title: '数据同步成功',
          icon: 'success',
          duration: 2000
        });

        // 强制保存游戏状态
        game.gameStateManager.saveGameState();

        // 刷新主场景UI
        this.clearUIElements();
        this.initUI();

        // 手动调用绘制方法确保UI更新
        this.drawScene();
      } else {
        console.log('云端未找到用户数据，保存当前数据');
        // 保存当前游戏数据到云端
        this.savePlayerData();

        wx.showToast({
          title: '数据已上传',
          icon: 'success',
          duration: 2000
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('查询用户数据失败', err);
      wx.showToast({
        title: '数据同步失败',
        icon: 'none',
        duration: 2000
      });
    });
  }

  /**
   * 请求用户信息和授权
   */
  requestUserInfo() {
    // 使用微信原生的getUserProfile API
    wx.getUserProfile({
      desc: '用于完善会员资料', // 声明获取用户个人信息后的用途
      lang: 'zh_CN',
      success: (res) => {
        console.log('获取用户信息成功:', res.userInfo);

        // 保存用户信息
        this.setUserInfo(res.userInfo);

        // 继续登录流程
        this.proceedToLogin();
      },
      fail: (err) => {
        console.error('用户拒绝授权:', err);
        wx.showModal({
          title: '授权提醒',
          content: '需要您的授权才能保存游戏进度，请重新点击按钮并允许授权',
          showCancel: false,
          confirmText: '我知道了'
        });
      }
    });
  }

  /**
   * 保存用户信息
   */
  setUserInfo(userInfo) {
    // 确保game.user存在
    game.user = game.user || {};

    // 更新用户信息
    game.user.nickName = userInfo.nickName;
    game.user.avatarUrl = userInfo.avatarUrl;
    game.user.gender = userInfo.gender;
    game.userAuthorized = true;

    // 将用户信息同步到GameStateManager
    if (game.gameStateManager) {
      if (!game.gameStateManager.state.player) {
        game.gameStateManager.state.player = {};
      }

      // 确保player对象包含基本信息
      game.gameStateManager.state.player.nickname = userInfo.nickName || '无名修士';
      game.gameStateManager.state.player.avatarUrl = userInfo.avatarUrl;

      console.log('已将用户信息同步到GameStateManager', {
        nickname: game.gameStateManager.state.player.nickname
      });

      // 更新GameStateManager中的玩家数据
      game.gameStateManager.setPlayer(game.gameStateManager.state.player);
    }

    // 将用户数据存储到本地以便下次启动使用
    try {
      wx.setStorageSync('userInfo', {
        nickName: userInfo.nickName,
        avatarUrl: userInfo.avatarUrl,
        gender: userInfo.gender,
        authorized: true,
        timestamp: Date.now()
      });
      console.log('用户信息已保存到本地存储');
    } catch (err) {
      console.error('保存用户信息到本地失败:', err);
    }

    // 更新TitleBar显示
    this.updateTitleBar(userInfo);
  }

  /**
   * 更新TitleBar显示
   */
  updateTitleBar(userInfo) {
    // 查找TitleBar组件并更新
    if (this.titleBar && this.titleBar.updateUserInfo) {
      console.log('正在更新TitleBar用户信息');
      this.titleBar.updateUserInfo(userInfo);
    } else {
      console.warn('未找到TitleBar组件或updateUserInfo方法');
    }
  }

  /**
   * 继续登录流程
   */
  proceedToLogin() {
    // 获取用户OpenID
    game.cloud.callFunction({
      name: 'login',
      success: res => {
        console.log('获取用户OpenID成功', res.result);
        const openid = res.result.openid;
        if (openid) {
          // 设置用户基本信息
          game.user = game.user || {};
          game.user.openid = openid;

          // 同步openid到玩家数据
          if (game.gameStateManager && game.gameStateManager.state && game.gameStateManager.state.player) {
            game.gameStateManager.state.player.openid = openid;
          }

          // 查询用户是否已经在数据库中存在
          game.db.collection('xiuxian-player').where({
            openid: openid
          }).get().then(res => {
            wx.hideLoading();

            if (res.data && res.data.length > 0) {
              console.log('用户数据已存在，载入用户数据');
              const userData = res.data[0];

              // 更新本地用户信息
              game.user = { ...game.user, ...userData };

              // 如果有gameState数据，载入到游戏状态
              if (userData.gameState) {
                console.log('从云端载入游戏数据:', userData.gameState);

                try {
                  // 在加载前记录之前的状态，用于日志对比
                  const previousResources = game.gameStateManager.state.player?.resources
                    ? JSON.stringify(game.gameStateManager.state.player.resources)
                    : 'undefined';

                  // 加载游戏状态
                  game.gameStateManager.loadGameState(userData.gameState);

                  // 确保资源数据正确同步
                  if (userData.gameState.player && userData.gameState.player.resources) {
                    console.log('同步玩家资源数据:', userData.gameState.player.resources);

                    // 确保player对象存在
                    if (!game.gameStateManager.state.player) {
                      game.gameStateManager.state.player = {};
                    }

                    // 直接强制更新资源数据
                    game.gameStateManager.state.player.resources = { ...userData.gameState.player.resources };

                    // 记录资源更新日志
                    console.log('资源数据更新:', '之前:', previousResources, '之后:', JSON.stringify(game.gameStateManager.state.player.resources));

                    // 触发玩家数据更新事件
                    game.gameStateManager.emit('playerDataChanged', game.gameStateManager.state.player);
                  }

                  // 更新TitleBar显示
                  if (userData.gameState.player) {
                    const userInfo = {
                      nickName: userData.gameState.player.nickname || userData.nickName,
                      avatarUrl: userData.gameState.player.avatarUrl || userData.avatarUrl
                    };
                    this.updateTitleBar(userInfo);
                  }
                } catch (error) {
                  console.error('载入游戏数据失败:', error);
                }
              }

              wx.showToast({
                title: '登录成功',
                icon: 'success',
                duration: 2000
              });

              // 强制保存游戏状态
              game.gameStateManager.saveGameState();

              // 刷新主场景UI
              this.clearUIElements();
              this.initUI();

              // 手动调用绘制方法确保UI更新
              this.drawScene();
            } else {
              console.log('用户数据不存在，创建新数据');
              // 保存当前游戏数据到云端
              this.savePlayerData();

              wx.showToast({
                title: '欢迎新玩家',
                icon: 'success',
                duration: 2000
              });
            }
          }).catch(err => {
            wx.hideLoading();
            console.error('查询用户数据失败', err);
            wx.showToast({
              title: '登录失败',
              icon: 'none',
              duration: 2000
            });
          });
        } else {
          wx.hideLoading();
          console.error('获取用户OpenID为空');
          wx.showToast({
            title: '登录失败',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: err => {
        wx.hideLoading();
        console.error('获取用户OpenID失败', err);
        wx.showToast({
          title: '登录失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  }

  /**
   * 创建玩家数据 - 直接调用createPlayerData云函数
   */
  async createPlayerData() {
    try {
      console.log('开始创建玩家数据...');
      wx.showLoading({
        title: '创建中...',
        mask: true
      });

      // 获取游戏状态
      const gameStateManager = game.gameStateManager;
      if (!gameStateManager) {
        console.error('游戏状态管理器未初始化');
        wx.hideLoading();
        wx.showToast({
          title: '游戏状态未初始化',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 获取游戏状态数据
      const gameState = gameStateManager.getFullStateForSave();
      
      // 构建玩家数据
      const playerData = {
        nickname: gameState.player?.nickname || '修仙者',
        avatar_url: gameState.player?.avatar_url || '',
        server_id: 1,
        level: gameState.player?.level || 1,
        exp: gameState.player?.exp || 0,
        power: gameState.player?.power || 0,
        cultivation_realm: gameState.player?.cultivation_realm || '炼气期一层',
        dongfu_level: gameState.player?.dongfu_level || 1,
        vip_level: gameState.player?.vip_level || 0,
        total_recharge: gameState.player?.total_recharge || 0,
        last_vip_reward_time: gameState.player?.last_vip_reward_time || null,
        last_offline_time: gameState.player?.last_offline_time || null,
        formation: gameState.player?.formation || [],
        game_settings: gameState.player?.game_settings || {}
      };

      // 获取用户信息
      const userInfo = {
        nickname: game.user?.nickName || '修仙者',
        avatarUrl: game.user?.avatarUrl || ''
      };

      console.log('调用createPlayerData云函数...');
      console.log('玩家数据:', JSON.stringify(playerData));
      console.log('用户信息:', JSON.stringify(userInfo));

      // 调用云函数
      const result = await wx.cloud.callFunction({
        name: 'createPlayerData',
        data: {
          ...playerData,
          userInfo: userInfo
        }
      });

      wx.hideLoading();
      console.log('createPlayerData云函数调用结果:', result);

      if (result.result && result.result.success) {
        const resultData = result.result.data;
        
        if (resultData.action === 'exists') {
          wx.showToast({
            title: '玩家数据已存在',
            icon: 'success',
            duration: 2000
          });
        } else if (resultData.action === 'created') {
          wx.showToast({
            title: '玩家数据创建成功',
            icon: 'success',
            duration: 2000
          });
        }
        
        console.log('玩家数据处理成功，playerId:', resultData.playerId);
        
      } else {
        const error = result.result?.error || result.errMsg || '创建玩家数据失败';
        throw new Error(error);
      }

    } catch (error) {
      console.error('创建玩家失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '创建失败: ' + error.message,
        icon: 'none',
        duration: 2000
      });
    }
  }

  /**
   * 保存玩家数据到云数据库 - 使用新的云函数架构
   */
  async savePlayerData() {
    try {
      // 等待数据库客户端初始化完成
      if (!game.databaseManager) {
        console.error('数据库管理器不存在');
        wx.showToast({
          title: '数据库管理器不存在',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      console.log('等待数据库客户端初始化完成...');
      const isInitialized = await game.databaseManager.waitForInitialization();
      
      if (!isInitialized) {
        console.error('数据库客户端初始化失败，无法保存数据');
        wx.showToast({
          title: '数据库初始化失败',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      console.log('数据库客户端已初始化，开始保存数据...');

      // 检查用户是否已登录
      const openid = game.databaseManager.getCurrentOpenId();
      if (!openid) {
        console.log('用户未登录，先执行登录流程');
        await this.loginUser(); // 添加await等待登录完成
        return;
      }

      // 获取游戏状态
      const gameStateManager = game.gameStateManager;
      if (!gameStateManager) {
        console.error('游戏状态管理器未初始化');
        return;
      }

      console.log('准备使用云函数保存玩家数据...');
      wx.showLoading({
        title: '保存中...',
        mask: true
      });

      // 获取完整的游戏状态数据
      let gameState;
      let playerData; // 将playerData提升到外层作用域
      
      try {
        gameState = gameStateManager.getFullStateForSave();
        console.log('原始游戏状态数据大小:', JSON.stringify(gameState).length, '字符');
        
        // 额外的数据清理步骤 - 移除所有null和undefined值
        gameState = this.deepCleanNullValues(gameState);
        console.log('清理后游戏状态数据大小:', JSON.stringify(gameState).length, '字符');
        
        // 构建符合players表结构的玩家数据（平铺结构）
        playerData = {
          nickname: gameState.player?.nickname || '修仙者',
          avatar_url: gameState.player?.avatar_url || '',
          server_id: 1,
          level: gameState.player?.level || 1,
          exp: gameState.player?.exp || 0,
          power: gameState.player?.power || 0,
          cultivation_realm: gameState.player?.cultivation_realm || '炼气期一层',
          dongfu_level: gameState.player?.dongfu_level || 1,
          vip_level: gameState.player?.vip_level || 0,
          total_recharge: gameState.player?.total_recharge || 0,
          last_vip_reward_time: gameState.player?.last_vip_reward_time || null,
          last_offline_time: gameState.player?.last_offline_time || null,
          formation: gameState.player?.formation || [],
          game_settings: gameState.player?.game_settings || {}
        };
        
        console.log('players表数据:', JSON.stringify(playerData));
        
      } catch (error) {
        console.error('获取游戏状态数据失败:', error);
        wx.showToast({
          title: '无法获取游戏数据',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 获取用户信息（统一使用小写字段名）
      const userInfo = {
        nickname: game.user?.nickName || '修仙者', // 保持小写
        avatarUrl: game.user?.avatarUrl || ''
      };
      
      console.log('用户信息:', JSON.stringify(userInfo));

      // 使用专门的createPlayerData云函数创建玩家数据（平铺数据结构）
      console.log('调用createPlayerData云函数...');
      const result = await wx.cloud.callFunction({
        name: 'createPlayerData',
        data: {
          ...playerData, // 平铺玩家数据字段
          userInfo: userInfo
        }
      });
      
      console.log('createPlayerData云函数调用结果:', result);
      wx.hideLoading();
      
      if (result.result && result.result.success) {
        // 显示成功消息
        const resultData = result.result.data;
        
        if (resultData.action === 'exists') {
          wx.showToast({
            title: '玩家数据已存在',
            icon: 'success',
            duration: 2000
          });
        } else if (resultData.action === 'created') {
          wx.showToast({
            title: '玩家数据创建成功',
            icon: 'success',
            duration: 2000
          });
        }
        
        console.log('玩家数据处理成功，playerId:', resultData.playerId);
        
        // 更新最后保存时间
        game.gameStateManager.lastSaveTime = Date.now();
        
      } else {
        const error = result.result?.error || result.errMsg || '创建玩家数据失败';
        throw new Error(error);
      }

    } catch (error) {
      console.error('保存失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '保存失败: ' + error.message,
        icon: 'none',
        duration: 2000
      });
    }
  }

  /**
   * 深度清理对象中的null和undefined值
   * @param {*} obj 要清理的对象
   * @returns {*} 清理后的对象
   */
  deepCleanNullValues(obj) {
    if (obj === null || obj === undefined) {
      return {};
    }

    if (Array.isArray(obj)) {
      // 对数组进行递归清理，过滤掉null/undefined元素
      return obj.filter(item => item !== null && item !== undefined)
                .map(item => this.deepCleanNullValues(item));
    }

    if (typeof obj === 'object') {
      const cleaned = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          const value = obj[key];
          // 跳过null和undefined值
          if (value !== null && value !== undefined) {
            if (typeof value === 'object') {
              const cleanedValue = this.deepCleanNullValues(value);
              // 只有当清理后的对象不为空时才添加
              if (Array.isArray(cleanedValue) ? cleanedValue.length > 0 : Object.keys(cleanedValue).length > 0) {
                cleaned[key] = cleanedValue;
              }
            } else {
              cleaned[key] = value;
            }
          }
        }
      }
      return cleaned;
    }

    return obj;
  }
}

export default MainScene;