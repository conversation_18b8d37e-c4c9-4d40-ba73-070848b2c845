# 数据库测试系统使用指南

## 概述

数据库测试系统是一个全面的数据库操作测试工具，支持对20个数据表进行完整的CRUD操作测试。系统已根据官方API格式进行优化，确保与微信小游戏云开发数据库的完全兼容。

## 功能特性

### 🎯 核心功能
- **完整CRUD支持**：创建(CREATE)、查询(GET)、更新(UPDATE)、删除(DELETE)、列表(LIST)
- **官方API格式**：完全采用官方推荐的查询和操作语法
- **环境切换**：支持正式环境(prod)和体验环境(pre)
- **模板数据**：预设测试数据模板，快速填充测试内容
- **实时结果**：即时显示操作结果和详细信息

### 📊 支持的数据表

#### 核心数据表
- `players` - 玩家基础信息表
- `player_res` - 玩家资源表  
- `characters` - 角色表
- `player_treasures` - 玩家古宝表
- `player_skills` - 玩家技能表
- `player_items` - 背包物品表
- `sword_hearts` - 剑心系统表
- `sword_bones` - 剑骨系统表

#### 系统功能表
- `player_dongf` - 洞府系统表
- `player_arena` - 竞技场数据表
- `player_idle` - 挂机游历表
- `p_skill_cul` - 技能修炼表

#### 交易记录表
- `recharge_rec` - 充值记录表
- `gacha_records` - 抽卡记录表
- `battle_records` - 战斗记录表

#### 邮件通知表
- `mail_temp` - 邮件模板表
- `player_mails` - 玩家邮件表

#### 活动任务表
- `daily_tasks` - 每日任务表
- `activity_participation` - 活动参与表

#### 系统配置表
- `game_configs` - 游戏配置表

## 使用方法

### 1. 启动测试工具

在主页面点击"数据库测试"按钮，打开数据库测试对话框。

### 2. 选择数据表

从20个可用数据表中选择要测试的表，每个表都有对应的测试数据模板。

### 3. 选择操作类型

#### 查询操作 (GET)
- **用途**：根据ID查询单条记录或查询所有记录
- **参数**：
  - `_id`：记录ID（可选，留空查询所有）
  - `envType`：环境类型（prod/pre）

#### 创建操作 (CREATE)
- **用途**：创建新的数据记录
- **参数**：
  - 数据字段：根据选中表的模板填写
  - `envType`：环境类型（prod/pre）
- **功能**：点击"使用模板数据"快速填充预设值

#### 更新操作 (UPDATE)
- **用途**：更新现有记录
- **参数**：
  - `_id`：要更新的记录ID
  - 数据字段：要更新的字段值
  - `envType`：环境类型（prod/pre）

#### 删除操作 (DELETE)
- **用途**：删除指定记录
- **参数**：
  - `_id`：要删除的记录ID
  - `envType`：环境类型（prod/pre）

#### 列表操作 (LIST)
- **用途**：列出当前用户的所有记录
- **参数**：
  - `envType`：环境类型（prod/pre）

### 4. 设置参数

#### 环境选择
- **正式环境 (prod)**：连接到生产数据库
- **体验环境 (pre)**：连接到测试数据库

#### 数据输入
根据操作类型和选中的表，系统会显示相应的输入字段。可以手动输入或使用模板数据。

### 5. 执行测试

点击"执行测试"按钮开始操作，系统会：
1. 验证输入参数
2. 调用databaseService云函数
3. 显示详细的执行结果

## 预设测试数据

### sword_bones 表
```json
{
  "level": 1,
  "rank": 1,
  "total_attributes": { "attack": 100, "defense": 50 },
  "upgrade_materials_used": { "iron": 10, "gold": 5 }
}
```

### players 表
```json
{
  "nickname": "测试玩家",
  "level": 10,
  "exp": 1000,
  "power": 500,
  "cultivation_realm": "筑基期一层"
}
```

### player_res 表
```json
{
  "xianyu": 2000,
  "lingshi": 3000,
  "sword_intent": 100
}
```

## 技术实现

### 官方API格式

系统完全采用官方推荐的API格式：

#### 查询操作
```javascript
const { data } = await models.sword_bones.get({
  filter: {
    where: {
      $and: [
        {
          _id: {
            $eq: _id
          }
        }
      ]
    }
  },
  envType: "prod"
});
```

#### 创建操作
```javascript
const { data } = await models.sword_bones.create({
  data: {
    level: 1,
    upgrade_materials_used: {},
    rank: 1,
    total_attributes: {}
  },
  envType: "prod"
});
```

#### 更新操作
```javascript
const { data } = await models.sword_bones.update({
  data: {
    level: 1,
    upgrade_materials_used: {},
    rank: 1,
    total_attributes: {}
  },
  filter: {
    where: {
      $and: [
        {
          _id: {
            $eq: 'xxxx'
          }
        }
      ]
    }
  },
  envType: "prod"
});
```

### 云函数架构

- **databaseService**：统一的数据库操作云函数
- **参数验证**：完整的数据校验机制
- **错误处理**：详细的错误信息和状态码
- **安全控制**：基于openid的用户数据隔离

## 结果解读

### 成功响应格式
```json
{
  "success": true,
  "data": {
    "records": [...],  // 查询结果
    "count": 1,        // 记录数量
    "id": "xxx",       // 创建的记录ID
    "message": "操作成功"
  },
  "timestamp": 1234567890
}
```

### 错误响应格式
```json
{
  "success": false,
  "error": "错误描述",
  "code": "ERROR_CODE",
  "timestamp": 1234567890
}
```

## 常见问题

### Q1: 查询返回空结果？
**A**: 检查以下几点：
- 确认选择了正确的环境(prod/pre)
- 确认用户已登录获得openid
- 确认_id格式正确（如果指定了ID）

### Q2: 创建操作失败？
**A**: 可能原因：
- 数据格式不正确
- 必填字段缺失
- 字段长度超出限制
- 数值字段为负数

### Q3: 环境切换无效？
**A**: 确保：
- 云函数已部署到对应环境
- 数据库表在目标环境中存在
- 用户在目标环境中有相应权限

## 最佳实践

1. **先测试查询**：使用GET操作确认表结构和现有数据
2. **使用模板数据**：利用预设模板快速填充测试数据
3. **环境隔离**：开发时使用pre环境，生产使用prod环境
4. **记录ID管理**：保存创建操作返回的ID用于后续测试
5. **数据清理**：测试完成后清理测试数据

## 技术支持

如遇到问题，请检查：
1. 云函数部署状态
2. 数据库权限配置
3. 网络连接状态
4. 控制台错误日志

系统会在控制台输出详细的调试信息，有助于问题定位和解决。 