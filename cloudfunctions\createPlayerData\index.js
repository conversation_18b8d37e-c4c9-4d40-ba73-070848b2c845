// 云函数入口文件 - 专门创建玩家数据（MySQL数据库版本）
const cloudbase = require("@cloudbase/node-sdk")

// 指定云开发环境 ID
const app = cloudbase.init({
  env: "cloud1-9gzbxxbff827656f",
})

// 引入微信云开发SDK获取用户身份
const cloud = require('wx-server-sdk')
cloud.init({
  env: 'cloud1-9gzbxxbff827656f'
})

// 云函数入口函数
exports.main = async (event, context) => {
  console.log('createPlayerData云函数被调用')
  console.log('接收到的参数:', JSON.stringify(event))
  
  // 获取数据模型实例
  const models = app.models
  
  // 使用微信云开发SDK获取用户身份
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  if (!openid) {
    console.error('未获取到用户openid')
    console.error('wxContext:', wxContext)
    return {
      success: false,
      error: '用户身份验证失败',
      code: 'UNAUTHORIZED'
    }
  }
  
  console.log('用户openid:', openid)
  
  try {
    // 获取传入的数据（平铺结构）
    const { userInfo = {}, ...playerData } = event
    
    console.log('玩家数据:', JSON.stringify(playerData))
    console.log('用户信息:', JSON.stringify(userInfo))
    
    // 玩家数据校验
    if (playerData.server_id && playerData.server_id < 1) {
      throw new Error('无效的服务器ID')
    }
    
    // 使用 openid 作为唯一标识查询现有玩家
    console.log('检查玩家是否已存在...')
    
    try {
      // 查询现有玩家数据 - 使用_openid字段
      const existingPlayerResult = await models.players.where({
        _openid: openid
      }).find()
      
      console.log('查询现有玩家结果:', existingPlayerResult)
      
      if (existingPlayerResult.data && existingPlayerResult.data.length > 0) {
        const existingPlayer = existingPlayerResult.data[0]
        console.log('玩家已存在，返回现有数据')
        
        return {
          success: true,
          data: {
            playerId: existingPlayer.id,
            message: '玩家已存在，无需重复创建',
            action: 'exists',
            player: existingPlayer
          },
          timestamp: Date.now()
        }
      }
    } catch (queryError) {
      console.log('查询玩家时出错:', queryError.message)
      // 如果查询出错，继续尝试创建新玩家
    }
    
    console.log('玩家不存在，开始创建新玩家数据...')
    
    // 构建符合players表结构的完整数据 - 根据datasource约束
    const currentTimestamp = Date.now() // 使用timestamp格式（number类型）
    
    // 数据校验和格式化
    const nickname = (playerData.nickname || userInfo.nickname || '修仙者').slice(0, 50) // 最大长度50
    const avatarUrl = (playerData.avatar_url || userInfo.avatarUrl || '').slice(0, 255) // 最大长度255
    const cultivationRealm = (playerData.cultivation_realm || '炼气期一层').slice(0, 50) // 最大长度50
    
    const completePlayerData = {
      _openid: openid, // 系统字段，用于微信云开发
      nickname: nickname,
      avatar_url: avatarUrl,
      server_id: Math.max(1, playerData.server_id || 1), // 最小值1
      level: Math.max(1, playerData.level || 1), // 最小值1
      exp: Math.max(0, playerData.exp || 0), // 最小值0
      power: Math.max(0, playerData.power || 0), // 最小值0
      cultivation_realm: cultivationRealm,
      dongfu_level: Math.max(1, playerData.dongfu_level || 1), // 最小值1
      vip_level: Math.max(0, playerData.vip_level || 0), // 最小值0
      total_recharge: Math.max(0, playerData.total_recharge || 0), // 最小值0
      last_vip_reward_time: playerData.last_vip_reward_time || currentTimestamp, // number类型timestamp
      last_login_time: currentTimestamp, // number类型timestamp
      last_offline_time: playerData.last_offline_time || currentTimestamp, // number类型timestamp
      registration_time: currentTimestamp, // number类型timestamp
      game_settings: playerData.game_settings || {} // object类型，保持原始格式
    }
    
    console.log('准备创建的完整玩家数据:', JSON.stringify(completePlayerData))
    
    // 使用CloudBase数据模型创建玩家记录
    console.log('开始创建玩家记录到players表...')
    const createResult = await models.players.create({
      data: completePlayerData,
      envType: "prod" // 使用正式生产环境
    })
    
    console.log('玩家数据创建成功，记录ID:', createResult.data.id)
    console.log('players表现在包含新记录')
    
    // 准备返回数据 - 过滤敏感信息
    const responsePlayerData = {
      ...completePlayerData,
      id: createResult.data.id
    }
    delete responsePlayerData._openid // 过滤敏感的openid信息
    
    // 返回成功结果
    return {
      success: true,
      data: {
        playerId: createResult.data.id,
        message: '玩家数据创建成功',
        action: 'created',
        player: responsePlayerData
      },
      timestamp: Date.now()
    }
    
  } catch (error) {
    console.error('完整错误上下文:', error)
    console.error('错误详情:', {
      message: error.message,
      code: error.code,
      stack: error.stack
    })
    
    // 根据错误类型返回不同的错误信息
    let errorCode = 'DATABASE_ERROR'
    let errorMessage = '数据操作失败'
    
    if (error.code === 'ECONNREFUSED') {
      errorCode = 'DATABASE_CONNECTION_FAILED'
      errorMessage = '数据库连接失败'
    } else if (error.code === 'ER_NO_SUCH_TABLE') {
      errorCode = 'TABLE_NOT_FOUND'
      errorMessage = 'players表不存在'
    } else if (error.code === 'ER_DUP_ENTRY') {
      errorCode = 'DUPLICATE_ENTRY'
      errorMessage = '玩家数据已存在'
    } else if (error.message.includes('无效的服务器ID')) {
      errorCode = 'INVALID_SERVER_ID'
      errorMessage = error.message
    } else if (error.message.includes('用户身份验证失败')) {
      errorCode = 'UNAUTHORIZED'
      errorMessage = error.message
    }
    
    return {
      success: false,
      error: errorMessage,
      code: errorCode,
      timestamp: Date.now()
    }
  }
} 