// 云函数入口文件 - 通用数据库操作服务
const cloudbase = require("@cloudbase/node-sdk")

// 指定云开发环境 ID
const app = cloudbase.init({
  env: "cloud1-9gzbxxbff827656f",
})

// 引入微信云开发SDK获取用户身份
const cloud = require('wx-server-sdk')
cloud.init({
  env: 'cloud1-9gzbxxbff827656f'
})

// 数据库表配置
const TABLE_CONFIGS = {
  // 核心数据表
  players: {
    name: 'players',
    description: '玩家基础信息表',
    defaultData: {
      nickname: '修仙者',
      avatar_url: '',
      server_id: 1,
      level: 1,
      exp: 0,
      power: 0,
      cultivation_realm: '炼气期一层',
      dongfu_level: 1,
      vip_level: 0,
      total_recharge: 0,
      last_vip_reward_time: null,
      last_login_time: Date.now(),
      last_offline_time: null,
      registration_time: Date.now(),
      game_settings: {}
    }
  },
  player_res: {
    name: 'player_res',
    description: '玩家资源表',
    defaultData: {
      xianyu: 1000,
      lingshi: 1000,
      sword_intent: 0,
      lianlidian: 100,
      spirit_stone: 0,
      tiangang_stone: 0,
      xiuwei_point: 0,
      arena_point: 0,
      guild_contribution: 0,
      lingli: 0  // 添加灵力字段
    }
  },
  player_treasures: {
    name: 'player_treasures',
    description: '玩家古宝表',
    defaultData: {
      treasure_id: 'treasure_001',
      name: '青铜剑',
      category: 'weapon',
      rarity: 1,
      level: 1,
      max_level: 100,
      star: 0,
      base_attributes: {},
      current_attributes: {},
      is_equipped: false,
      acquired_time: Date.now()
    }
  },
  player_skill: {
    name: 'player_skill',
    description: '玩家技能表',
    defaultData: {
      skill_id: 'skill_001',
      name: '基础剑法',
      type: 'passive',
      quality: 'common',
      level: 1,
      max_level: 10,
      stars: 1,
      power: 0,
      current_attributes: {},
      is_equipped: false,
      total_upgrade_cost: 0
    }
  },
  player_items: {
    name: 'player_items',
    description: '背包物品表',
    defaultData: {
      item_id: 'item_001',
      name: '回血丹',
      type: 'consumable',
      subtype: 'heal',
      quality: 1,
      count: 1,
      max_stack: 999,
      effects: {},
      can_use: true,
      can_sell: true,
      sell_price: 10,
      description: '恢复少量生命值',
      icon: '',
      acquired_time: Date.now()
    }
  },
  sword_hearts: {
    name: 'sword_hearts',
    description: '剑心系统表',
    defaultData: {
      sword_heart_id: 'heart_001',
      name: '初心剑心',
      level: 1,
      max_level: 10,
      advancement_level: 0,
      max_advancement_level: 9,
      sword_intent_invested: 0,
      total_attributes: {},
      color: '#4299e1',
      description: '修炼剑道的初心'
    }
  },
  sword_bones: {
    name: 'sword_bones',
    description: '剑骨系统表',
    defaultData: {
      level: 1,
      rank: 1,
      total_attributes: {},
      upgrade_materials_used: {}
    }
  },
  
  // 系统功能表
  player_dongf: {
    name: 'player_dongf',
    description: '洞府系统表',
    defaultData: {
      level: 1,
      current_lingqi: 0,
      max_lingqi: 1000,
      lingqi_per_hour: 10,
      last_collect_time: null,
      cultivation_start_time: null,
      cultivation_speed_bonus: 1.0,
      buildings: {},
      upgrade_materials: {}
    }
  },
  player_arena: {
    name: 'player_arena',
    description: '竞技场数据表',
    defaultData: {
      current_rank: 10000,
      highest_rank: 10000,
      challenges_used: 0,
      max_challenges: 5,
      last_challenge_time: null,
      last_reset_time: null,
      last_reward_time: null,
      season_wins: 0,
      season_losses: 0,
      total_wins: 0,
      total_losses: 0,
      arena_points: 0
    }
  },
  player_idle: {
    name: 'player_idle',
    description: '挂机游历表',
    defaultData: {
      current_location: 'bamboo_forest',
      start_time: null,
      last_collect_time: null,
      total_time: 0,
      total_kills: 0,
      elite_kills: 0,
      boss_kills: 0,
      location_progress: {},
      offline_rewards: {},
      auto_use_items: {}
    }
  },
  p_skill_cul: {
    name: 'p_skill_cul',
    description: '技能修炼表',
    defaultData: {
      cultivation_tree_id: 'tree_001',
      realm: '炼气期',
      current_level: 0,
      current_layer: 1,
      unlocked_nodes: [],
      total_cost: 0,
      attributes_bonus: {},
      special_effects: []
    }
  },
  
  // 交易和记录表
  recharge_rec: {
    name: 'recharge_rec',
    description: '充值记录表',
    defaultData: {
      order_id: 'order_' + Date.now(),
      amount: 100,
      xianyu_amount: 100,
      bonus_xianyu: 0,
      package_id: 'package_001',
      payment_method: 'wechat',
      status: 'pending',
      transaction_id: null,
      recharge_time: Date.now(),
      callback_time: null
    }
  },
  gacha_record: {
    name: 'gacha_record',
    description: '抽卡记录表',
    defaultData: {
      gacha_type: 'treasure',
      draw_count: 1,
      cost_type: 'xianyu',
      cost_amount: 100,
      results: [],
      is_pity: false,
      pity_count: 0,
      draw_time: Date.now()
    }
  },
  battle_recor: {
    name: 'battle_recor',
    description: '战斗记录表',
    defaultData: {
      battle_type: 'arena',
      opponent_id: null,
      battle_result: 'win',
      battle_rounds: 3,
      damage_dealt: 100,
      damage_taken: 50,
      rewards: {},
      battle_duration: 30,
      battle_data: {},
      battle_time: Date.now()
    }
  },
  
  // 邮件和通知表
  mail_temp: {
    name: 'mail_temp',
    description: '邮件模板表',
    defaultData: {
      template_id: 'temp_' + Date.now(),
      title: '系统邮件',
      content: '欢迎进入修仙世界',
      mail_type: 'system',
      sender: '系统',
      rewards: null,
      has_rewards: false,
      expire_days: 7,
      target_conditions: {},
      is_active: true
    }
  },
  player_mails: {
    name: 'player_mails',
    description: '玩家邮件表',
    defaultData: {
      mail_id: 'mail_' + Date.now(),
      title: '欢迎邮件',
      content: '欢迎进入修仙世界',
      sender: '系统',
      mail_type: 'system',
      rewards: null,
      has_rewards: false,
      is_read: false,
      is_claimed: false,
      is_deleted: false,
      expire_time: Date.now() + 7 * 24 * 60 * 60 * 1000,
      read_time: null,
      claim_time: null,
      delete_time: null
    }
  },
  
  // 活动和任务表
  daily_tasks: {
    name: 'daily_tasks',
    description: '每日任务表',
    defaultData: {
      task_date: new Date().toISOString().split('T')[0],
      tasks: {},
      total_activity: 0,
      claimed_rewards: [],
      is_completed: false
    }
  },
  activity_par: {
    name: 'activity_par',
    description: '活动参与表',
    defaultData: {
      activity_id: 'activity_001',
      activity_type: 'daily_login',
      participation_data: {},
      rewards_claimed: [],
      total_score: 0,
      is_active: true,
      first_join_time: Date.now(),
      last_action_time: Date.now()
    }
  },
  
  // 系统配置表
  game_configs: {
    name: 'game_configs',
    description: '游戏配置表',
    defaultData: {
      config_key: 'test_config',
      config_value: 'test_value',
      config_type: 'string',
      description: '测试配置',
      is_client_visible: 'false',
      version: 1
    }
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  console.log('databaseService云函数被调用')
  console.log('接收到的参数:', JSON.stringify(event))
  
  // 获取用户身份
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  if (!openid) {
    console.error('未获取到用户openid')
    return {
      success: false,
      error: '用户身份验证失败',
      code: 'UNAUTHORIZED'
    }
  }
  
  const { action, tableName, data = {}, conditions = {}, _id, limit = 10, skip = 0, envType = 'prod' } = event
  
  if (!action) {
    return {
      success: false,
      error: '缺少必要参数：action',
      code: 'MISSING_PARAMS'
    }
  }
  
  // 特殊操作不需要tableName参数
  const specialActions = ['saveOfflineTime', 'getServerTime']
  
  if (!specialActions.includes(action)) {
    if (!tableName) {
      return {
        success: false,
        error: '缺少必要参数：tableName',
        code: 'MISSING_PARAMS'
      }
    }
    
    if (!TABLE_CONFIGS[tableName]) {
      return {
        success: false,
        error: `不支持的数据表：${tableName}`,
        code: 'UNSUPPORTED_TABLE'
      }
    }
  }
  
  try {
    const models = app.models
    const currentTime = Date.now()
    
    // 处理特殊操作
    switch (action) {
      case 'saveOfflineTime':
        return await saveOfflineTime(models.players, openid, currentTime, envType)
      
      case 'getServerTime':
        return await getServerTime()
    }
    
    // 处理需要tableName的普通操作
    const table = models[tableName]
    
    switch (action) {
      case 'create':
        return await createRecord(table, tableName, data, openid, currentTime, envType)
      
      case 'read':
      case 'get':
        return await readRecords(table, tableName, conditions, _id, openid, limit, skip, envType)
      
      case 'update':
        return await updateRecord(table, tableName, data, conditions, _id, openid, currentTime, envType)
      
      case 'delete':
        return await deleteRecord(table, tableName, conditions, _id, openid, envType)
      
      case 'list':
        return await listRecords(table, tableName, openid, limit, skip, envType)
      
      case 'count':
        return await countRecords(table, tableName, conditions, openid, envType)
      
      default:
        return {
          success: false,
          error: `不支持的操作：${action}`,
          code: 'UNSUPPORTED_ACTION'
        }
    }
    
  } catch (error) {
    console.error('数据库操作错误:', error)
    
    return {
      success: false,
      error: error.message || '数据库操作失败',
      code: error.code || 'DATABASE_ERROR',
      timestamp: Date.now()
    }
  }
}

// 创建记录 - 使用官方API格式
async function createRecord(table, tableName, data, openid, currentTime, envType) {
  console.log(`创建${tableName}表记录`)
  
  // 获取默认数据并合并用户数据
  const defaultData = TABLE_CONFIGS[tableName].defaultData
  const completeData = {
    ...defaultData,
    ...data,
    _openid: openid,
    createdAt: currentTime,
    updatedAt: currentTime
  }
  
  // 数据校验
  validateData(tableName, completeData)
  
  // 使用官方API格式创建记录
  const result = await table.create({
    data: completeData,
    envType: envType
  })
  
  console.log('创建结果:', result)
  
  return {
    success: true,
    data: {
      id: result.data?.id || result.data,
      record: completeData,
      message: `${TABLE_CONFIGS[tableName].description}创建成功`
    },
    timestamp: Date.now()
  }
}

// 读取记录 - 使用官方API格式
async function readRecords(table, tableName, conditions, _id, openid, limit, skip, envType) {
  console.log(`读取${tableName}表记录`)
  
  let filter = {}
  
  if (_id) {
    // 根据_id查询单条记录
    filter = {
      where: {
        $and: [
          {
            _id: {
              $eq: _id
            }
          }
        ]
      }
    }
  } else {
    // 根据条件查询多条记录
    const whereConditions = [
      {
        _openid: {
          $eq: openid
        }
      }
    ]
    
    // 添加其他查询条件
    Object.keys(conditions).forEach(key => {
      if (key !== '_openid') {
        whereConditions.push({
          [key]: {
            $eq: conditions[key]
          }
        })
      }
    })
    
    filter = {
      where: {
        $and: whereConditions
      }
    }
  }
  
  // 使用官方API格式查询
  const result = await table.get({
    filter: filter,
    envType: envType
  })
  
  console.log('查询结果:', result)
  
  return {
    success: true,
    data: {
      records: result.data || [],
      count: Array.isArray(result.data) ? result.data.length : (result.data ? 1 : 0),
      message: `${TABLE_CONFIGS[tableName].description}查询成功`
    },
    timestamp: Date.now()
  }
}

// 更新记录 - 使用官方API格式
async function updateRecord(table, tableName, data, conditions, _id, openid, currentTime, envType) {
  console.log(`更新${tableName}表记录`)
  
  // 添加更新时间
  const updateData = {
    ...data,
    updatedAt: currentTime
  }
  
  // 数据校验
  validateData(tableName, updateData, true)
  
  let filter = {}
  
  if (_id) {
    // 根据_id更新记录（最安全的方式）
    filter = {
      where: {
        $and: [
          {
            _id: {
              $eq: _id
            }
          },
          {
            _openid: {
              $eq: openid
            }
          }
        ]
      }
    }
  } else {
    // 没有_id时，先查询获取第一条记录的_id，然后更新
    console.log('没有提供_id，先查询记录获取_id')
    
    const whereConditions = [
      {
        _openid: {
          $eq: openid
        }
      }
    ]
    
    // 添加其他查询条件
    Object.keys(conditions).forEach(key => {
      if (key !== '_openid') {
        whereConditions.push({
          [key]: {
            $eq: conditions[key]
          }
        })
      }
    })
    
    // 先查询获取记录
    const queryResult = await table.get({
      filter: {
        where: {
          $and: whereConditions
        }
      },
      envType: envType
    })
    
    console.log('查询结果:', queryResult)
    
    if (!queryResult.data || queryResult.data.length === 0) {
      throw new Error('未找到要更新的记录')
    }
    
    // 获取第一条记录的_id
    const recordToUpdate = Array.isArray(queryResult.data) ? queryResult.data[0] : queryResult.data
    const recordId = recordToUpdate._id
    
    if (!recordId) {
      throw new Error('记录缺少_id字段')
    }
    
    // 使用_id进行精确更新
    filter = {
      where: {
        $and: [
          {
            _id: {
              $eq: recordId
            }
          },
          {
            _openid: {
              $eq: openid
            }
          }
        ]
      }
    }
    
    console.log(`将更新记录ID: ${recordId}`)
  }
  
  // 使用官方API格式更新
  const result = await table.update({
    data: updateData,
    filter: filter,
    envType: envType
  })
  
  console.log('更新结果:', result)
  
  return {
    success: true,
    data: {
      count: result.data?.count || 0,
      recordId: _id || 'auto-detected',
      message: `${TABLE_CONFIGS[tableName].description}更新成功`
    },
    timestamp: Date.now()
  }
}

// 删除记录 - 使用官方API格式
async function deleteRecord(table, tableName, conditions, _id, openid, envType) {
  console.log(`删除${tableName}表记录`)
  
  let filter = {}
  
  if (_id) {
    // 根据_id删除记录（最安全的方式）
    filter = {
      where: {
        $and: [
          {
            _id: {
              $eq: _id
            }
          },
          {
            _openid: {
              $eq: openid
            }
          }
        ]
      }
    }
  } else {
    // 没有_id时，先查询获取第一条记录的_id，然后删除
    console.log('没有提供_id，先查询记录获取_id')
    
    const whereConditions = [
      {
        _openid: {
          $eq: openid
        }
      }
    ]
    
    // 添加其他查询条件
    Object.keys(conditions).forEach(key => {
      if (key !== '_openid') {
        whereConditions.push({
          [key]: {
            $eq: conditions[key]
          }
        })
      }
    })
    
    // 先查询获取记录
    const queryResult = await table.get({
      filter: {
        where: {
          $and: whereConditions
        }
      },
      envType: envType
    })
    
    console.log('查询结果:', queryResult)
    
    if (!queryResult.data || queryResult.data.length === 0) {
      throw new Error('未找到要删除的记录')
    }
    
    // 获取第一条记录的_id
    const recordToDelete = Array.isArray(queryResult.data) ? queryResult.data[0] : queryResult.data
    const recordId = recordToDelete._id
    
    if (!recordId) {
      throw new Error('记录缺少_id字段')
    }
    
    // 使用_id进行精确删除
    filter = {
      where: {
        $and: [
          {
            _id: {
              $eq: recordId
            }
          },
          {
            _openid: {
              $eq: openid
            }
          }
        ]
      }
    }
    
    console.log(`将删除记录ID: ${recordId}`)
  }
  
  // 使用官方API格式删除
  const result = await table.delete({
    filter: filter,
    envType: envType
  })
  
  console.log('删除结果:', result)
  
  return {
    success: true,
    data: {
      count: result.data?.count || 0,
      recordId: _id || 'auto-detected',
      message: `${TABLE_CONFIGS[tableName].description}删除成功`
    },
    timestamp: Date.now()
  }
}

// 列出所有记录 - 使用官方API格式
async function listRecords(table, tableName, openid, limit, skip, envType) {
  console.log(`列出${tableName}表所有记录`)
  
  const filter = {
    where: {
      $and: [
        {
          _openid: {
            $eq: openid
          }
        }
      ]
    }
  }
  
  // 使用官方API格式查询
  const result = await table.get({
    filter: filter,
    envType: envType
  })
  
  console.log('列表查询结果:', result)
  
  return {
    success: true,
    data: {
      records: result.data || [],
      count: Array.isArray(result.data) ? result.data.length : (result.data ? 1 : 0),
      tableName: tableName,
      description: TABLE_CONFIGS[tableName].description,
      message: `${TABLE_CONFIGS[tableName].description}列表获取成功`
    },
    timestamp: Date.now()
  }
}

// 统计记录数量 - 使用官方API格式
async function countRecords(table, tableName, conditions, openid, envType) {
  console.log(`统计${tableName}表记录数量`)
  
  const whereConditions = [
    {
      _openid: {
        $eq: openid
      }
    }
  ]
  
  // 添加其他查询条件
  Object.keys(conditions).forEach(key => {
    if (key !== '_openid') {
      whereConditions.push({
        [key]: {
          $eq: conditions[key]
        }
      })
    }
  })
  
  const filter = {
    where: {
      $and: whereConditions
    }
  }
  
  // 使用官方API格式查询
  const result = await table.get({
    filter: filter,
    envType: envType
  })
  
  console.log('统计查询结果:', result)
  
  const count = Array.isArray(result.data) ? result.data.length : (result.data ? 1 : 0)
  
  return {
    success: true,
    data: {
      count: count,
      message: `${TABLE_CONFIGS[tableName].description}统计完成`
    },
    timestamp: Date.now()
  }
}

// 数据校验函数
function validateData(tableName, data, isUpdate = false) {
  // 基本字段长度校验
  if (data.nickname && data.nickname.length > 50) {
    throw new Error('昵称长度不能超过50个字符')
  }
  
  if (data.avatar_url && data.avatar_url.length > 255) {
    throw new Error('头像URL长度不能超过255个字符')
  }
  
  if (data.cultivation_realm && data.cultivation_realm.length > 50) {
    throw new Error('修炼境界长度不能超过50个字符')
  }
  
  // 数值校验
  if (data.level !== undefined && data.level < 1) {
    throw new Error('等级不能小于1')
  }
  
  if (data.exp !== undefined && data.exp < 0) {
    throw new Error('经验值不能小于0')
  }
  
  if (data.power !== undefined && data.power < 0) {
    throw new Error('战力不能小于0')
  }
  
  if (data.server_id !== undefined && data.server_id < 1) {
    throw new Error('服务器ID不能小于1')
  }
  
  console.log(`${tableName}表数据校验通过`)
} 

// 保存离线时间 - 使用服务器时间防止客户端篡改
async function saveOfflineTime(table, openid, serverTime, envType) {
  console.log(`保存玩家${openid}的离线时间: ${new Date(serverTime).toLocaleString()}`)
  
  try {
    // 首先查询玩家记录是否存在
    const queryResult = await table.get({
      filter: {
        where: {
          _openid: {
            $eq: openid
          }
        }
      },
      envType: envType
    })
    
    if (!queryResult.data || queryResult.data.length === 0) {
      console.log('玩家记录不存在，无法保存离线时间')
      return {
        success: false,
        error: '玩家记录不存在',
        code: 'PLAYER_NOT_FOUND'
      }
    }
    
    // 更新离线时间
    const updateResult = await table.update({
      filter: {
        where: {
          _openid: {
            $eq: openid
          }
        }
      },
      data: {
        last_offline_time: serverTime,
        updated_at: serverTime
      },
      envType: envType
    })
    
    console.log('离线时间保存结果:', updateResult)
    
    return {
      success: true,
      data: {
        serverTime: serverTime,
        message: '离线时间保存成功'
      },
      timestamp: serverTime
    }
    
  } catch (error) {
    console.error('保存离线时间失败:', error)
    return {
      success: false,
      error: error.message || '保存离线时间失败',
      code: 'SAVE_OFFLINE_TIME_ERROR'
    }
  }
}

// 获取服务器时间
async function getServerTime() {
  const serverTime = Date.now()
  console.log(`获取服务器时间: ${new Date(serverTime).toLocaleString()}`)
  
  return {
    success: true,
    data: {
      serverTime: serverTime,
      message: '服务器时间获取成功'
    },
    timestamp: serverTime
  }
}