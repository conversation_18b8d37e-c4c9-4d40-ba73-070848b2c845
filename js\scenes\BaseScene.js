/**
 * 场景基类
 * 定义了所有场景的共同属性和方法
 */
class BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager) {
    // 画布上下文
    this.ctx = ctx;

    // 屏幕尺寸
    this.screenWidth = screenWidth;
    this.screenHeight = screenHeight;

    // 场景管理器
    this.sceneManager = sceneManager;

    // UI元素列表
    this.uiElements = [];

    // 资源加载器
    this.resourceLoader = null;

    // 场景状态
    this.visible = false;

    // 触摸状态
    this.touchX = 0;
    this.touchY = 0;
    this.isTouching = false;

    // 战力增长动画
    this.powerAnimation = null;
  }

  // 显示场景（强制接口）
  show(params) {
    if (typeof this.onShow !== 'function') {
      console.error(`场景 ${this.constructor.name} 必须实现 onShow 方法`);
      return false;
    }
    this.visible = true;

    // 注册战力增长事件监听
    this.initPowerIncreaseListener();

    this.onShow(params);
    return true;
  }

  // 初始化战力增长事件监听
  initPowerIncreaseListener() {
    // 如果已经初始化过，则跳过
    if (this._powerListenerInitialized) {
      return;
    }

    try {
      // 注册战力增长事件监听
      this.registerPowerIncreaseListener();
      this._powerListenerInitialized = true;
    } catch (error) {
      console.warn('注册战力增长事件监听失败:', error);
    }
  }

  // 隐藏场景（强制接口）
  hide() {
    if (typeof this.onHide !== 'function') {
      console.error(`场景 ${this.constructor.name} 必须实现 onHide 方法`);
      return false;
    }
    this.visible = false;
    this.onHide();
    return true;
  }

  // 初始化UI
  initUI() {
    // 由子类实现
  }

  // 添加UI元素
  addUIElement(element) {
    if (!element) {
      console.error('添加了空的UI元素');
      return null;
    }

    const requiredMethods = ['isPointInside', 'render'];
    const missingMethods = requiredMethods.filter(m => !(m in element));

    if (missingMethods.length > 0) {
      console.error(`UI元素缺少必要方法: ${missingMethods.join(', ')}`, element);
      return null;
    }

    this.uiElements.push(element);
    return element;
  }

  // 移除UI元素
  removeUIElement(element) {
    const index = this.uiElements.indexOf(element);
    if (index !== -1) {
      this.uiElements.splice(index, 1);
      return true;
    }
    return false;
  }

  // 清空所有UI元素
  clearUIElements() {
    this.uiElements = [];
  }

  // 场景显示时的回调（子类可以覆盖）
  onShow(params) {
    // 默认实现，子类可以覆盖
    console.log(`${this.constructor.name} 显示场景`);
  }

  // 场景隐藏时的回调（子类必须实现）
  onHide() {
    // 默认实现，子类可以覆盖
    this.clearUIElements();
  }

  // 更新逻辑
  update() {
    if (!this.visible) {
      return;
    }

    // 更新所有UI元素
    for (const element of this.uiElements) {
      if (element.update) {
        element.update();
      }
    }

    // 执行场景特定的更新逻辑
    this.updateScene();
  }

  // 子类实现的更新逻辑
  updateScene() {
    // 子类实现
  }

  // 渲染场景
  render() {
    if (!this.visible) {
      return;
    }

    // 清空画布
    this.ctx.clearRect(0, 0, this.screenWidth, this.screenHeight);

    // 执行场景特定的绘制逻辑
    this.drawScene();

    // 绘制所有UI元素
    for (const element of this.uiElements) {
      if (element.render) {
        element.render(this.ctx);
      }
    }

    // 绘制战力增长动画（如果有）
    if (this.powerAnimation && this.powerAnimation.isActive()) {
      this.powerAnimation.draw();
    }
  }

  // 子类实现的绘制逻辑
  drawScene() {
    // 子类实现
  }

  // 触摸事件处理
  handleTouch(type, x, y) {
    if (!this.visible) {
      console.log(`Scene ${this.constructor.name} is not visible, ignoring touch event`);
      return false;
    }

    console.log(`Scene ${this.constructor.name} handleTouch: ${type} at ${x},${y}, UI元素数: ${this.uiElements.length}`);

    // 反向遍历确保最上层元素优先处理
    for (let i = this.uiElements.length - 1; i >= 0; i--) {
      const element = this.uiElements[i];

      // 安全检查
      if (!element) {
        console.warn(`空UI元素在索引 ${i}`);
        continue;
      }

      if (typeof element.isPointInside !== 'function') {
        console.warn(`UI元素缺少isPointInside方法:`, element);
        continue;
      }

      if (element.isPointInside(x, y)) {
        console.log(`触摸命中UI元素: ${element.constructor.name || '未知元素'}`);

        // 处理触摸事件
        switch (type) {
          case 'touchstart':
            if (typeof element.onTouchStart === 'function') {
              const result = element.onTouchStart(x, y);
              if (result) return true;
            } else {
              console.warn(`UI元素缺少onTouchStart方法:`, element);
            }
            break;
          case 'touchmove':
            if (typeof element.onTouchMove === 'function') {
              const result = element.onTouchMove(x, y);
              if (result) return true;
            }
            break;
          case 'touchend':
            if (typeof element.onTouchEnd === 'function') {
              const result = element.onTouchEnd(x, y);
              if (result) return true;
            } else {
              console.warn(`UI元素缺少onTouchEnd方法:`, element);
            }
            break;
        }
      }
    }

    // 场景自身处理
    console.log(`所有UI元素都未处理触摸事件，由场景自身处理`);

    // 直接调用场景的触摸事件处理方法
    switch (type) {
      case 'touchstart':
        return this.handleTouchStart(x, y);
      case 'touchmove':
        return this.handleTouchMove(x, y);
      case 'touchend':
        return this.handleTouchEnd(x, y);
      default:
        return false;
    }
  }

  // 子类实现的触摸开始事件处理
  handleTouchStart(x, y) {
    return false;
  }

  // 子类实现的触摸移动事件处理
  handleTouchMove(x, y) {
    // 可选坐标参数
    if (this.isTouching) {
      this.isTouching = false;
      return true;
    }
    return false;
  }

  // 子类实现的触摸结束事件处理
  handleTouchEnd(x, y) {
    return false;
  }

  // 安全获取资源
  getResource(key) {
    if (!this.resourceLoader) {
      // 使用场景管理器获取游戏实例
      let game = null;

      // 先尝试从场景管理器获取
      if (this.sceneManager && this.sceneManager.game) {
        game = this.sceneManager.game;
      }
      // 如果没有，尝试从全局变量获取
      else if (typeof window !== 'undefined' && window.game) {
        game = window.game;
      }
      // 如果还是没有，尝试从 global 变量获取
      else if (typeof global !== 'undefined' && global.game) {
        game = global.game;
      }

      if (game && game.resourceLoader) {
        this.resourceLoader = game.resourceLoader;
      } else {
        console.error('资源加载器未初始化');
        return null;
      }
    }

    if (this.resourceLoader && this.resourceLoader.resources && this.resourceLoader.resources[key]) {
      return this.resourceLoader.resources[key];
    }

    return null;
  }

  // 安全绘制图像
  drawImage(image, x, y, width, height, defaultCallback = null) {
    if (!image) {
      if (defaultCallback && typeof defaultCallback === 'function') {
        defaultCallback();
      }
      return false;
    }

    try {
      this.ctx.drawImage(image, x, y, width, height);
      return true;
    } catch (error) {
      console.error('绘制图像失败:', error);
      if (defaultCallback && typeof defaultCallback === 'function') {
        defaultCallback();
      }
      return false;
    }
  }

  // 安全绘制资源图像
  drawResourceImage(key, x, y, width, height, defaultCallback = null) {
    const resource = this.getResource(key);
    return this.drawImage(resource, x, y, width, height, defaultCallback);
  }

  // 注册战力增长事件监听
  registerPowerIncreaseListener() {
    try {
      // 使用场景管理器获取游戏实例
      let game = null;

      // 先尝试从场景管理器获取
      if (this.sceneManager && this.sceneManager.game) {
        game = this.sceneManager.game;
      }
      // 如果没有，尝试从全局变量获取
      else if (typeof window !== 'undefined' && window.game) {
        game = window.game;
      }
      // 如果还是没有，尝试从 global 变量获取
      else if (typeof global !== 'undefined' && global.game) {
        game = global.game;
      }

      if (!game) {
        console.warn('无法注册战力增长事件监听，游戏实例未找到');
        return;
      }

      if (!game.eventSystem) {
        console.warn('无法注册战力增长事件监听，事件系统未初始化');
        return;
      }

      // 监听玩家战力增长事件
      this._playerPowerIncreaseListener = (data) => {
        // 如果当前场景可见，显示战力增长动画
        if (this.visible) {
          console.log('玩家战力增长事件触发动画:', data);
          this.showPowerIncreaseAnimation(data.oldPower, data.newPower);
        }
      };

      // 监听角色战力增长事件（不显示动画，只用于调试）
      this._characterPowerIncreaseListener = (data) => {
        if (this.visible) {
          console.log('角色战力增长事件触发:', data);
        }
      };

      // 注册事件监听
      game.eventSystem.on('playerPowerIncrease', this._playerPowerIncreaseListener);
      game.eventSystem.on('characterPowerIncrease', this._characterPowerIncreaseListener);

      console.log(`场景 ${this.constructor.name} 注册战力增长事件监听成功`);
    } catch (error) {
      console.error('注册战力增长事件监听时发生错误:', error);
    }
  }

  // 显示战力增长动画
  async showPowerIncreaseAnimation(oldPower, newPower) {
    try {
      // 如果已经有正在运行的动画，则跳过
      if (this.powerAnimation && this.powerAnimation.isActive()) {
        console.log('已有战力增长动画正在运行，跳过新的动画');
        return;
      }

      // 使用场景管理器获取游戏实例
      let game = null;

      // 先尝试从场景管理器获取
      if (this.sceneManager && this.sceneManager.game) {
        game = this.sceneManager.game;
      }
      // 如果没有，尝试从全局变量获取
      else if (typeof window !== 'undefined' && window.game) {
        game = window.game;
      }
      // 如果还是没有，尝试从 global 变量获取
      else if (typeof global !== 'undefined' && global.game) {
        game = global.game;
      }

      if (!game) {
        console.warn('无法显示战力增长动画，游戏实例未找到');
        return;
      }

      // 在屏幕中央绘制战力增长效果
      const centerX = this.screenWidth / 2;
      const centerY = this.screenHeight / 2;
      const duration = 1000; // 1秒动画时间
      const startTime = Date.now();
      const increase = newPower - oldPower;

      // 播放音效（如果有）
      if (typeof wx !== 'undefined' && wx.createInnerAudioContext) {
        try {
          const audioContext = wx.createInnerAudioContext();
          audioContext.src = 'assets/sounds/power_up.mp3'; // 假设有这个音效
          audioContext.play();
        } catch (e) {
          console.warn('播放音效失败:', e);
        }
      }

      // 创建动画对象
      this.powerAnimation = {
        isActive: () => Date.now() - startTime < duration,
        draw: () => {
          const now = Date.now();
          const progress = Math.min(1, (now - startTime) / duration);

          // 绘制半透明背景
          this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
          this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);

          // 计算当前显示的战力值
          const currentPower = Math.floor(oldPower + increase * progress);
          const remainingIncrease = Math.floor(increase * (1 - progress));

          // 绘制光效
          const glowSize = 150 + Math.sin(progress * Math.PI * 2) * 20;
          const gradient = this.ctx.createRadialGradient(
            centerX, centerY, 10,
            centerX, centerY, glowSize
          );
          gradient.addColorStop(0, 'rgba(255, 215, 0, 0.8)');
          gradient.addColorStop(0.7, 'rgba(255, 215, 0, 0.2)');
          gradient.addColorStop(1, 'rgba(255, 215, 0, 0)');

          this.ctx.fillStyle = gradient;
          this.ctx.beginPath();
          this.ctx.arc(centerX, centerY, glowSize, 0, Math.PI * 2);
          this.ctx.fill();

          // 绘制“战力”标签
          this.ctx.font = 'bold 32px Arial';
          this.ctx.fillStyle = '#FFCC00';
          this.ctx.textAlign = 'center';
          this.ctx.fillText('战力', centerX, centerY - 60);

          // 绘制当前战力值
          const powerSize = 40 + Math.sin(progress * Math.PI) * 8;
          this.ctx.font = `bold ${powerSize}px Arial`;
          this.ctx.fillStyle = '#FFFFFF';
          this.ctx.fillText(`${currentPower}`, centerX, centerY);

          // 绘制战力值光效
          this.ctx.shadowColor = '#FFCC00';
          this.ctx.shadowBlur = 15;
          this.ctx.fillText(`${currentPower}`, centerX, centerY);
          this.ctx.shadowBlur = 0;

          // 如果还有增长部分，绘制增长部分
          if (remainingIncrease > 0 && progress < 1) {
            const increaseSize = 30 + Math.sin(progress * Math.PI) * 5;
            this.ctx.font = `bold ${increaseSize}px Arial`;
            this.ctx.fillStyle = '#FFD700'; // 金色
            this.ctx.shadowColor = '#FFCC00';
            this.ctx.shadowBlur = 10;
            this.ctx.fillText(`+${remainingIncrease}`, centerX + 100, centerY);
            this.ctx.shadowBlur = 0;
          }

          // 不显示进度条

          // 绘制提示文字
          this.ctx.font = 'bold 18px Arial';
          this.ctx.fillStyle = '#FFFFFF';
          this.ctx.fillText('战力增长!', centerX, centerY + 60);

          // 如果动画完成，清除动画对象
          if (progress >= 1) {
            setTimeout(() => {
              this.powerAnimation = null;
            }, 800); // 延迟清除，让最终效果显示一会儿
          }
        }
      };

      console.log(`显示战力增长动画: ${oldPower} -> ${newPower}, 增长: ${increase}`);
    } catch (error) {
      console.error('显示战力增长动画时发生错误:', error);
      this.powerAnimation = null;
    }
  }

  // 绘制默认图标
  drawDefaultIcon(x, y, size, color = '#cccccc', shape = 'circle') {
    this.ctx.fillStyle = color;

    if (shape === 'circle') {
      this.ctx.beginPath();
      this.ctx.arc(x + size/2, y + size/2, size/2, 0, Math.PI * 2);
      this.ctx.fill();
    } else if (shape === 'rect') {
      this.ctx.fillRect(x, y, size, size);
    } else if (shape === 'diamond') {
      this.ctx.beginPath();
      this.ctx.moveTo(x + size/2, y);
      this.ctx.lineTo(x + size, y + size/2);
      this.ctx.lineTo(x + size/2, y + size);
      this.ctx.lineTo(x, y + size/2);
      this.ctx.closePath();
      this.ctx.fill();
    }
  }

  // 绘制默认头像
  drawDefaultAvatar(x, y, size, color = '#cccccc', text = '') {
    // 绘制头像背景
    this.ctx.fillStyle = color;
    this.ctx.beginPath();
    this.ctx.arc(x + size/2, y + size/2, size/2, 0, Math.PI * 2);
    this.ctx.fill();

    // 如果提供了文本，则绘制文本
    if (text) {
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = `bold ${size/2}px Arial`;
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText(text.charAt(0).toUpperCase(), x + size/2, y + size/2);
    } else {
      // 否则绘制默认的人形图标
      this.ctx.fillStyle = '#ffffff';

      // 头部
      this.ctx.beginPath();
      this.ctx.arc(x + size/2, y + size/2 - size/8, size/4, 0, Math.PI * 2);
      this.ctx.fill();

      // 身体
      this.ctx.beginPath();
      this.ctx.moveTo(x + size/2, y + size/2 + size/8);
      this.ctx.lineTo(x + size/2 - size/4, y + size/2 + size/2);
      this.ctx.lineTo(x + size/2 + size/4, y + size/2 + size/2);
      this.ctx.closePath();
      this.ctx.fill();
    }
  }

  // 创建渐变背景
  createGradientBackground(colors) {
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);

    // 如果没有提供颜色，使用默认颜色
    if (!colors || !colors.length) {
      colors = [
        { pos: 0, color: '#1a2a6c' },
        { pos: 0.5, color: '#b21f1f' },
        { pos: 1, color: '#fdbb2d' }
      ];
    }

    // 添加渐变色停止点
    colors.forEach(color => {
      gradient.addColorStop(color.pos, color.color);
    });

    return gradient;
  }

  // 绘制带圆角的矩形
  drawRoundRect(x, y, width, height, radius, fillStyle = null, strokeStyle = null) {
    this.ctx.beginPath();
    this.ctx.moveTo(x + radius, y);
    this.ctx.lineTo(x + width - radius, y);
    this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    this.ctx.lineTo(x + width, y + height - radius);
    this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    this.ctx.lineTo(x + radius, y + height);
    this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    this.ctx.lineTo(x, y + radius);
    this.ctx.quadraticCurveTo(x, y, x + radius, y);
    this.ctx.closePath();

    if (fillStyle) {
      this.ctx.fillStyle = fillStyle;
      this.ctx.fill();
    }

    if (strokeStyle) {
      this.ctx.strokeStyle = strokeStyle;
      this.ctx.stroke();
    }
  }

  // 绘制文本，带省略号处理
  drawText(text, x, y, maxWidth, font, color) {
    this.ctx.font = font || '16px Arial';
    this.ctx.fillStyle = color || '#ffffff';

    // 检查文本宽度是否超过最大宽度
    if (this.ctx.measureText(text).width > maxWidth) {
      // 查找可以显示的最大字符数
      let displayText = '';
      for (let i = 0; i < text.length; i++) {
        const testText = text.substring(0, i + 1) + '…';
        if (this.ctx.measureText(testText).width > maxWidth) {
          displayText = text.substring(0, i) + '…';
          break;
        }

        if (i === text.length - 1) {
          displayText = text;
        }
      }

      this.ctx.fillText(displayText, x, y);
    } else {
      this.ctx.fillText(text, x, y);
    }
  }
}

export default BaseScene;