{"name": "xiuxian-admin-system", "version": "1.0.0", "description": "修仙游戏后台管理系统", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node start-dev.js", "dev:watch": "nodemon start-dev.js", "start:mock": "USE_MOCK_DB=true node server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["admin", "game", "management"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "body-parser": "^1.20.2", "dotenv": "^16.3.1", "ejs": "^3.1.9", "jsonwebtoken": "^9.0.2", "express-session": "^1.17.3", "express-rate-limit": "^7.1.5", "bcryptjs": "^2.4.3", "joi": "^17.11.0", "helmet": "^7.1.0"}, "devDependencies": {"nodemon": "^3.0.1"}}