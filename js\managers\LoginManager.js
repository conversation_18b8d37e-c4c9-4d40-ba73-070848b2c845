/**
 * 登录管理器
 * 负责处理用户登录流程、权限获取、服务器选择等功能
 * 版本: v2.0 - 添加仙友系统数据初始化支持
 */

class LoginManager {
  constructor(game) {
    this.game = game;
    this.isLoggedIn = false;
    this.userOpenId = null;
    this.selectedServer = null;
    this.hasPrivacyAuth = false;

    // 登录状态常量
    this.LOGIN_STATUS = {
      NOT_LOGGED: 'not_logged',      // 未登录
      PRIVACY_PENDING: 'privacy_pending', // 等待隐私授权
      SERVER_SELECTION: 'server_selection', // 服务器选择（已不使用）
      GAME_ENTERING: 'game_entering', // 正在进入游戏
      LOGGED_IN: 'logged_in'         // 已登录
    };

    this.currentStatus = this.LOGIN_STATUS.NOT_LOGGED;
  }

  /**
   * 初始化登录流程
   * 游戏加载完成后调用此方法
   */
  async initializeLogin() {
    console.log('开始初始化登录流程...');

    try {
      // 1. 检查用户是否曾经登录过
      const hasLoggedBefore = await this.checkPreviousLogin();

      if (hasLoggedBefore) {
        console.log('检测到用户曾经登录过，开始自动登录流程');
        await this.autoLogin();
      } else {
        console.log('用户首次使用，需要进行完整登录流程');
        this.currentStatus = this.LOGIN_STATUS.PRIVACY_PENDING;
        this.showPrivacyAuthDialog();
      }
    } catch (error) {
      console.error('初始化登录流程失败:', error);
      this.handleLoginError(error);
    }
  }

  /**
   * 检查用户是否曾经登录过
   */
  async checkPreviousLogin() {
    try {
      // 检查本地存储中的授权信息
      const userInfo = wx.getStorageSync('userInfo');
      const lastServer = wx.getStorageSync('lastSelectedServer');

      if (userInfo && userInfo.authorized && lastServer) {
        console.log('发现本地授权信息和服务器记录');
        return true;
      }

      // 检查微信授权设置
      const authSetting = await this.getAuthSetting();
      if (authSetting && authSetting['scope.userInfo'] === true) {
        console.log('发现微信授权记录');
        return true;
      }

      return false;
    } catch (error) {
      console.error('检查登录历史失败:', error);
      return false;
    }
  }

  /**
   * 获取微信授权设置
   */
  getAuthSetting() {
    return new Promise((resolve, reject) => {
      wx.getSetting({
        success: (res) => resolve(res.authSetting),
        fail: (err) => reject(err)
      });
    });
  }

  /**
   * 自动登录流程（用户曾经登录过）
   */
  async autoLogin() {
    // 增加重试计数器
    this.autoLoginRetryCount = (this.autoLoginRetryCount || 0) + 1;
    const maxRetries = 2;

    try {
      console.log(`开始自动登录... (第${this.autoLoginRetryCount}次尝试)`);

      // 检查重试次数限制
      if (this.autoLoginRetryCount > maxRetries) {
        console.warn('自动登录重试次数超限，切换到手动登录');
        this.switchToManualLogin();
        return;
      }

      // 先检查用户会话状态
      const sessionValid = await this.checkUserSession();
      if (!sessionValid) {
        console.log('用户会话已失效，需要重新登录');
        this.switchToManualLogin();
        return;
      }

      // 1. 获取用户openid
      const openid = await this.getUserOpenId();
      if (!openid) {
        throw new Error('获取openid失败');
      }

      this.userOpenId = openid;
      console.log('获取openid成功:', openid);

      // 2. 获取用户最后登录的服务器
      const lastServer = wx.getStorageSync('lastSelectedServer');
      if (lastServer) {
        this.selectedServer = lastServer;
        console.log('使用上次选择的服务器:', lastServer);
      } else {
        // 如果没有服务器记录，使用默认服务器
        this.selectedServer = this.getDefaultServer();
        console.log('使用默认服务器:', this.selectedServer);
      }

      // 3. 从云数据库加载用户数据
      await this.loadUserDataFromCloud();

      // 4. 启动与openid相关的功能
      this.startOpenIdRelatedFeatures();

      // 5. 标记登录完成
      this.completeLogin();

      // 重置重试计数
      this.autoLoginRetryCount = 0;

    } catch (error) {
      console.error(`自动登录失败 (第${this.autoLoginRetryCount}次):`, error);
      
      // 根据错误类型决定是否重试
      if (this.shouldRetryAutoLogin(error) && this.autoLoginRetryCount < maxRetries) {
        console.log('将在1秒后重试自动登录...');
        setTimeout(() => this.autoLogin(), 1000);
      } else {
        console.log('自动登录彻底失败，切换到手动登录');
        this.switchToManualLogin();
      }
    }
  }

  /**
   * 检查是否应该重试自动登录
   */
  shouldRetryAutoLogin(error) {
    const retryableErrors = [
      'access_token missing',
      'network error',
      'timeout',
      'FUNCTIONS_EXECUTE_FAIL'
    ];
    
    const errorMessage = error.message || error.toString();
    return retryableErrors.some(retryError => 
      errorMessage.toLowerCase().includes(retryError.toLowerCase())
    );
  }

  /**
   * 切换到手动登录模式
   */
  switchToManualLogin() {
    console.log('切换到手动登录模式');
    
    // 清除可能过期的本地数据
    this.clearExpiredLocalData();
    
    // 重置状态
    this.currentStatus = this.LOGIN_STATUS.PRIVACY_PENDING;
    this.autoLoginRetryCount = 0;
    
    // 显示手动登录界面
    this.showPrivacyAuthDialog();
  }

  /**
   * 清除过期的本地数据
   */
  clearExpiredLocalData() {
    try {
      if (typeof wx !== 'undefined') {
        // 清除可能过期的用户信息，但保留一些基础设置
        const keysToRemove = ['userInfo', 'openid'];
        keysToRemove.forEach(key => {
          try {
            wx.removeStorageSync(key);
          } catch (err) {
            console.warn(`清除${key}失败:`, err);
          }
        });
        console.log('已清除过期的本地数据');
      }
    } catch (err) {
      console.error('清除本地数据失败:', err);
    }
  }

  /**
   * 检查用户会话状态
   */
  async checkUserSession() {
    return new Promise((resolve) => {
      if (typeof wx !== 'undefined' && wx.checkSession) {
        wx.checkSession({
          success: () => {
            console.log('用户会话有效');
            resolve(true);
          },
          fail: () => {
            console.log('用户会话已过期');
            resolve(false);
          }
        });
      } else {
        resolve(true); // 非微信环境
      }
    });
  }

  /**
   * 显示隐私权限授权弹窗
   */
  showPrivacyAuthDialog() {
    console.log('显示隐私权限授权弹窗');

    wx.showModal({
      title: '隐私权限授权',
      content: '为了保存您的游戏进度和提供更好的游戏体验，需要获取您的基本信息权限。',
      confirmText: '同意授权',
      cancelText: '暂不授权',
      success: (res) => {
        if (res.confirm) {
          this.requestPrivacyAuth();
        } else {
          this.handlePrivacyAuthDenied();
        }
      }
    });
  }

  /**
   * 检查隐私权限状态
   */
  async checkPrivacyAuthStatus() {
    return new Promise((resolve) => {
      if (typeof wx !== 'undefined' && wx.getPrivacySetting) {
        wx.getPrivacySetting({
          success: (res) => {
            console.log('LoginManager - 隐私设置状态:', res);
            // needAuthorization: true 表示需要用户授权
            // privacyContractName: 隐私协议名称
            resolve({
              needAuthorization: res.needAuthorization,
              privacyContractName: res.privacyContractName
            });
          },
          fail: (err) => {
            console.error('LoginManager - 获取隐私设置失败:', err);
            resolve({ needAuthorization: false });
          }
        });
      } else {
        // 不支持隐私设置API，认为不需要授权
        console.log('LoginManager - 不支持隐私设置API');
        resolve({ needAuthorization: false });
      }
    });
  }

  /**
   * 等待隐私授权完成
   */
  async waitForPrivacyAuthorization() {
    return new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        wx.getPrivacySetting({
          success: (res) => {
            if (!res.needAuthorization) {
              console.log('LoginManager - 隐私授权已完成');
              clearInterval(checkInterval);
              resolve(true);
            }
          },
          fail: () => {
            // 如果检查失败，认为不需要授权
            clearInterval(checkInterval);
            resolve(true);
          }
        });
      }, 500); // 每500ms检查一次

      // 设置超时，避免无限等待
      setTimeout(() => {
        clearInterval(checkInterval);
        console.warn('LoginManager - 等待隐私授权超时');
        resolve(false);
      }, 30000); // 30秒超时
    });
  }

  /**
   * 请求隐私权限授权
   */
  async requestPrivacyAuth() {
    console.log('LoginManager - 请求隐私权限授权...');

    try {
      // 首先检查是否需要隐私授权
      const privacyStatus = await this.checkPrivacyAuthStatus();

      if (privacyStatus.needAuthorization) {
        console.log('LoginManager - 需要进行隐私授权，等待用户操作...');

        // 等待用户在game.js中创建的隐私授权弹窗中完成授权
        const authResult = await this.waitForPrivacyAuthorization();

        if (!authResult) {
          console.error('LoginManager - 隐私授权超时或失败');
          this.handlePrivacyAuthDenied();
          return;
        }

        console.log('LoginManager - 隐私授权完成');
      } else {
        console.log('LoginManager - 无需隐私授权');
      }

      // 隐私授权完成后，获取用户信息
      this.getUserProfileInfo();

    } catch (err) {
      console.error('LoginManager - 隐私权限授权过程出错:', err);
      this.handlePrivacyAuthDenied();
    }
  }

  /**
   * 获取用户资料信息
   */
  getUserProfileInfo() {
    console.log('LoginManager - 获取用户资料信息...');

    // 检查是否在开发环境
    const isDevEnv = this.isDevEnvironment();

    if (isDevEnv) {
      console.log('LoginManager - 检测到开发环境，使用模拟用户数据');
      this.handleDevEnvironmentAuth();
      return;
    }

    // 生产环境使用真实的getUserProfile
    wx.getUserProfile({
      desc: '用于保存游戏进度和个性化体验',
      lang: 'zh_CN',
      success: (res) => {
        console.log('LoginManager - 获取用户资料成功:', res.userInfo);
        this.hasPrivacyAuth = true;

        // 保存用户信息
        this.saveUserInfo(res.userInfo);

        // 使用默认服务器直接进入游戏
        this.selectedServer = this.getDefaultServer();
        this.currentStatus = this.LOGIN_STATUS.GAME_ENTERING;
        this.enterGame();
      },
      fail: (err) => {
        console.error('LoginManager - 获取用户资料失败:', err);

        // 如果是因为用户取消或其他原因失败，尝试使用备用方案
        console.log('LoginManager - 尝试使用备用授权方案...');
        this.handleAuthFallback(err);
      }
    });
  }

  /**
   * 检查是否在开发环境
   */
  isDevEnvironment() {
    // 检查多个开发环境标识
    const userAgent = navigator.userAgent || '';
    const isDevTool = userAgent.includes('wechatdevtools');
    const isLocalhost = location.hostname === 'localhost' || location.hostname === '127.0.0.1';
    const isFileProtocol = location.protocol === 'file:';

    return isDevTool || isLocalhost || isFileProtocol;
  }

  /**
   * 处理开发环境授权
   */
  handleDevEnvironmentAuth() {
    console.log('LoginManager - 开发环境授权处理');

    // 模拟用户信息
    const mockUserInfo = {
      nickName: '开发者',
      avatarUrl: '',
      gender: 1,
      country: '中国',
      province: '广东',
      city: '深圳',
      language: 'zh_CN'
    };

    this.hasPrivacyAuth = true;

    // 保存模拟用户信息
    this.saveUserInfo(mockUserInfo);

    // 显示开发环境提示
    if (typeof wx !== 'undefined' && wx.showToast) {
      wx.showToast({
        title: '开发环境模拟登录',
        icon: 'none',
        duration: 2000
      });
    }

    // 使用默认服务器直接进入游戏
    this.selectedServer = this.getDefaultServer();
    this.currentStatus = this.LOGIN_STATUS.GAME_ENTERING;
    this.enterGame();
  }

  /**
   * 处理授权失败的备用方案
   */
  handleAuthFallback(error) {
    console.log('LoginManager - 执行授权备用方案, 错误:', error);

    // 检查错误类型
    if (error && error.errMsg) {
      if (error.errMsg.includes('cancel')) {
        // 用户主动取消
        console.log('LoginManager - 用户取消授权');
        this.handlePrivacyAuthDenied();
        return;
      }

      if (error.errMsg.includes('fail')) {
        // API调用失败，可能是环境问题
        console.log('LoginManager - API调用失败，尝试简化流程');
        this.handleSimplifiedAuth();
        return;
      }
    }

    // 其他未知错误
    console.log('LoginManager - 未知错误，使用简化授权流程');
    this.handleSimplifiedAuth();
  }

  /**
   * 简化授权流程（用于兼容性处理）
   */
  handleSimplifiedAuth() {
    console.log('LoginManager - 使用简化授权流程');

    // 使用基础用户信息
    const basicUserInfo = {
      nickName: '修仙者',
      avatarUrl: '',
      gender: 0,
      country: '',
      province: '',
      city: '',
      language: 'zh_CN'
    };

    this.hasPrivacyAuth = true;

    // 保存基础用户信息
    this.saveUserInfo(basicUserInfo);

    // 显示兼容性提示
    if (typeof wx !== 'undefined' && wx.showToast) {
      wx.showToast({
        title: '使用基础信息登录',
        icon: 'none',
        duration: 2000
      });
    }

    // 使用默认服务器直接进入游戏
    this.selectedServer = this.getDefaultServer();
    this.currentStatus = this.LOGIN_STATUS.GAME_ENTERING;
    this.enterGame();
  }

  /**
   * 处理隐私权限授权被拒绝
   */
  handlePrivacyAuthDenied() {
    console.log('LoginManager - 处理隐私权限授权被拒绝');

    // 检查是否在开发环境，如果是则使用简化流程
    if (this.isDevEnvironment()) {
      console.log('LoginManager - 开发环境中的授权问题，使用简化流程');
      this.handleSimplifiedAuth();
      return;
    }

    // 生产环境中的真正拒绝
    wx.showModal({
      title: '提示',
      content: '未授权将无法保存游戏进度，您可以继续游戏但数据不会同步到云端。是否重新授权？',
      confirmText: '重新授权',
      cancelText: '继续游戏',
      success: (res) => {
        if (res.confirm) {
          this.showPrivacyAuthDialog();
        } else {
          // 用户选择继续游戏，使用离线模式
          this.startOfflineMode();
        }
      }
    });
  }

  /**
   * 显示服务器选择界面（已移除 - 现在使用默认服务器直接进入游戏）
   */
  // showServerSelection() - 已移除，不再需要服务器选择弹窗

  /**
   * 进入游戏（完成服务器选择后）
   */
  async enterGame() {
    try {
      console.log('开始进入游戏...');

      // 1. 获取用户openid
      const openid = await this.getUserOpenId();
      if (!openid) {
        throw new Error('获取openid失败');
      }

      this.userOpenId = openid;
      console.log('获取openid成功:', openid);

      // 2. 从云数据库加载用户数据
      await this.loadUserDataFromCloud();

      // 3. 启动与openid相关的功能
      this.startOpenIdRelatedFeatures();

      // 4. 标记登录完成
      this.completeLogin();

    } catch (error) {
      console.error('进入游戏失败:', error);
      this.handleLoginError(error);
    }
  }

  /**
   * 获取用户openid（使用云函数安全获取）
   */
  getUserOpenId() {
    return new Promise(async (resolve, reject) => {
      try {
        // 先检查本地缓存的openid是否有效
        const cachedOpenId = this.getCachedOpenId();
        if (cachedOpenId && await this.validateCachedOpenId(cachedOpenId)) {
          console.log('使用缓存的openid:', cachedOpenId);
          resolve(cachedOpenId);
          return;
        }

        // 检查云环境是否已初始化
        let cloudInstance = null;

        if (this.game.cloud && this.game.cloudInited) {
          cloudInstance = this.game.cloud;
        } else if (typeof wx !== 'undefined' && wx.cloud) {
          cloudInstance = wx.cloud;
        } else {
          console.error('云环境未初始化');
          reject(new Error('云环境未初始化，请确保微信云开发已正确配置'));
          return;
        }

        console.log('开始调用getOpenId云函数获取openid...');

        // 设置超时处理
        const timeout = setTimeout(() => {
          reject(new Error('获取openid超时'));
        }, 10000); // 10秒超时

        // 优先使用新的getOpenId云函数
        cloudInstance.callFunction({
          name: 'getOpenId',
          data: {
            timestamp: Date.now(),
            source: 'login_manager'
          },
          success: (res) => {
            clearTimeout(timeout);
            console.log('getOpenId云函数调用成功:', res);

            if (res.result && res.result.success && res.result.openid) {
              console.log('成功获取到openid:', res.result.openid);
              // 缓存openid
              this.cacheOpenId(res.result.openid);
              resolve(res.result.openid);
            } else {
              console.error('getOpenId云函数返回失败:', res.result);
              // 回退到旧的login云函数
              this.fallbackToLoginFunction(cloudInstance, resolve, reject);
            }
          },
          fail: (err) => {
            clearTimeout(timeout);
            console.error('getOpenId云函数调用失败:', err);
            
            // 判断是否是致命错误
            if (this.isFatalCloudError(err)) {
              reject(new Error(`云函数调用失败: ${err.errMsg || err.message || '未知错误'}`));
            } else {
              // 回退到旧的login云函数
              this.fallbackToLoginFunction(cloudInstance, resolve, reject);
            }
          }
        });
      } catch (error) {
        console.error('getUserOpenId异常:', error);
        reject(error);
      }
    });
  }

  /**
   * 获取缓存的openid
   */
  getCachedOpenId() {
    try {
      if (typeof wx !== 'undefined') {
        return wx.getStorageSync('cached_openid');
      }
    } catch (err) {
      console.warn('获取缓存openid失败:', err);
    }
    return null;
  }

  /**
   * 验证缓存的openid是否有效
   */
  async validateCachedOpenId(openid) {
    // 简单验证：检查格式和缓存时间
    if (!openid || typeof openid !== 'string' || openid.length < 20) {
      return false;
    }

    try {
      const cacheTime = wx.getStorageSync('openid_cache_time');
      const now = Date.now();
      // 缓存有效期1小时
      if (cacheTime && (now - cacheTime) < 3600000) {
        return true;
      }
    } catch (err) {
      console.warn('验证缓存openid时间失败:', err);
    }

    return false;
  }

  /**
   * 缓存openid
   */
  cacheOpenId(openid) {
    try {
      if (typeof wx !== 'undefined') {
        wx.setStorageSync('cached_openid', openid);
        wx.setStorageSync('openid_cache_time', Date.now());
        console.log('openid已缓存');
      }
    } catch (err) {
      console.warn('缓存openid失败:', err);
    }
  }

  /**
   * 判断是否是致命的云函数错误
   */
  isFatalCloudError(error) {
    const fatalErrors = [
      'INVALID_ENV',
      'FUNCTION_NOT_FOUND',
      'PERMISSION_DENIED'
    ];
    
    const errorMessage = error.errMsg || error.message || '';
    return fatalErrors.some(fatalError => 
      errorMessage.includes(fatalError)
    );
  }

  /**
   * 回退到使用login云函数获取openid
   */
  fallbackToLoginFunction(cloudInstance, resolve, reject) {
    console.log('回退到使用login云函数获取openid...');

    cloudInstance.callFunction({
      name: 'login',
      success: (res) => {
        console.log('login云函数调用成功:', res);

        // 兼容新旧两种返回格式
        const openid = res.result.openid || (res.result.success && res.result.openid);

        if (openid) {
          console.log('通过login云函数获取到openid:', openid);
          resolve(openid);
        } else {
          console.error('login云函数返回的openid为空:', res.result);
          reject(new Error('无法获取用户openid，请检查云函数配置'));
        }
      },
      fail: (err) => {
        console.error('login云函数调用失败:', err);
        reject(new Error(`云函数调用失败: ${err.errMsg || err.message || '未知错误'}`));
      }
    });
  }

  /**
   * 从云数据库加载用户数据
   */
  async loadUserDataFromCloud() {
    if (!this.userOpenId) {
      console.warn('没有openid，跳过云数据库加载');
      return;
    }

    try {
      console.log('从云数据库加载用户数据...');

      // 使用新的DatabaseManager加载数据
      if (this.game.gameStateManager && this.game.gameStateManager.databaseManager) {
        const success = await this.game.gameStateManager.loadGameStateFromCloud();
        if (success) {
          console.log('从云数据库加载用户数据成功');
          
          // 初始化仙友数据
          await this.initializeXianyouData();
        } else {
          console.log('云数据库加载失败，使用本地数据');
          
          // 即使云数据库加载失败，也要初始化仙友数据结构
          this.initializeLocalXianyouData();
        }
      }
    } catch (error) {
      console.error('从云数据库加载用户数据失败:', error);
    }
  }

  /**
   * 初始化仙友系统数据
   */
  async initializeXianyouData() {
    try {
      console.log('开始初始化仙友系统数据...');
      
      const dataSyncManager = this.game.gameStateManager?.dataSyncManager;
      if (!dataSyncManager) {
        console.warn('数据同步管理器未初始化，跳过仙友数据初始化');
        this.initializeLocalXianyouData();
        return;
      }

      // 尝试从云端加载仙友数据
      const xianyouDataLoaded = await dataSyncManager.loadXianyouDataFromCloud();
      
      if (!xianyouDataLoaded) {
        // 如果云端没有数据，创建新的仙友数据
        console.log('云端无仙友数据，创建新的仙友数据...');
        await dataSyncManager.createNewPlayerXianyouData();
        
        // 初始化本地仙友数据结构
        this.initializeLocalXianyouData();
      }

      console.log('仙友系统数据初始化完成');
    } catch (error) {
      console.error('初始化仙友数据失败:', error);
      // 不抛出错误，避免影响登录流程
      // 但要确保本地数据结构正确
      this.initializeLocalXianyouData();
    }
  }

  /**
   * 初始化本地仙友数据结构
   */
  initializeLocalXianyouData() {
    try {
      if (!this.game.gameStateManager || !this.game.gameStateManager.state) {
        console.warn('游戏状态管理器未初始化');
        return;
      }

      // 确保仙友数据结构存在
      if (!this.game.gameStateManager.state.xianyou) {
        this.game.gameStateManager.state.xianyou = {
          ownedXianyou: [],
          favorabilityLevels: {},
          favorabilityPoints: {},
          starLevels: {},
          placedInDongfu: {},
          gachaPity: 0,
          lastInteractionTime: {}
        };
        
        console.log('本地仙友数据结构初始化完成');
      }
    } catch (error) {
      console.error('初始化本地仙友数据结构失败:', error);
    }
  }

  /**
   * 启动与openid相关的功能
   */
  startOpenIdRelatedFeatures() {
    console.log('启动与openid相关的功能，openid:', this.userOpenId);

    if (!this.userOpenId) {
      console.error('userOpenId为空，无法启动相关功能');
      return;
    }

    // 设置全局openid到多个位置
    if (typeof window !== 'undefined') {
      window.userOpenId = this.userOpenId;
    }

    // 存储到微信本地存储
    wx.setStorageSync('openid', this.userOpenId);

    // 存储到用户信息中
    const userInfo = wx.getStorageSync('userInfo') || {};
    userInfo.openid = this.userOpenId;
    wx.setStorageSync('userInfo', userInfo);

    // 设置到game对象上
    if (this.game) {
      this.game.user = this.game.user || {};
      this.game.user.openid = this.userOpenId;
    }

    // 启动数据库相关功能
    if (this.game.gameStateManager) {
      // 标记游戏状态管理器可以使用数据库功能
      this.game.gameStateManager.gameInitialized = true;

      // 确保数据库管理器也能获取到openid
      if (this.game.gameStateManager.databaseManager) {
        // 可以在这里做额外的初始化
        console.log('数据库管理器已准备就绪');
      }
    }

    // 启动自动同步
    if (this.game.startAutoSync) {
      this.game.startAutoSync();
    }

    // 启动自动保存
    if (this.game.autoSaveManager) {
      this.game.autoSaveManager.startAutoSave();
    }

    console.log('openid相关功能启动完成，openid已存储到多个位置');
  }

  /**
   * 完成登录
   */
  completeLogin() {
    this.isLoggedIn = true;
    this.currentStatus = this.LOGIN_STATUS.LOGGED_IN;

    console.log('登录流程完成');

    // 触发登录完成事件
    if (this.game.eventSystem) {
      this.game.eventSystem.emit('loginCompleted', {
        openid: this.userOpenId,
        server: this.selectedServer
      });
    }

    // 显示登录成功提示
    wx.showToast({
      title: '登录成功',
      icon: 'success',
      duration: 2000
    });

    // 检查并计算离线修炼收益
    setTimeout(() => {
      this.loadOfflineTimeAndCalculateGains();
    }, 1000); // 延迟1秒确保登录流程完全完成
  }

  /**
   * 检查登录是否已完成
   */
  isLoginComplete() {
    return this.isLoggedIn && this.currentStatus === this.LOGIN_STATUS.LOGGED_IN;
  }

  /**
   * 启动离线模式
   */
  startOfflineMode() {
    console.log('启动离线模式');
    this.currentStatus = this.LOGIN_STATUS.NOT_LOGGED;

    wx.showToast({
      title: '离线模式',
      icon: 'none',
      duration: 2000
    });
  }

  /**
   * 处理登录错误
   */
  handleLoginError(error) {
    console.error('登录错误:', error);

    wx.showModal({
      title: '登录失败',
      content: '登录过程中出现错误，是否重试？',
      confirmText: '重试',
      cancelText: '离线游戏',
      success: (res) => {
        if (res.confirm) {
          this.initializeLogin();
        } else {
          this.startOfflineMode();
        }
      }
    });
  }

  /**
   * 保存用户信息
   */
  saveUserInfo(userInfo) {
    const userData = {
      ...userInfo,
      authorized: true,
      authorizeTime: Date.now()
    };

    wx.setStorageSync('userInfo', userData);

    // 同步到游戏状态管理器
    if (this.game.gameStateManager) {
      const player = this.game.gameStateManager.getPlayer();
      if (player) {
        player.nickname = userInfo.nickName || '修仙者';
        player.avatarUrl = userInfo.avatarUrl || '';
        this.game.gameStateManager.setPlayer(player);
      }
    }
  }

  /**
   * 获取可用服务器列表
   */
  getAvailableServers() {
    return [
      { id: 'server1', name: '青云门', region: 'cn-east' },
      { id: 'server2', name: '天音寺', region: 'cn-south' },
      { id: 'server3', name: '鬼王宗', region: 'cn-north' },
      { id: 'server4', name: '合欢派', region: 'cn-west' }
    ];
  }

  /**
   * 获取默认服务器
   */
  getDefaultServer() {
    return { id: 'server1', name: '青云门', region: 'cn-east' };
  }

  /**
   * 获取当前登录状态
   */
  getLoginStatus() {
    return {
      isLoggedIn: this.isLoggedIn,
      status: this.currentStatus,
      openid: this.userOpenId,
      server: this.selectedServer,
      hasPrivacyAuth: this.hasPrivacyAuth
    };
  }

  /**
   * 手动登录（用户点击登录按钮）
   */
  async manualLogin() {
    if (this.isLoggedIn) {
      console.log('用户已登录');
      return;
    }

    console.log('开始手动登录流程');
    await this.initializeLogin();
  }

  /**
   * 登出
   */
  logout() {
    this.isLoggedIn = false;
    this.userOpenId = null;
    this.selectedServer = null;
    this.hasPrivacyAuth = false;
    this.currentStatus = this.LOGIN_STATUS.NOT_LOGGED;

    // 清除本地存储
    wx.removeStorageSync('userInfo');
    wx.removeStorageSync('openid');
    wx.removeStorageSync('lastSelectedServer');

    console.log('用户已登出');
  }

  /**
   * 保存离线时间到云端
   * 使用服务器时间防止玩家篡改本地时间获得额外收益
   */
  async saveOfflineTime() {
    try {
      if (!this.game || !this.game.gameStateManager) {
        console.log('游戏状态管理器不可用，无法保存离线时间');
        return;
      }

      const player = this.game.gameStateManager.getPlayer();
      if (!player) {
        console.log('玩家数据不存在，无法保存离线时间');
        return;
      }

      // 如果用户未登录，只保存到本地存储作为备用
      if (!this.isLoggedIn) {
        const currentTime = Date.now();
        wx.setStorageSync('lastOfflineTime', currentTime);
        console.log('用户未登录，离线时间仅保存到本地存储');
        return;
      }

      console.log('开始保存离线时间到云端...');

      // 调用云函数保存离线时间（使用服务器时间）
      const result = await wx.cloud.callFunction({
        name: 'databaseService',
        data: {
          action: 'saveOfflineTime',
          envType: 'prod' // 确保连接到正式环境
        }
      });

      if (result.result && result.result.success) {
        const serverTime = result.result.serverTime;
        console.log(`离线时间已保存到云端: ${new Date(serverTime).toLocaleString()}`);

        // 同时更新本地玩家数据
        player.lastOfflineTime = serverTime;
        this.game.gameStateManager.setPlayer(player);

        // 保存到本地存储作为备用
        wx.setStorageSync('lastOfflineTime', serverTime);

      } else {
        console.error('保存离线时间到云端失败:', result.result?.error);
        
        // 失败时使用本地时间作为备用方案
        const currentTime = Date.now();
        player.lastOfflineTime = currentTime;
        this.game.gameStateManager.setPlayer(player);
        wx.setStorageSync('lastOfflineTime', currentTime);
        console.log('使用本地时间作为备用方案');
      }

    } catch (error) {
      console.error('保存离线时间失败:', error);
      
      // 出现异常时使用本地时间作为备用方案
      try {
        const currentTime = Date.now();
        const player = this.game.gameStateManager.getPlayer();
        if (player) {
          player.lastOfflineTime = currentTime;
          this.game.gameStateManager.setPlayer(player);
        }
        wx.setStorageSync('lastOfflineTime', currentTime);
        console.log('异常情况下使用本地时间作为备用方案');
      } catch (backupError) {
        console.error('备用方案也失败了:', backupError);
      }
    }
  }

  /**
   * 加载离线时间并计算离线修炼收益
   * 在登录完成后调用
   */
  async loadOfflineTimeAndCalculateGains() {
    try {
      if (!this.game || !this.game.gameStateManager) {
        return;
      }

      const player = this.game.gameStateManager.getPlayer();
      if (!player) {
        return;
      }

      console.log('开始检查离线修炼收益...');

      // 从云端数据中获取上次离线时间
      let lastOfflineTime = player.lastOfflineTime;
      
      // 如果没有离线时间，尝试从本地存储加载
      if (!lastOfflineTime) {
        const localOfflineTime = wx.getStorageSync('lastOfflineTime');
        if (localOfflineTime) {
          lastOfflineTime = localOfflineTime;
          console.log(`从本地存储加载离线时间: ${new Date(localOfflineTime).toLocaleString()}`);
        }
      }

      // 如果有离线时间，计算离线收益
      if (lastOfflineTime) {
        // 获取服务器当前时间
        const serverTimeResult = await wx.cloud.callFunction({
          name: 'databaseService',
          data: {
            action: 'getServerTime',
            envType: 'prod'
          }
        });

        let currentTime = Date.now(); // 默认使用本地时间
        
        if (serverTimeResult.result && serverTimeResult.result.success) {
          currentTime = serverTimeResult.result.serverTime;
          console.log(`使用服务器时间计算收益: ${new Date(currentTime).toLocaleString()}`);
        } else {
          console.log('获取服务器时间失败，使用本地时间计算收益');
        }

        // 计算离线时长（毫秒）
        const offlineTime = currentTime - lastOfflineTime;
        
        // 只有离线时间大于1分钟才计算收益（避免频繁切换产生收益）
        if (offlineTime > 60000) {
          console.log(`计算离线收益，离线时长: ${Math.floor(offlineTime / 1000)}秒`);
          
                     // 延迟一点再显示离线收益弹窗，让游戏先完全加载
           setTimeout(() => {
             try {
               // 通过SceneManager访问静室场景实例
               if (this.game && this.game.sceneManager && this.game.sceneManager.scenes) {
                 const jingshiScene = this.game.sceneManager.scenes.jingshi;
                 
                 if (jingshiScene && typeof jingshiScene.checkAndUpdateMeditation === 'function') {
                   jingshiScene.checkAndUpdateMeditation();
                   console.log('离线修炼收益计算完成');
                 } else {
                   console.error('静室场景实例或checkAndUpdateMeditation方法不存在');
                   // 备用方案：通过require直接引用JingshiScene类
                   try {
                     const JingshiScene = require('../scenes/JingshiScene');
                     if (JingshiScene && typeof JingshiScene.checkAndUpdateMeditation === 'function') {
                       JingshiScene.checkAndUpdateMeditation();
                       console.log('通过备用方案计算离线修炼收益完成');
                     }
                   } catch (requireError) {
                     console.error('备用方案也失败了:', requireError);
                   }
                 }
               } else {
                 console.error('SceneManager或scenes不可用');
               }
             } catch (error) {
               console.error('计算离线收益失败:', error);
             }
           }, 2000);
        } else {
          console.log('离线时间过短，不计算收益');
        }
      } else {
        console.log('没有找到离线时间，可能是新玩家');
      }

    } catch (error) {
      console.error('加载离线时间和计算收益失败:', error);
    }
  }
}

export default LoginManager;
