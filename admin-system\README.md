# 修仙游戏后台管理系统

## 🎮 项目概述

修仙游戏后台管理系统是一个基于Node.js + Express + EJS的Web管理平台，专为微信小游戏"修仙六道"设计。

## ✨ 主要功能

- 🔐 **安全认证系统** - JWT令牌认证，管理员权限控制
- 👥 **玩家数据管理** - 查看、编辑、搜索玩家信息
- 📧 **邮件系统管理** - 完整的邮件管理功能
  - 📝 邮件模板管理：创建、编辑、删除邮件模板
  - 📮 批量邮件发送：支持全员发送和指定玩家发送
  - 🎁 附件奖励系统：支持资源、物品等多种奖励类型
  - 📊 邮件状态跟踪：实时查看阅读率、领取率统计
  - 👀 接收者详情：查看每个玩家的邮件阅读和领取状态
  - 🔄 智能重发功能：向未读玩家重新发送邮件
  - 🗑️ 邮件删除管理：支持软删除，保护数据安全
  - 🔍 历史记录搜索：按标题、状态、日期筛选邮件历史
- ⚔️ **游戏模板管理** - 功法、剑心、古宝模板的增删改查
- 📊 **数据统计面板** - 实时统计数据展示
- 📝 **操作日志审计** - 完整的管理员操作记录

## 🚀 快速开始

### 环境要求
- Node.js 14+
- npm 6+

### 安装依赖
```bash
cd admin-system
npm install
```

### 配置环境
1. 复制配置文件：
```bash
cp config.example.js config.js
```

2. 配置云函数环境ID（与游戏端保持一致）：
```javascript
// config.js
module.exports = {
  cloud: {
    envId: 'cloud1-9gzbxxbff827656f' // 游戏端云环境ID
  }
}
```

### 启动服务
```bash
npm start
```

## 🔐 登录信息

**默认管理员账号：**
- 用户名：`admin`
- 密码：`admin123`
- 权限：超级管理员

**运营账号：**
- 用户名：`operator`  
- 密码：`operator123`
- 权限：运营人员

## 🛠️ 最新修复

### v2.1.0 (2025-01-28) - 邮件系统全面升级
- ✨ **邮件历史管理功能** - 新增完整的邮件发送历史查看系统
  - 📊 **实时统计展示** - 邮件阅读率、领取率可视化进度条
  - 🔍 **高级搜索筛选** - 支持按标题、状态、日期筛选邮件历史
  - 📱 **响应式分页** - 优化的分页导航，支持大量历史记录
- 👀 **邮件详情查看** - 全新的邮件详情模态框
  - 📈 **统计信息展示** - 总接收人数、已读人数、已领取人数
  - 🎁 **附件奖励显示** - 直观的奖励类型和数量展示
  - ⚡ **快速操作按钮** - 一键查看接收者、重发、删除
- 🧑‍🤝‍🧑 **接收者状态管理** - 详细的玩家邮件状态追踪
  - 🔍 **多条件筛选** - 按阅读状态、领取状态、玩家ID筛选
  - ⏰ **时间戳记录** - 精确的阅读时间、领取时间显示
  - 📊 **实时统计** - 动态更新的接收者状态统计信息
- 🔄 **智能重发系统** - 向未读玩家批量重发邮件功能
  - 🎯 **精准定位** - 自动识别未读玩家进行重发
  - 📝 **操作审计** - 完整的重发操作日志记录
  - ✅ **成功率统计** - 重发成功率实时反馈
- 🗑️ **软删除机制** - 安全的邮件删除管理
  - 🛡️ **数据保护** - 软删除机制保护历史数据
  - 👁️ **状态区分** - 已删除邮件视觉区分显示
  - 📋 **完整记录** - 删除操作完整审计日志
- 🎨 **用户体验优化**
  - 🌈 **视觉优化** - 统一的设计风格和配色方案
  - ⚡ **性能提升** - 优化的数据加载和渲染机制
  - 📱 **移动端适配** - 响应式设计，支持各种屏幕尺寸

### v2.0.1 (2024-12-19) - 无限重定向修复版本

**问题描述：**
- 网页打开后出现无限刷新现象
- 控制台显示429 (Too Many Requests)错误
- `/api/auth/verify-token`接口被疯狂调用

**修复内容：**

#### 1. 前端认证逻辑优化
- ✅ 改进token获取和存储机制
- ✅ 修复无限重定向循环问题  
- ✅ 增加认证状态检查机制
- ✅ 优化错误处理和用户体验

#### 2. 后端路由优化
- ✅ 改进首页认证逻辑
- ✅ 增强错误处理机制
- ✅ 优化数据获取流程

#### 3. 速率限制调整
- ✅ 全局请求限制：1000 → 2000次/15分钟
- ✅ 登录尝试限制：5 → 10次/15分钟
- ✅ 新增Token验证限制：30次/分钟
- ✅ 静态资源跳过速率限制

#### 4. 性能优化
- ✅ 数据刷新频率：30秒 → 60秒
- ✅ 移除不必要的API调用
- ✅ 改进页面加载逻辑

**使用指南：**

1. **正常登录流程：**
   ```
   访问 http://localhost:3000 
   → 自动跳转到登录页
   → 输入用户名密码
   → 登录成功后跳转到管理面板
   ```

2. **如果遇到问题：**
   ```bash
   # 清理浏览器缓存和localStorage
   # 重启服务器
   npm start
   ```

3. **监控系统状态：**
   - 查看控制台日志
   - 检查网络请求频率
   - 监控错误信息

## 📊 系统架构

```
游戏客户端 ←→ 微信云开发数据库 ←→ 云函数(databaseService) ←→ 后台管理系统
```

**数据流向：**
1. 后台管理系统通过CloudFunctionAdapter调用游戏端云函数
2. 云函数执行数据库操作并返回结果
3. 后台系统展示和管理数据

## 🗂️ 目录结构

```
admin-system/
├── lib/                    # 核心库文件
│   ├── CloudFunctionAdapter.js  # 云函数调用适配器
│   └── DataService.js           # 数据服务封装
├── middleware/             # 中间件
│   ├── auth.js            # 认证和权限控制
│   └── audit.js           # 操作日志审计
├── routes/                # 路由文件
│   └── auth.js            # 认证相关路由
├── views/                 # EJS模板文件
│   ├── index.ejs          # 首页
│   ├── login.ejs          # 登录页
│   ├── players.ejs        # 玩家管理页
│   └── ...
├── config.example.js      # 配置文件示例
├── server.js              # 主服务器文件
└── package.json           # 项目依赖
```

## 🔧 开发指南

### 添加新功能
1. 在对应的路由文件中添加API端点
2. 创建或修改EJS模板文件
3. 添加前端JavaScript交互逻辑
4. 更新权限配置（如需要）

### 调试技巧
```bash
# 启用详细日志
DEBUG=admin-system:* npm start

# 查看实时日志
tail -f logs/admin.log
```

### 安全注意事项
- 定期更换JWT密钥
- 监控操作日志
- 限制管理员数量
- 定期备份数据

## 📝 更新日志

### v2.1.0 (2025-01-28) - 邮件系统全面升级
- ✨ **邮件历史管理功能** - 新增完整的邮件发送历史查看系统
  - 📊 **实时统计展示** - 邮件阅读率、领取率可视化进度条
  - 🔍 **高级搜索筛选** - 支持按标题、状态、日期筛选邮件历史
  - 📱 **响应式分页** - 优化的分页导航，支持大量历史记录
- 👀 **邮件详情查看** - 全新的邮件详情模态框
  - 📈 **统计信息展示** - 总接收人数、已读人数、已领取人数
  - 🎁 **附件奖励显示** - 直观的奖励类型和数量展示
  - ⚡ **快速操作按钮** - 一键查看接收者、重发、删除
- 🧑‍🤝‍🧑 **接收者状态管理** - 详细的玩家邮件状态追踪
  - 🔍 **多条件筛选** - 按阅读状态、领取状态、玩家ID筛选
  - ⏰ **时间戳记录** - 精确的阅读时间、领取时间显示
  - 📊 **实时统计** - 动态更新的接收者状态统计信息
- 🔄 **智能重发系统** - 向未读玩家批量重发邮件功能
  - 🎯 **精准定位** - 自动识别未读玩家进行重发
  - 📝 **操作审计** - 完整的重发操作日志记录
  - ✅ **成功率统计** - 重发成功率实时反馈
- 🗑️ **软删除机制** - 安全的邮件删除管理
  - 🛡️ **数据保护** - 软删除机制保护历史数据
  - 👁️ **状态区分** - 已删除邮件视觉区分显示
  - 📋 **完整记录** - 删除操作完整审计日志
- 🎨 **用户体验优化**
  - 🌈 **视觉优化** - 统一的设计风格和配色方案
  - ⚡ **性能提升** - 优化的数据加载和渲染机制
  - 📱 **移动端适配** - 响应式设计，支持各种屏幕尺寸

### v2.0.1 (2024-12-19) - 无限重定向修复版本
- 🐛 修复无限重定向问题
- 🐛 修复429错误
- ⚡ 性能优化
- 🔧 改进错误处理

### v2.0.0 (2024-12-18)
- 🎉 首次发布
- ✨ 完整的后台管理功能
- 🔐 安全认证系统
- 📊 数据统计面板

## 🤝 技术支持

如遇到问题，请：
1. 检查控制台错误日志
2. 确认网络连接
3. 验证配置文件
4. 查看操作日志

---

**开发团队：** 修仙六道开发组  
**最后更新：** 2025-01-28
