/**
 * 玩家数据管理器
 * 负责游戏内数据操作与云数据库的同步
 * 包括新玩家数据初始化和老玩家数据加载
 */

class PlayerDataManager {
  constructor(gameStateManager) {
    this.gameStateManager = gameStateManager;
    this.isInitialized = false;
    this.syncQueue = [];
    this.isSyncing = false;
    
    // 数据表映射
    this.TABLES = {
      PLAYERS: 'players',
      PLAYER_RES: 'player_res',
      PLAYER_TREASURES: 'player_treasures',
      PLAYER_SKILL: 'player_skill',
      PLAYER_ITEMS: 'player_items',
      SWORD_HEARTS: 'sword_hearts',
      SWORD_BONES: 'sword_bones',
      PLAYER_DONGF: 'player_dongf',
      PLAYER_ARENA: 'player_arena',
      PLAYER_IDLE: 'player_idle',
      P_SKILL_CUL: 'p_skill_cul',
      RECHARGE_REC: 'recharge_rec',
      GACHA_RECORD: 'gacha_record',
      BATTLE_RECOR: 'battle_recor',
      MAIL_TEMP: 'mail_temp',
      PLAYER_MAILS: 'player_mails',
      DAILY_TASKS: 'daily_tasks',
      ACTIVITY_PAR: 'activity_par',
      GAME_CONFIGS: 'game_configs'
    };
    
    // 绑定游戏状态变化事件
    this.bindGameStateEvents();
    
    console.log('玩家数据管理器初始化完成');
  }
  
  /**
   * 绑定游戏状态变化事件
   */
  bindGameStateEvents() {
    if (!this.gameStateManager) return;
    
    // 监听玩家等级变化
    this.gameStateManager.on('playerLevelUp', (data) => {
      this.handlePlayerLevelUp(data);
    });
    
    // 监听境界突破
    this.gameStateManager.on('realmBreakthrough', (data) => {
      this.handleRealmBreakthrough(data);
    });
    
    // 监听资源变化
    this.gameStateManager.on('resourcesChanged', (data) => {
      this.handleResourcesChanged(data);
    });
    
    // 监听古宝获得
    this.gameStateManager.on('treasureAcquired', (data) => {
      this.handleTreasureAcquired(data);
    });
    
    // 监听技能升级
    this.gameStateManager.on('skillUpgraded', (data) => {
      this.handleSkillUpgraded(data);
    });
    
    // 监听战斗结果
    this.gameStateManager.on('battleCompleted', (data) => {
      this.handleBattleCompleted(data);
    });
    
    // 监听充值记录
    this.gameStateManager.on('rechargeCompleted', (data) => {
      this.handleRechargeCompleted(data);
    });
    
    console.log('游戏状态事件绑定完成');
  }
  
  /**
   * 初始化玩家数据系统
   * 检查是否为新玩家，如果是则创建完整数据表，否则加载现有数据
   */
  async initializePlayerData() {
    try {
      console.log('开始初始化玩家数据系统...');
      
      // 检查玩家是否存在
      const isNewPlayer = await this.checkIfNewPlayer();
      
      if (isNewPlayer) {
        console.log('检测到新玩家，开始创建完整数据表...');
        await this.createNewPlayerData();
      } else {
        console.log('检测到老玩家，开始加载现有数据...');
        await this.loadExistingPlayerData();
      }
      
      this.isInitialized = true;
      console.log('玩家数据系统初始化完成');
      
      return true;
    } catch (error) {
      console.error('玩家数据系统初始化失败:', error);
      return false;
    }
  }
  
  /**
   * 检查是否为新玩家
   */
  async checkIfNewPlayer() {
    try {
      const result = await this.callDatabaseService('get', this.TABLES.PLAYERS, {
        query: {}
      });
      
      return !result.success || !result.data || result.data.length === 0;
    } catch (error) {
      console.log('检查玩家状态时出错，视为新玩家:', error);
      return true;
    }
  }
  
  /**
   * 创建新玩家的完整数据表
   */
  async createNewPlayerData() {
    try {
      console.log('开始为新玩家创建完整数据表...');
      
      const currentTime = Date.now();
      const gameState = this.gameStateManager.getFullStateForSave();
      const userInfo = this.getUserInfo();
      
      // 创建数据表的顺序（考虑依赖关系）
      const tableCreationTasks = [
        // 1. 玩家基础信息表
        {
          table: this.TABLES.PLAYERS,
          data: {
            nickname: userInfo.nickname || gameState.player?.nickname || '修仙者',
            avatar_url: userInfo.avatarUrl || gameState.player?.avatarUrl || '',
            server_id: 1,
            level: gameState.player?.level || 1,
            exp: gameState.player?.exp || 0,
            power: gameState.player?.power || 0,
            cultivation_realm: gameState.player?.cultivation_realm || '炼气期一层',
            dongfu_level: gameState.player?.dongfu_level || 1,
            vip_level: gameState.player?.vip_level || 0,
            total_recharge: gameState.player?.total_recharge || 0,
            last_vip_reward_time: null,
            last_login_time: currentTime,
            last_offline_time: null,
            registration_time: currentTime,
            game_settings: gameState.player?.game_settings || {}
          }
        },
        
        // 2. 玩家资源表
        {
          table: this.TABLES.PLAYER_RES,
          data: {
            xianyu: gameState.player?.resources?.xianyu || 1000,
            lingshi: gameState.player?.resources?.lingshi || 1000,
            sword_intent: gameState.player?.resources?.swordIntent || 0,
            lianlidian: gameState.player?.resources?.lianlidian || 100,
            spirit_stone: gameState.player?.resources?.spiritStone || 0,
            tiangang_stone: gameState.player?.resources?.tiangangStone || 0,
            xiuwei_point: gameState.player?.resources?.xiuweiPoint || 0,
            arena_point: gameState.player?.resources?.arenaPoint || 0,
            guild_contribution: gameState.player?.resources?.guildContribution || 0
          }
        },
        
        // 3. 剑骨系统表
        {
          table: this.TABLES.SWORD_BONES,
          data: {
            level: 1,
            rank: 1,
            total_attributes: {},
            upgrade_materials_used: {}
          }
        }
      ];
      
      // 批量创建数据表
      const results = [];
      for (const task of tableCreationTasks) {
        try {
          console.log(`创建 ${task.table} 表数据...`);
          const result = await this.callDatabaseService('create', task.table, {
            data: task.data
          });
          
          if (result.success) {
            console.log(`✓ ${task.table} 表创建成功`);
            results.push({ table: task.table, success: true, data: result.data });
          } else {
            console.error(`✗ ${task.table} 表创建失败:`, result.error);
            results.push({ table: task.table, success: false, error: result.error });
          }
        } catch (error) {
          console.error(`✗ ${task.table} 表创建异常:`, error);
          results.push({ table: task.table, success: false, error: error.message });
        }
        
        // 添加延迟避免请求过快
        await this.delay(200);
      }
      
      // 统计结果
      const successCount = results.filter(r => r.success).length;
      const totalCount = results.length;
      
      console.log(`新玩家数据表创建完成: ${successCount}/${totalCount} 成功`);
      
      if (successCount > 0) {
        wx.showToast({
          title: `新玩家数据创建成功 (${successCount}/${totalCount})`,
          icon: 'success',
          duration: 3000
        });
      }
      
      return results;
    } catch (error) {
      console.error('创建新玩家数据失败:', error);
      throw error;
    }
  }
  
  /**
   * 加载现有玩家数据
   */
  async loadExistingPlayerData() {
    try {
      console.log('开始加载现有玩家数据...');
      
      // 需要加载的数据表
      const tablesToLoad = [
        this.TABLES.PLAYERS,
        this.TABLES.PLAYER_RES,
        this.TABLES.SWORD_BONES
      ];
      
      const loadedData = {};
      
      // 并行加载所有表数据
      const loadPromises = tablesToLoad.map(async (table) => {
        try {
          const result = await this.callDatabaseService('get', table, {
            query: {}
          });
          
          if (result.success && result.data && result.data.length > 0) {
            loadedData[table] = result.data[0]; // 取第一条记录
            console.log(`✓ ${table} 数据加载成功`);
          } else {
            console.warn(`⚠ ${table} 数据不存在，将使用默认值`);
            loadedData[table] = null;
          }
        } catch (error) {
          console.error(`✗ ${table} 数据加载失败:`, error);
          loadedData[table] = null;
        }
      });
      
      await Promise.all(loadPromises);
      
      // 将加载的数据同步到游戏状态管理器
      this.syncLoadedDataToGameState(loadedData);
      
      console.log('现有玩家数据加载完成');
      
      wx.showToast({
        title: '玩家数据加载成功',
        icon: 'success',
        duration: 2000
      });
      
      return loadedData;
    } catch (error) {
      console.error('加载现有玩家数据失败:', error);
      throw error;
    }
  }
  
  /**
   * 将加载的数据同步到游戏状态管理器
   */
  syncLoadedDataToGameState(loadedData) {
    try {
      const gameState = this.gameStateManager.state;
      
      // 同步玩家基础信息
      if (loadedData[this.TABLES.PLAYERS]) {
        const playerData = loadedData[this.TABLES.PLAYERS];
        gameState.player.nickname = playerData.nickname || gameState.player.nickname;
        gameState.player.level = playerData.level || gameState.player.level;
        gameState.player.exp = playerData.exp || gameState.player.exp;
        gameState.player.power = playerData.power || gameState.player.power;
        gameState.player.cultivation_realm = playerData.cultivation_realm || gameState.player.cultivation_realm;
        gameState.player.dongfu_level = playerData.dongfu_level || gameState.player.dongfu_level;
        gameState.player.vip_level = playerData.vip_level || gameState.player.vip_level;
        gameState.player.total_recharge = playerData.total_recharge || gameState.player.total_recharge;
      }
      
      // 同步玩家资源
      if (loadedData[this.TABLES.PLAYER_RES]) {
        const resourceData = loadedData[this.TABLES.PLAYER_RES];
        gameState.player.resources = {
          xianyu: resourceData.xianyu || gameState.player.resources.xianyu,
          lingshi: resourceData.lingshi || gameState.player.resources.lingshi,
          swordIntent: resourceData.sword_intent || gameState.player.resources.swordIntent,
          lianlidian: resourceData.lianlidian || gameState.player.resources.lianlidian,
          spiritStone: resourceData.spirit_stone || 0,
          tiangangStone: resourceData.tiangang_stone || 0,
          xiuweiPoint: resourceData.xiuwei_point || 0,
          arenaPoint: resourceData.arena_point || 0,
          guildContribution: resourceData.guild_contribution || 0,
          lingli: resourceData.lingli || 0  // 添加灵力字段
        };
      }
      
      console.log('数据同步到游戏状态管理器完成');
      
      // 触发数据更新事件
      this.gameStateManager.emit('playerDataLoaded', gameState.player);
      
    } catch (error) {
      console.error('同步数据到游戏状态管理器失败:', error);
    }
  }
  
  /**
   * 处理玩家等级提升
   */
  async handlePlayerLevelUp(data) {
    console.log('处理玩家等级提升:', data);
    
    try {
      await this.updatePlayerBasicInfo({
        level: data.newLevel,
        exp: data.newExp,
        power: data.newPower
      });
      
      console.log(`玩家升级到 ${data.newLevel} 级，战力: ${data.newPower}`);
    } catch (error) {
      console.error('更新玩家等级失败:', error);
    }
  }
  
  /**
   * 处理境界突破
   */
  async handleRealmBreakthrough(data) {
    console.log('处理境界突破:', data);
    
    try {
      await this.updatePlayerBasicInfo({
        cultivation_realm: data.newRealm,
        power: data.newPower
      });
      
      console.log(`玩家突破到 ${data.newRealm}，战力: ${data.newPower}`);
    } catch (error) {
      console.error('更新玩家境界失败:', error);
    }
  }
  
  /**
   * 处理资源变化
   */
  async handleResourcesChanged(data) {
    console.log('处理资源变化:', data);
    
    try {
      // 构建资源更新数据
      const updateData = {};
      const resourceMapping = {
        xianyu: 'xianyu',
        lingshi: 'lingshi',
        swordIntent: 'sword_intent',
        lianlidian: 'lianlidian',
        spiritStone: 'spirit_stone',
        tiangangStone: 'tiangang_stone',
        xiuweiPoint: 'xiuwei_point',
        arenaPoint: 'arena_point',
        guildContribution: 'guild_contribution',
        lingli: 'lingli'  // 添加灵力映射
      };
      
      for (const [gameKey, dbKey] of Object.entries(resourceMapping)) {
        if (data.resources.hasOwnProperty(gameKey)) {
          updateData[dbKey] = data.resources[gameKey];
        }
      }
      
      if (Object.keys(updateData).length > 0) {
        updateData.updatedAt = Date.now();
        
        await this.callDatabaseService('update', this.TABLES.PLAYER_RES, {
          data: updateData
        });
        
        console.log('玩家资源更新成功:', updateData);
      }
    } catch (error) {
      console.error('更新玩家资源失败:', error);
    }
  }
  
  /**
   * 处理古宝获得
   */
  async handleTreasureAcquired(data) {
    console.log('处理古宝获得:', data);
    
    try {
      const treasureData = {
        treasure_id: data.treasureId,
        name: data.name,
        category: data.category,
        rarity: data.rarity,
        level: data.level || 1,
        max_level: data.maxLevel || 100,
        star: data.star || 0,
        base_attributes: data.baseAttributes || {},
        current_attributes: data.currentAttributes || {},
        is_equipped: data.isEquipped || false,
        acquired_time: Date.now()
      };
      
      await this.callDatabaseService('create', this.TABLES.PLAYER_TREASURES, {
        data: treasureData
      });
      
      console.log('古宝数据保存成功:', treasureData.name);
    } catch (error) {
      console.error('保存古宝数据失败:', error);
    }
  }
  
  /**
   * 处理技能升级
   */
  async handleSkillUpgraded(data) {
    console.log('处理技能升级:', data);
    
    try {
      const skillData = {
        skill_id: data.skillId,
        name: data.name,
        type: data.type,
        quality: data.quality,
        level: data.newLevel,
        max_level: data.maxLevel,
        stars: data.stars,
        power: data.power,
        current_attributes: data.currentAttributes || {},
        is_equipped: data.isEquipped || false,
        total_upgrade_cost: data.totalCost || 0
      };
      
      // 尝试更新现有技能，如果不存在则创建
      const updateResult = await this.callDatabaseService('update', this.TABLES.PLAYER_SKILL, {
        query: { skill_id: data.skillId },
        data: skillData
      });
      
      if (!updateResult.success) {
        // 如果更新失败，尝试创建新技能
        await this.callDatabaseService('create', this.TABLES.PLAYER_SKILL, {
          data: skillData
        });
      }
      
      console.log('技能数据更新成功:', skillData.name);
    } catch (error) {
      console.error('更新技能数据失败:', error);
    }
  }
  
  /**
   * 处理战斗完成
   */
  async handleBattleCompleted(data) {
    console.log('处理战斗完成:', data);
    
    try {
      const battleRecord = {
        battle_type: data.battleType,
        opponent_id: data.opponentId || null,
        battle_result: data.result,
        battle_rounds: data.rounds || 0,
        damage_dealt: data.damageDealt || 0,
        damage_taken: data.damageTaken || 0,
        rewards: data.rewards || {},
        battle_duration: data.duration || 0,
        battle_data: data.battleData || {},
        battle_time: Date.now()
      };
      
      await this.callDatabaseService('create', this.TABLES.BATTLE_RECOR, {
        data: battleRecord
      });
      
      // 如果是竞技场战斗，更新竞技场数据
      if (data.battleType === 'arena') {
        await this.updateArenaData(data);
      }
      
      console.log('战斗记录保存成功');
    } catch (error) {
      console.error('保存战斗记录失败:', error);
    }
  }
  
  /**
   * 处理充值完成
   */
  async handleRechargeCompleted(data) {
    console.log('处理充值完成:', data);
    
    try {
      const rechargeRecord = {
        order_id: data.orderId,
        amount: data.amount,
        xianyu_amount: data.xianyuAmount,
        bonus_xianyu: data.bonusXianyu || 0,
        package_id: data.packageId,
        payment_method: data.paymentMethod || 'wechat',
        status: data.status || 'success',
        transaction_id: data.transactionId,
        recharge_time: Date.now(),
        callback_time: Date.now()
      };
      
      await this.callDatabaseService('create', this.TABLES.RECHARGE_REC, {
        data: rechargeRecord
      });
      
      // 更新玩家VIP等级和总充值
      await this.updatePlayerBasicInfo({
        total_recharge: data.totalRecharge,
        vip_level: data.newVipLevel
      });
      
      console.log('充值记录保存成功');
    } catch (error) {
      console.error('保存充值记录失败:', error);
    }
  }
  
  /**
   * 更新玩家基础信息
   */
  async updatePlayerBasicInfo(updateData) {
    try {
      updateData.updatedAt = Date.now();
      
      const result = await this.callDatabaseService('update', this.TABLES.PLAYERS, {
        data: updateData
      });
      
      if (result.success) {
        console.log('玩家基础信息更新成功:', updateData);
      } else {
        console.error('玩家基础信息更新失败:', result.error);
      }
      
      return result;
    } catch (error) {
      console.error('更新玩家基础信息异常:', error);
      throw error;
    }
  }
  
  /**
   * 更新竞技场数据
   */
  async updateArenaData(battleData) {
    try {
      const updateData = {
        updatedAt: Date.now()
      };
      
      if (battleData.result === 'win') {
        updateData.season_wins = this.gameStateManager.arenaManager.seasonWins + 1;
        updateData.total_wins = this.gameStateManager.arenaManager.totalWins + 1;
      } else if (battleData.result === 'lose') {
        updateData.season_losses = this.gameStateManager.arenaManager.seasonLosses + 1;
        updateData.total_losses = this.gameStateManager.arenaManager.totalLosses + 1;
      }
      
      if (battleData.newRank) {
        updateData.current_rank = battleData.newRank;
        if (battleData.newRank < this.gameStateManager.arenaManager.highestRank) {
          updateData.highest_rank = battleData.newRank;
        }
      }
      
      await this.callDatabaseService('update', this.TABLES.PLAYER_ARENA, {
        data: updateData
      });
      
      console.log('竞技场数据更新成功');
    } catch (error) {
      console.error('更新竞技场数据失败:', error);
    }
  }
  
  /**
   * 调用数据库服务
   */
  async callDatabaseService(action, table, params = {}) {
    try {
      const requestData = {
        action: action,
        table: table,
        ...params
      };
      
      console.log(`调用数据库服务: ${action} ${table}`, requestData);
      
      const result = await wx.cloud.callFunction({
        name: 'databaseService',
        data: requestData
      });
      
      if (result.result) {
        return result.result;
      } else {
        throw new Error('云函数调用失败: ' + (result.errMsg || '未知错误'));
      }
    } catch (error) {
      console.error('数据库服务调用失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取用户信息
   */
  getUserInfo() {
    try {
      // 从多个来源获取用户信息
      const sources = [
        () => game.user,
        () => wx.getStorageSync('userInfo'),
        () => this.gameStateManager.state.player
      ];
      
      for (const getSource of sources) {
        try {
          const source = getSource();
          if (source && (source.nickName || source.nickname)) {
            return {
              nickname: source.nickName || source.nickname || '修仙者',
              avatarUrl: source.avatarUrl || source.avatar_url || ''
            };
          }
        } catch (err) {
          continue;
        }
      }
      
      return {
        nickname: '修仙者',
        avatarUrl: ''
      };
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return {
        nickname: '修仙者',
        avatarUrl: ''
      };
    }
  }
  
  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  /**
   * 手动同步所有数据
   */
  async syncAllData() {
    try {
      console.log('开始手动同步所有数据...');
      
      const gameState = this.gameStateManager.getFullStateForSave();
      const userInfo = this.getUserInfo();
      
      // 同步玩家基础信息
      await this.updatePlayerBasicInfo({
        nickname: userInfo.nickname,
        level: gameState.player?.level,
        exp: gameState.player?.exp,
        power: gameState.player?.power,
        cultivation_realm: gameState.player?.cultivation_realm
      });
      
      // 同步资源信息
      if (gameState.player?.resources) {
        await this.handleResourcesChanged({ resources: gameState.player.resources });
      }
      
      console.log('手动同步完成');
      
      wx.showToast({
        title: '数据同步成功',
        icon: 'success',
        duration: 2000
      });
      
    } catch (error) {
      console.error('手动同步失败:', error);
      wx.showToast({
        title: '同步失败: ' + error.message,
        icon: 'none',
        duration: 2000
      });
    }
  }
}

export default PlayerDataManager; 