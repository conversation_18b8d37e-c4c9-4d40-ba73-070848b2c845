---
description: 
globs: 
alwaysApply: false
---
# Development Guidelines for WeChat Mini-Game

## Development Philosophy
This project follows specific development principles for WeChat mini-game development, emphasizing simplicity and user-friendly design for a middle school student user.

## Code Organization Principles

### Module Structure
- **Managers**: Handle cross-cutting concerns and global state
- **Scenes**: Represent game screens and user interfaces
- **Models**: Define data structures and business logic
- **UI Components**: Reusable interface elements
- **Utils**: Helper functions and utilities

### File Naming Conventions
- Use PascalCase for class files: `GameStateManager.js`, `MainScene.js`
- Use camelCase for utility files: `eventEmitter.js`, `appContext.js`
- Scene files end with "Scene": `CharacterDetailScene.js`
- Manager files end with "Manager": `DatabaseManager.js`

## WeChat Mini-Game Specific Guidelines

### Framework Usage
- Use **CommonJS** syntax (`import`/`export`) throughout the codebase
- No external npm packages - rely on WeChat mini-game native APIs
- Canvas-based rendering using WeChat's game framework
- Cloud development for backend services

### Performance Considerations
- Implement proper memory management in scene transitions
- Use resource preloading for game assets
- Optimize Canvas rendering operations
- Implement efficient touch event handling

### Data Management
- Primary data storage via [GameStateManager.js](mdc:js/managers/GameStateManager.js)
- Cloud sync through [DatabaseManager.js](mdc:js/managers/DatabaseManager.js)
- Local storage as fallback mechanism
- Auto-save functionality via [AutoSaveManager.js](mdc:js/managers/AutoSaveManager.js)

## Scene Development Pattern

### Scene Lifecycle
All scenes must extend [BaseScene.js](mdc:js/scenes/BaseScene.js) and implement:
```javascript
class NewScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager) {
    super(ctx, screenWidth, screenHeight, sceneManager);
    this.initUI();
  }
  
  onShow(params) { /* Scene activation logic */ }
  onHide() { /* Cleanup logic */ }
  updateScene() { /* Game loop updates */ }
  drawScene() { /* Rendering logic */ }
}
```

### UI Component Integration
- Use existing UI components from [ui/](mdc:js/ui) directory
- Create reusable components for common interface elements
- Follow consistent styling patterns across scenes

## Data Access Patterns

### Game State Access
- Access global state via `AppContext.game.gameStateManager`
- Never directly modify state objects - use manager methods
- Emit events for state changes that other systems need to know about

### Database Operations
- All cloud database operations through [DatabaseManager.js](mdc:js/managers/DatabaseManager.js)
- Implement proper error handling and retry logic
- Cache frequently accessed data locally

## Cloud Development

### Database Schema
Reference [database_design.md](mdc:database_design.md) for complete schema:
- Follow established table naming conventions
- Include proper indexing for performance
- Maintain data consistency across collections

### Cloud Functions
Located in [cloudfunctions/](mdc:cloudfunctions) directory:
- `login`: User authentication and initialization
- `updateUserData`: Sync player data to cloud
- `getOpenId`: Retrieve user OpenID
- `giveReward` & `claimMailReward`: Reward distribution

## UI/UX Guidelines

### Touch Interface
- Implement responsive touch handling in scenes
- Provide visual feedback for user interactions
- Support different screen sizes and orientations

### Visual Design
- Maintain consistent visual theme across scenes
- Use appropriate cultivation/martial arts themed elements
- Optimize for mobile device viewing

## Testing and Debugging

### Development Environment
- Use WeChat DevTools for debugging
- Test on multiple device sizes
- Verify cloud function integration

### Error Handling
- Implement comprehensive error catching
- Provide user-friendly error messages
- Log errors for debugging purposes

## Documentation Requirements
- Update [README.md](mdc:README.md) when adding major features
- Document new game systems and mechanics
- Maintain clear code comments in Chinese for user understanding

