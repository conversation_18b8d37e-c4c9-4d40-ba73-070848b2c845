/**
 * 数据库管理器 - 云函数版
 * 负责与云函数进行数据交互
 */

class DatabaseManager {
  constructor() {
    // 初始化云数据库客户端
    this.cloudDBClient = null;
    this.initialized = false;
    this.initializationPromise = null;

    // 延迟初始化（异步）
    this.initializationPromise = this.initCloudDBClient();

    // 表名常量 - 使用优化后的数据库设计
    this.TABLES = {
      PLAYERS: 'players',
      PLAYER_RESOURCES: 'player_resources',
      CHARACTERS: 'characters',
      PLAYER_TREASURES: 'player_treasures',
      PLAYER_SKILLS: 'player_skills',
      PLAYER_ITEMS: 'player_items',
      PLAYER_SWORD_HEARTS: 'player_sword_hearts',
      PLAYER_SWORD_BONES: 'player_sword_bones',
      PLAYER_DONGFU: 'player_dongfu',
      PLAYER_ARENA: 'player_arena',
      PLAYER_IDLE: 'player_idle',
      PLAYER_SKILL_CULTIVATION: 'player_skill_cultivation',
      RECHARGE_RECORDS: 'recharge_records',
      GACHA_RECORDS: 'gacha_records',
      BATTLE_RECORDS: 'battle_records',
      MAIL_TEMPLATES: 'mail_templates',
      PLAYER_MAILS: 'player_mails',
      DAILY_TASKS: 'daily_tasks',
      ACTIVITY_PARTICIPATION: 'activity_participation',
      GAME_CONFIGS: 'game_configs'
    };
  }

  /**
   * 初始化云数据库客户端
   */
  async initCloudDBClient() {
    try {
      console.log('开始初始化云数据库客户端...');
      
      // 检查是否在微信小程序环境中
      if (typeof wx === 'undefined') {
        console.warn('不在微信小程序环境中，跳过云数据库客户端初始化');
        return;
      }

      // 检查云开发是否可用
      if (!wx.cloud) {
        console.warn('云开发不可用，跳过云数据库客户端初始化');
        return;
      }

      // 初始化云开发
      try {
        wx.cloud.init({
          env: 'cloud1-9gzbxxbff827656f', // 使用正确的云开发环境ID
          traceUser: true
        });
        console.log('微信云开发初始化成功');

        // 检查CloudDBClient的可用性
        console.log('开始检查CloudDBClient可用性...');
        console.log('window对象存在:', typeof window !== 'undefined');
        console.log('window.CloudDBClient类型:', typeof window !== 'undefined' ? typeof window.CloudDBClient : '无');
        console.log('AppContext对象存在:', typeof AppContext !== 'undefined');
        console.log('AppContext.CloudDBClient类型:', typeof AppContext !== 'undefined' ? typeof AppContext.CloudDBClient : '无');

        // 直接使用动态导入
        try {
          // 简化初始化，直接创建CloudDBClient实例
          const CloudDBClientClass = (typeof AppContext !== 'undefined' && AppContext.CloudDBClient) 
            ? AppContext.CloudDBClient 
            : (typeof window !== 'undefined' && window.CloudDBClient) 
              ? window.CloudDBClient 
              : null;

          if (!CloudDBClientClass) {
            throw new Error('CloudDBClient类在AppContext和window对象中都未找到');
          }

          if (typeof CloudDBClientClass !== 'function') {
            throw new Error(`CloudDBClient不是构造函数，类型为: ${typeof CloudDBClientClass}`);
          }

          // 创建实例
          this.cloudDBClient = new CloudDBClientClass();
          console.log('CloudDBClient实例创建成功');
          
          this.initialized = true;
          console.log('云数据库客户端初始化成功');

        } catch (clientError) {
          console.error('CloudDBClient创建失败:', clientError);
          
          // 回退方案：手动创建一个基本的客户端
          console.log('尝试回退方案：创建基本客户端...');
          this.cloudDBClient = this.createFallbackClient();
          this.initialized = true;
          console.log('回退方案初始化成功');
        }

      } catch (initError) {
        console.error('云数据库客户端初始化失败:', initError);
        this.initialized = false;
      }
    } catch (error) {
      console.error('云数据库客户端初始化过程中出现错误:', error);
      this.initialized = false;
    }
  }

  /**
   * 创建回退客户端
   */
  createFallbackClient() {
    return {
      syncPlayerData: async (gameState) => {
        console.log('使用回退客户端保存数据...');
        // 简单的本地保存逻辑
        try {
          wx.setStorageSync('fallback_game_state', gameState);
          console.log('数据已保存到本地存储（回退模式）');
          return { success: true, message: '数据已保存到本地（回退模式）' };
        } catch (error) {
          throw new Error('本地存储失败: ' + error.message);
        }
      },
      // 添加其他必要的方法
      getPlayerFullData: async () => {
        try {
          const data = wx.getStorageSync('fallback_game_state');
          return data || null;
        } catch (error) {
          return null;
        }
      }
    };
  }

  /**
   * 检查数据库客户端是否已初始化
   */
  isInitialized() {
    return this.initialized && this.cloudDBClient !== null;
  }

  /**
   * 等待数据库客户端初始化完成
   */
  async waitForInitialization() {
    if (this.initializationPromise) {
      await this.initializationPromise;
    }
    return this.isInitialized();
  }

  /**
   * 获取当前用户的openid
   */
  getCurrentOpenId() {
    try {
      // 检查是否在微信小程序环境中
      if (typeof wx === 'undefined') {
        console.warn('不在微信小程序环境中，无法获取openid');
        return null;
      }

      // 尝试从本地存储获取openid
      const openid = wx.getStorageSync('openid');
      if (openid) {
        return openid;
      }

      // 如果本地存储没有，尝试从全局变量获取
      if (typeof window !== 'undefined' && window.userOpenId) {
        return window.userOpenId;
      }

      // 尝试从游戏实例的登录管理器获取
      if (typeof AppContext !== 'undefined' &&
          AppContext &&
          AppContext.game &&
          AppContext.game.loginManager &&
          AppContext.game.loginManager.userOpenId) {
        return AppContext.game.loginManager.userOpenId;
      }

      // 如果都没有，返回null
      console.warn('未找到用户openid，可能需要重新登录');
      return null;
    } catch (error) {
      console.error('获取openid时出错:', error);
      return null;
    }
  }

  // ==================== 玩家数据相关 ====================

  /**
   * 获取玩家数据
   */
  async getPlayerData() {
    try {
      // 检查数据库客户端是否已初始化
      if (!this.isInitialized()) {
        console.warn('数据库客户端未初始化，跳过获取玩家数据');
        return null;
      }

      // 使用云函数获取完整玩家数据
      const fullData = await this.cloudDBClient.getPlayerFullData({
        includeResources: true,
        includeCharacters: false,
        includeItems: false
      });

      if (fullData && fullData.player) {
        // 合并玩家基础数据和资源数据
        const playerData = {
          ...fullData.player,
          ...fullData.resources
        };
        return playerData;
      } else {
        // 如果没有数据，创建默认玩家数据
        const openid = this.getCurrentOpenId();
        if (openid) {
          return await this.createDefaultPlayerData(openid);
        }
        return null;
      }
    } catch (error) {
      console.error('获取玩家数据失败:', error);
      
      // 降级处理：尝试创建新玩家
      try {
        const openid = this.getCurrentOpenId();
        if (openid) {
          console.log('尝试创建新玩家数据...');
          return await this.createDefaultPlayerData(openid);
        }
      } catch (createError) {
        console.error('创建默认玩家数据也失败:', createError);
      }
      
      return null;
    }
  }

  /**
   * 创建默认玩家数据
   */
  async createDefaultPlayerData(openid) {
    try {
      console.log('正在创建默认玩家数据...');

      // 使用云函数创建新玩家
      const result = await this.cloudDBClient.createPlayer({
        nickname: '修仙者',
        avatarUrl: '',
        serverName: '青云门'
      });

      console.log('默认玩家数据创建成功:', result);

      // 重新获取完整数据
      const fullData = await this.cloudDBClient.getPlayerFullData({
        includeResources: true,
        includeCharacters: false,
        includeItems: false
      });

      if (fullData && fullData.player) {
        // 合并玩家基础数据和资源数据
        const playerData = {
          ...fullData.player,
          ...fullData.resources
        };
        return playerData;
      }

      throw new Error('创建玩家后无法获取数据');
    } catch (error) {
      console.error('创建默认玩家数据失败:', error);
      throw error;
    }
  }

  /**
   * 更新玩家数据
   */
  async updatePlayerData(updateData) {
    try {
      // 检查数据库是否已初始化
      if (!this.isInitialized()) {
        console.warn('数据库未初始化，跳过更新玩家数据');
        return null;
      }

      const openid = this.getCurrentOpenId();
      if (!openid) {
        console.warn('未获取到用户openid，跳过更新玩家数据');
        return null;
      }

      // 添加更新时间
      updateData.updatedAt = Date.now();

      console.log('正在更新玩家数据:', { openid, updateData });

      const res = await this.db.collection(this.TABLES.PLAYERS)
        .where({ _openid: openid })
        .update({
          data: updateData
        });

      console.log('玩家数据更新结果:', res);
      return res;
    } catch (error) {
      console.error('更新玩家数据失败:', error);
      return null;
    }
  }

  /**
   * 更新玩家资源
   */
  async updatePlayerResources(resources) {
    try {
      const updateData = {};

      if (resources.xianyu !== undefined) updateData.xianyu = resources.xianyu;
      if (resources.lingshi !== undefined) updateData.lingshi = resources.lingshi;
      if (resources.sword_intent !== undefined) updateData.sword_intent = resources.sword_intent;
      if (resources.lianlidian !== undefined) updateData.lianlidian = resources.lianlidian;

      return await this.updatePlayerData(updateData);
    } catch (error) {
      console.error('更新玩家资源失败:', error);
      throw error;
    }
  }

  // ==================== 角色数据相关 ====================

  /**
   * 获取玩家所有角色
   */
  async getPlayerCharacters() {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const res = await this.db.collection(this.TABLES.CHARACTERS)
        .where({ _openid: openid })
        .get();

      return res.data;
    } catch (error) {
      console.error('获取角色数据失败:', error);
      throw error;
    }
  }

  /**
   * 添加角色
   */
  async addCharacter(characterData) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const data = {
        ...characterData,
        _openid: openid,
        created_at: new Date(),
        updated_at: new Date()
      };

      const res = await this.db.collection(this.TABLES.CHARACTERS).add({
        data: data
      });

      return { ...data, _id: res._id };
    } catch (error) {
      console.error('添加角色失败:', error);
      throw error;
    }
  }

  /**
   * 更新角色数据
   */
  async updateCharacter(characterId, updateData) {
    try {
      // 检查数据库是否已初始化
      if (!this.isInitialized()) {
        console.warn('数据库未初始化，跳过更新角色数据');
        return null;
      }

      const openid = this.getCurrentOpenId();
      if (!openid) {
        console.warn('未获取到用户openid，跳过更新角色数据');
        return null;
      }

      updateData.updated_at = new Date();

      const res = await this.db.collection(this.TABLES.CHARACTERS)
        .where({
          _openid: openid,
          character_id: characterId
        })
        .update({
          data: updateData
        });

      return res;
    } catch (error) {
      console.error('更新角色数据失败:', error);
      return null;
    }
  }

  /**
   * 删除角色
   */
  async deleteCharacter(characterId) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const res = await this.db.collection(this.TABLES.CHARACTERS)
        .where({
          _openid: openid,
          character_id: characterId
        })
        .remove();

      return res;
    } catch (error) {
      console.error('删除角色失败:', error);
      throw error;
    }
  }

  // ==================== 装备数据相关 ====================

  /**
   * 获取玩家所有装备
   */
  async getPlayerEquipments() {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const res = await this.db.collection(this.TABLES.EQUIPMENTS)
        .where({ _openid: openid })
        .get();

      return res.data;
    } catch (error) {
      console.error('获取装备数据失败:', error);
      throw error;
    }
  }

  /**
   * 添加装备
   */
  async addEquipment(equipmentData) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const data = {
        ...equipmentData,
        _openid: openid,
        created_at: new Date(),
        updated_at: new Date()
      };

      const res = await this.db.collection(this.TABLES.EQUIPMENTS).add({
        data: data
      });

      return { ...data, _id: res._id };
    } catch (error) {
      console.error('添加装备失败:', error);
      throw error;
    }
  }

  /**
   * 更新装备数据
   */
  async updateEquipment(equipmentId, updateData) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      updateData.updated_at = new Date();

      const res = await this.db.collection(this.TABLES.EQUIPMENTS)
        .where({
          _openid: openid,
          equipment_id: equipmentId
        })
        .update({
          data: updateData
        });

      return res;
    } catch (error) {
      console.error('更新装备数据失败:', error);
      throw error;
    }
  }

  /**
   * 删除装备
   */
  async deleteEquipment(equipmentId) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const res = await this.db.collection(this.TABLES.EQUIPMENTS)
        .where({
          _openid: openid,
          equipment_id: equipmentId
        })
        .remove();

      return res;
    } catch (error) {
      console.error('删除装备失败:', error);
      throw error;
    }
  }

  // ==================== 物品数据相关 ====================

  /**
   * 获取玩家所有物品
   */
  async getPlayerItems() {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const res = await this.db.collection(this.TABLES.ITEMS)
        .where({ _openid: openid })
        .get();

      return res.data;
    } catch (error) {
      console.error('获取物品数据失败:', error);
      throw error;
    }
  }

  /**
   * 添加物品
   */
  async addItem(itemData) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      // 检查是否已存在相同物品，如果存在则增加数量
      const existingRes = await this.db.collection(this.TABLES.ITEMS)
        .where({
          _openid: openid,
          item_id: itemData.item_id
        })
        .get();

      if (existingRes.data.length > 0) {
        // 物品已存在，增加数量
        const existingItem = existingRes.data[0];
        const newCount = existingItem.count + (itemData.count || 1);

        await this.db.collection(this.TABLES.ITEMS)
          .doc(existingItem._id)
          .update({
            data: {
              count: newCount,
              updated_at: new Date()
            }
          });

        return { ...existingItem, count: newCount };
      } else {
        // 新物品，直接添加
        const data = {
          ...itemData,
          _openid: openid,
          created_at: new Date(),
          updated_at: new Date()
        };

        const res = await this.db.collection(this.TABLES.ITEMS).add({
          data: data
        });

        return { ...data, _id: res._id };
      }
    } catch (error) {
      console.error('添加物品失败:', error);
      throw error;
    }
  }

  /**
   * 更新物品数量
   */
  async updateItemCount(itemId, count) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      if (count <= 0) {
        // 数量为0或负数，删除物品
        return await this.deleteItem(itemId);
      }

      const res = await this.db.collection(this.TABLES.ITEMS)
        .where({
          _openid: openid,
          item_id: itemId
        })
        .update({
          data: {
            count: count,
            updated_at: new Date()
          }
        });

      return res;
    } catch (error) {
      console.error('更新物品数量失败:', error);
      throw error;
    }
  }

  /**
   * 删除物品
   */
  async deleteItem(itemId) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const res = await this.db.collection(this.TABLES.ITEMS)
        .where({
          _openid: openid,
          item_id: itemId
        })
        .remove();

      return res;
    } catch (error) {
      console.error('删除物品失败:', error);
      throw error;
    }
  }

  // ==================== 技能数据相关 ====================

  /**
   * 获取玩家所有技能
   */
  async getPlayerSkills() {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const res = await this.db.collection(this.TABLES.SKILLS)
        .where({ _openid: openid })
        .get();

      return res.data;
    } catch (error) {
      console.error('获取技能数据失败:', error);
      throw error;
    }
  }

  /**
   * 添加技能
   */
  async addSkill(skillData) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const data = {
        ...skillData,
        _openid: openid,
        created_at: new Date(),
        updated_at: new Date()
      };

      const res = await this.db.collection(this.TABLES.SKILLS).add({
        data: data
      });

      return { ...data, _id: res._id };
    } catch (error) {
      console.error('添加技能失败:', error);
      throw error;
    }
  }

  /**
   * 更新技能数据
   */
  async updateSkill(skillId, updateData) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      updateData.updated_at = new Date();

      const res = await this.db.collection(this.TABLES.SKILLS)
        .where({
          _openid: openid,
          skill_id: skillId
        })
        .update({
          data: updateData
        });

      return res;
    } catch (error) {
      console.error('更新技能数据失败:', error);
      throw error;
    }
  }

  // ==================== 剑心数据相关 ====================

  /**
   * 获取玩家所有剑心
   */
  async getPlayerSwordHearts() {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const res = await this.db.collection(this.TABLES.SWORD_HEARTS)
        .where({ _openid: openid })
        .get();

      return res.data;
    } catch (error) {
      console.error('获取剑心数据失败:', error);
      throw error;
    }
  }

  /**
   * 添加或更新剑心
   */
  async savePlayerSwordHearts(swordHeartsData) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      // 先删除现有的剑心数据
      await this.db.collection(this.TABLES.SWORD_HEARTS)
        .where({ _openid: openid })
        .remove();

      // 批量添加新的剑心数据
      const promises = swordHeartsData.map(swordHeart => {
        const data = {
          ...swordHeart,
          _openid: openid,
          created_at: new Date(),
          updated_at: new Date()
        };

        return this.db.collection(this.TABLES.SWORD_HEARTS).add({
          data: data
        });
      });

      const results = await Promise.all(promises);
      return results;
    } catch (error) {
      console.error('保存剑心数据失败:', error);
      throw error;
    }
  }

  // ==================== 剑骨数据相关 ====================

  /**
   * 获取玩家剑骨数据
   */
  async getPlayerSwordBone() {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const res = await this.db.collection(this.TABLES.SWORD_BONES)
        .where({ _openid: openid })
        .get();

      if (res.data.length > 0) {
        return res.data[0];
      } else {
        // 创建默认剑骨数据
        return await this.createDefaultSwordBone(openid);
      }
    } catch (error) {
      console.error('获取剑骨数据失败:', error);
      throw error;
    }
  }

  /**
   * 创建默认剑骨数据
   */
  async createDefaultSwordBone(openid) {
    try {
      const defaultData = {
        _openid: openid,
        level: 0,
        rank: 0,
        created_at: new Date(),
        updated_at: new Date()
      };

      const res = await this.db.collection(this.TABLES.SWORD_BONES).add({
        data: defaultData
      });

      return { ...defaultData, _id: res._id };
    } catch (error) {
      console.error('创建默认剑骨数据失败:', error);
      throw error;
    }
  }

  /**
   * 更新剑骨数据
   */
  async updateSwordBone(updateData) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      updateData.updated_at = new Date();

      const res = await this.db.collection(this.TABLES.SWORD_BONES)
        .where({ _openid: openid })
        .update({
          data: updateData
        });

      return res;
    } catch (error) {
      console.error('更新剑骨数据失败:', error);
      throw error;
    }
  }

  // ==================== 洞府系统数据相关 ====================

  /**
   * 获取玩家洞府系统数据
   */
  async getPlayerDongfuSystem() {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const res = await this.db.collection(this.TABLES.DONGFU_SYSTEM)
        .where({ _openid: openid })
        .get();

      if (res.data.length > 0) {
        return res.data[0];
      } else {
        // 创建默认洞府系统数据
        return await this.createDefaultDongfuSystem(openid);
      }
    } catch (error) {
      console.error('获取洞府系统数据失败:', error);
      throw error;
    }
  }

  /**
   * 创建默认洞府系统数据
   */
  async createDefaultDongfuSystem(openid) {
    try {
      const defaultData = {
        _openid: openid,
        current_lingqi: 0,
        last_collect_time: null,
        cultivation_start_time: null,
        cultivation_character_id: null,
        created_at: new Date(),
        updated_at: new Date()
      };

      const res = await this.db.collection(this.TABLES.DONGFU_SYSTEM).add({
        data: defaultData
      });

      return { ...defaultData, _id: res._id };
    } catch (error) {
      console.error('创建默认洞府系统数据失败:', error);
      throw error;
    }
  }

  /**
   * 更新洞府系统数据
   */
  async updateDongfuSystem(updateData) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      updateData.updated_at = new Date();

      const res = await this.db.collection(this.TABLES.DONGFU_SYSTEM)
        .where({ _openid: openid })
        .update({
          data: updateData
        });

      return res;
    } catch (error) {
      console.error('更新洞府系统数据失败:', error);
      throw error;
    }
  }

  // ==================== 充值记录相关 ====================

  /**
   * 添加充值记录
   */
  async addRechargeRecord(rechargeData) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const data = {
        ...rechargeData,
        _openid: openid,
        created_at: new Date()
      };

      const res = await this.db.collection(this.TABLES.RECHARGE_RECORDS).add({
        data: data
      });

      return { ...data, _id: res._id };
    } catch (error) {
      console.error('添加充值记录失败:', error);
      throw error;
    }
  }

  /**
   * 获取玩家充值记录
   */
  async getPlayerRechargeRecords() {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const res = await this.db.collection(this.TABLES.RECHARGE_RECORDS)
        .where({ _openid: openid })
        .orderBy('created_at', 'desc')
        .get();

      return res.data;
    } catch (error) {
      console.error('获取充值记录失败:', error);
      throw error;
    }
  }

  /**
   * 更新充值记录状态
   */
  async updateRechargeRecordStatus(orderId, status) {
    try {
      const openid = this.getCurrentOpenId();
      if (!openid) {
        throw new Error('未获取到用户openid');
      }

      const res = await this.db.collection(this.TABLES.RECHARGE_RECORDS)
        .where({
          _openid: openid,
          order_id: orderId
        })
        .update({
          data: {
            status: status,
            updated_at: new Date()
          }
        });

      return res;
    } catch (error) {
      console.error('更新充值记录状态失败:', error);
      throw error;
    }
  }

  // ==================== 邮箱系统相关 ====================

  /**
   * 获取玩家邮件列表
   */
  async getPlayerMails() {
    try {
      if (!this.isInitialized()) {
        console.warn('数据库未初始化，跳过获取邮件列表');
        return [];
      }

      const openid = this.getCurrentOpenId();
      if (!openid) {
        console.warn('未获取到用户openid，跳过获取邮件列表');
        return [];
      }

      // 获取玩家的邮件接收记录
      const res = await this.db.collection(this.TABLES.MAIL_RECIPIENTS)
        .where({
          _openid: openid,
          deleted: false
        })
        .orderBy('created_at', 'desc')
        .get();

      return res.data || [];
    } catch (error) {
      console.error('获取邮件列表失败:', error);
      return [];
    }
  }

  /**
   * 标记邮件为已读
   */
  async markMailAsRead(mailId) {
    try {
      if (!this.isInitialized()) {
        console.warn('数据库未初始化，跳过标记邮件已读');
        return null;
      }

      const openid = this.getCurrentOpenId();
      if (!openid) {
        console.warn('未获取到用户openid，跳过标记邮件已读');
        return null;
      }

      const res = await this.db.collection(this.TABLES.MAIL_RECIPIENTS)
        .where({
          _openid: openid,
          mail_id: mailId
        })
        .update({
          data: {
            is_read: true,
            read_at: new Date()
          }
        });

      return res;
    } catch (error) {
      console.error('标记邮件已读失败:', error);
      return null;
    }
  }

  /**
   * 领取邮件奖励
   */
  async claimMailReward(mailId) {
    try {
      if (!this.isInitialized()) {
        console.warn('数据库未初始化，跳过领取邮件奖励');
        return null;
      }

      const openid = this.getCurrentOpenId();
      if (!openid) {
        console.warn('未获取到用户openid，跳过领取邮件奖励');
        return null;
      }

      const res = await this.db.collection(this.TABLES.MAIL_RECIPIENTS)
        .where({
          _openid: openid,
          mail_id: mailId
        })
        .update({
          data: {
            is_claimed: true,
            claimed_at: new Date()
          }
        });

      return res;
    } catch (error) {
      console.error('领取邮件奖励失败:', error);
      return null;
    }
  }

  /**
   * 删除邮件
   */
  async deleteMail(mailId) {
    try {
      if (!this.isInitialized()) {
        console.warn('数据库未初始化，跳过删除邮件');
        return null;
      }

      const openid = this.getCurrentOpenId();
      if (!openid) {
        console.warn('未获取到用户openid，跳过删除邮件');
        return null;
      }

      const res = await this.db.collection(this.TABLES.MAIL_RECIPIENTS)
        .where({
          _openid: openid,
          mail_id: mailId
        })
        .update({
          data: {
            deleted: true,
            deleted_at: new Date()
          }
        });

      return res;
    } catch (error) {
      console.error('删除邮件失败:', error);
      return null;
    }
  }
}

export default DatabaseManager;
