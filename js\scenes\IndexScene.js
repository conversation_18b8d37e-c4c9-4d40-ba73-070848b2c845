/**
 * 首页场景类
 * 玩家进入游戏后看到的第一个页面
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import Dialog from '../ui/Dialog';

class IndexScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager,resources) {
    super(ctx, screenWidth, screenHeight, sceneManager);
    
    // 场景资源
    this.resources = resources || {};
    
    // 服务器列表
    this.servers = [
      { id: 1, name: '太虚境' },
      { id: 2, name: '蜀山界' },
      { id: 3, name: '蓬莱岛' }
    ];
    
    // 当前选中的服务器
    this.selectedServer = this.servers[0];
    
    // 服务器选择框
    this.serverDialog = null;
    
    // 初始化UI
    this.initUI();
  }
  
  // 初始化UI
  initUI() {
    
    // 创建开始按钮
    const buttonWidth = 200;
    const buttonHeight = 60;
    const buttonX = (this.screenWidth - buttonWidth) / 2;
    const buttonY = this.screenHeight * 0.7;
    
    this.startButton = new Button(
      this.ctx,
      buttonX,
      buttonY,
      buttonWidth,
      buttonHeight,
      '开始游戏',
      null, // 暂时没有按钮资源，使用默认绘制
      null,
      () => {
        console.log('按钮点击确认'); // 调试点1
    try {
      this.sceneManager.showScene('main', { from: 'index' });
      console.log('场景切换调用完成'); // 调试点2
    } catch (e) {
      console.error('切换场景失败:', e);
    }
      }
    );
    
    this.addUIElement(this.startButton);
    
    // 创建服务器选择按钮
    const serverButtonWidth = 200;
    const serverButtonHeight = 50;
    const serverButtonX = (this.screenWidth - serverButtonWidth) / 2;
    const serverButtonY = buttonY - serverButtonHeight - 20;
    
    this.serverButton = new Button(
      this.ctx,
      serverButtonX,
      serverButtonY,
      serverButtonWidth,
      serverButtonHeight,
      `选择服务器: ${this.selectedServer.name}`,
      null,
      null,
      () => {
        this.showServerDialog();
      }
    );
    
    this.addUIElement(this.serverButton);
  }
  
  // 显示服务器选择对话框
  showServerDialog() {
    // 创建服务器选择对话框
    const dialogButtons = this.servers.map(server => {
      return {
        text: server.name,
        normalImg: null,
        pressedImg: null,
        onClick: () => {
          this.selectedServer = server;
          this.serverButton.setText(`选择服务器: ${server.name}`);
          this.serverDialog.hide();
        },
        closeDialog: false
      };
    });
    
    // 添加关闭按钮
    dialogButtons.push({
      text: '关闭',
      normalImg: null,
      pressedImg: null,
      onClick: null,
      closeDialog: true
    });
    
    this.serverDialog = new Dialog(
      this.ctx,
      this.screenWidth,
      this.screenHeight,
      '选择服务器',
      '请选择一个服务器进入游戏',
      dialogButtons
    );
    
    this.addUIElement(this.serverDialog);
    this.serverDialog.show();
  }
  
  // 场景显示时的回调
  onShow(params) {
    // 清空UI元素
    this.clearUIElements();
    
    // 初始化UI
    this.initUI();
    
    // 设置场景为可见状态
    this.visible = true;
    
    console.log('IndexScene shown');
  }
  
  // 场景隐藏时的回调
  onHide() {
    // 清空UI元素
    this.clearUIElements();
    
    // 设置场景为不可见
    this.visible = false;
    
    console.log('IndexScene hidden');
  }
  
  // 子类实现的更新逻辑
  updateScene() {
    // 暂无更新逻辑
  }
  
  // 子类实现的绘制逻辑
  drawScene() {
    // 安全检查屏幕尺寸
    if (!this.screenWidth || !this.screenHeight || 
        !isFinite(this.screenWidth) || !isFinite(this.screenHeight) ||
        this.screenWidth <= 0 || this.screenHeight <= 0) {
      console.error('IndexScene drawScene: 无效的屏幕尺寸', {
        screenWidth: this.screenWidth,
        screenHeight: this.screenHeight
      });
      // 使用默认值
      const defaultWidth = 375;
      const defaultHeight = 667;
      this.ctx.fillStyle = '#1a2a6c';
      this.ctx.fillRect(0, 0, defaultWidth, defaultHeight);
      return;
    }
    
    // 绘制背景（渐变色背景）
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, '#1a2a6c');
    gradient.addColorStop(0.5, '#b21f1f');
    gradient.addColorStop(1, '#fdbb2d');
    
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
    
    // 绘制游戏标题
    this.ctx.font = 'bold 48px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText('修仙六道', this.screenWidth / 2, this.screenHeight * 0.3);
    
    // 绘制游戏副标题
    this.ctx.font = '24px Arial';
    this.ctx.fillText('踏上修仙之路，成就不朽仙道', this.screenWidth / 2, this.screenHeight * 0.3 + 60);
    
    // 绘制服务器选择提示
    this.ctx.font = '18px Arial';
    this.ctx.fillText('请选择服务器并点击开始游戏', this.screenWidth / 2, this.screenHeight * 0.6);
  }
}

export default IndexScene; 