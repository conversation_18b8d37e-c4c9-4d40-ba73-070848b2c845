/**
 * 古宝系统管理器
 * 负责古宝的获取、升级、升星、装备等功能
 */

import treasureConfig from '../../game_data/treasure_data.js';
import AppContext from '../utils/AppContext.js';

class TreasureManager {
  constructor() {
    // 静态配置数据
    this.treasureData = treasureConfig.treasures;
    this.setEffects = treasureConfig.setEffects;
    this.rarityConfig = treasureConfig.rarityConfig;
    this.typeConfig = treasureConfig.typeConfig;
    this.categoryConfig = treasureConfig.categoryConfig;
    
    // 玩家古宝数据缓存
    this.playerTreasures = [];
    
    console.log('古宝管理器初始化完成');
  }

  /**
   * 根据ID获取古宝静态数据
   * @param {string} treasureId 古宝ID
   * @returns {Object|null} 古宝配置数据
   */
  getTreasureConfig(treasureId) {
    return this.treasureData.find(treasure => treasure.id === treasureId) || null;
  }

  /**
   * 获取玩家拥有的所有古宝
   * @returns {Array} 玩家古宝列表
   */
  getPlayerTreasures() {
    return this.playerTreasures;
  }

  /**
   * 计算所有古宝的属性加成总和
   * @returns {Object} 总属性加成
   */
  calculateTotalTreasureBonus() {
    const totalBonus = {};
    
    this.playerTreasures.forEach(treasure => {
      const attributes = this.calculateTreasureAttributes(treasure);
      
      // 累加属性
      for (const [attrName, value] of Object.entries(attributes)) {
        if (totalBonus[attrName] === undefined) {
          totalBonus[attrName] = 0;
        }
        totalBonus[attrName] += value;
      }
    });
    
    // 计算套装效果
    const setEffects = this.checkSetEffects();
    for (const [attrName, value] of Object.entries(setEffects)) {
      if (totalBonus[attrName] === undefined) {
        totalBonus[attrName] = 0;
      }
      totalBonus[attrName] += value;
    }
    
    return totalBonus;
  }

  /**
   * 根据分类筛选古宝
   * @param {string} category 分类 (weapon/artifact/talisman/all)
   * @returns {Array} 筛选后的古宝列表
   */
  getTreasuresByCategory(category = 'all') {
    if (category === 'all') {
      return this.playerTreasures;
    }
    return this.playerTreasures.filter(treasure => {
      const config = this.getTreasureConfig(treasure.treasure_id);
      return config && config.category === category;
    });
  }

  /**
   * 计算古宝当前属性
   * @param {Object} playerTreasure 玩家古宝数据
   * @returns {Object} 计算后的属性
   */
  calculateTreasureAttributes(playerTreasure) {
    const config = this.getTreasureConfig(playerTreasure.treasure_id);
    if (!config) return {};

    const attributes = { ...config.base_attributes };
    
    // 加上等级成长属性
    for (const [attrName, growthValue] of Object.entries(config.upgrade_growth)) {
      if (attributes[attrName] !== undefined) {
        attributes[attrName] += growthValue * (playerTreasure.level - 1);
      } else {
        attributes[attrName] = growthValue * (playerTreasure.level - 1);
      }
    }

    // 加上升星奖励属性
    for (let star = 1; star <= playerTreasure.star; star++) {
      const starConfig = config.star_upgrade.find(s => s.star === star);
      if (starConfig && starConfig.attribute_bonus) {
        for (const [attrName, bonusValue] of Object.entries(starConfig.attribute_bonus)) {
          if (attributes[attrName] !== undefined) {
            attributes[attrName] += bonusValue;
          } else {
            attributes[attrName] = bonusValue;
          }
        }
      }
    }

    return attributes;
  }

  /**
   * 计算古宝升级所需经验
   * @param {number} currentLevel 当前等级
   * @returns {number} 升级所需经验
   */
  calculateUpgradeExp(currentLevel) {
    return Math.floor(100 * Math.pow(currentLevel, 1.5));
  }

  /**
   * 获取古宝升星配置
   * @param {string} treasureId 古宝ID
   * @param {number} targetStar 目标星级
   * @returns {Object|null} 升星配置
   */
  getStarUpgradeConfig(treasureId, targetStar) {
    const config = this.getTreasureConfig(treasureId);
    if (!config) return null;
    
    return config.star_upgrade.find(s => s.star === targetStar) || null;
  }

  /**
   * 检查是否可以升星
   * @param {Object} playerTreasure 玩家古宝数据
   * @returns {Object} 检查结果 {canUpgrade: boolean, reason: string, cost: Object}
   */
  canUpgradeStar(playerTreasure) {
    if (playerTreasure.star >= 5) {
      return { canUpgrade: false, reason: '已达到最高星级', cost: null };
    }

    const nextStar = playerTreasure.star + 1;
    const starConfig = this.getStarUpgradeConfig(playerTreasure.treasure_id, nextStar);
    
    if (!starConfig) {
      return { canUpgrade: false, reason: '升星配置不存在', cost: null };
    }

    // 检查资源是否充足
    const gameState = AppContext.game.gameStateManager;
    const playerResources = gameState.state.player.resources;
    
    for (const [resourceType, requiredAmount] of Object.entries(starConfig.cost)) {
      const currentAmount = playerResources[resourceType] || 0;
      if (currentAmount < requiredAmount) {
        return { 
          canUpgrade: false, 
          reason: `${resourceType}不足，需要${requiredAmount}，当前${currentAmount}`, 
          cost: starConfig.cost 
        };
      }
    }

    return { canUpgrade: true, reason: '可以升星', cost: starConfig.cost };
  }

  /**
   * 检查是否可以升级
   * @param {Object} playerTreasure 玩家古宝数据
   * @param {Object} materials 消耗材料 {item_id: count}
   * @returns {Object} 检查结果
   */
  canUpgradeLevel(playerTreasure, materials) {
    if (playerTreasure.level >= 100) {
      return { canUpgrade: false, reason: '已达到最高等级' };
    }

    const requiredExp = this.calculateUpgradeExp(playerTreasure.level);
    
    // 计算材料提供的经验值
    let totalExp = playerTreasure.exp;
    const gameState = AppContext.game.gameStateManager;
    
    for (const [itemId, count] of Object.entries(materials)) {
      const item = gameState.getItem(itemId);
      if (!item) {
        return { canUpgrade: false, reason: `物品${itemId}不存在` };
      }
      
      if (item.count < count) {
        return { canUpgrade: false, reason: `${item.name}数量不足` };
      }
      
      // 假设每个强化材料提供50点经验
      totalExp += count * 50;
    }

    return { 
      canUpgrade: totalExp >= requiredExp, 
      reason: totalExp >= requiredExp ? '可以升级' : '经验不足',
      currentExp: totalExp,
      requiredExp: requiredExp
    };
  }

  /**
   * 升级古宝
   * @param {string} treasureRecordId 古宝记录ID
   * @param {Object} materials 消耗材料
   * @returns {Object} 操作结果
   */
  async upgradeTreasure(treasureRecordId, materials) {
    try {
      const treasure = this.playerTreasures.find(t => t._id === treasureRecordId);
      if (!treasure) {
        return { success: false, message: '古宝不存在' };
      }

      const canUpgrade = this.canUpgradeLevel(treasure, materials);
      if (!canUpgrade.canUpgrade) {
        return { success: false, message: canUpgrade.reason };
      }

      // 扣除材料，增加经验
      let addExp = 0;
      for (const [itemId, count] of Object.entries(materials)) {
        // 扣除材料
        const gameState = AppContext.game.gameStateManager;
        gameState.useItem(itemId, count);
        // 增加经验（假设每个材料50经验）
        addExp += count * 50;
      }

      treasure.exp += addExp;
      
      // 检查是否可以升级
      const requiredExp = this.calculateUpgradeExp(treasure.level);
      if (treasure.exp >= requiredExp) {
        treasure.level++;
        treasure.exp -= requiredExp;
      }

      // 重新计算角色属性
      this.recalculatePlayerAttributes();

      return { success: true, message: '古宝升级成功', newLevel: treasure.level };
    } catch (error) {
      console.error('升级古宝失败:', error);
      return { success: false, message: '升级失败' };
    }
  }

  /**
   * 重新计算玩家属性（所有古宝自动加成）
   */
  recalculatePlayerAttributes() {
    const gameState = AppContext.game.gameStateManager;
    const characters = gameState.getCharacters();
    
    // 为每个角色重新计算属性
    characters.forEach(character => {
      this.applyTreasureAttributesToCharacter(character);
    });

    // 检查套装效果
    this.checkSetEffects();
  }

  /**
   * 将古宝属性应用到角色（自动加成所有古宝）
   * @param {Object} character 角色对象
   */
  applyTreasureAttributesToCharacter(character) {
    // 重置古宝属性加成
    character.treasureAttributes = {};
    
    // 累加所有古宝的属性（自动加成）
    this.playerTreasures.forEach(treasure => {
      const attributes = this.calculateTreasureAttributes(treasure);
      
      for (const [attrName, value] of Object.entries(attributes)) {
        if (character.treasureAttributes[attrName]) {
          character.treasureAttributes[attrName] += value;
        } else {
          character.treasureAttributes[attrName] = value;
        }
      }
    });

    // 重新计算角色总战力
    character.calculatePower();
  }

  /**
   * 检查并应用套装效果（基于拥有的古宝）
   */
  checkSetEffects() {
    const ownedIds = this.playerTreasures.map(t => t.treasure_id);
    const activeSetEffects = [];

    for (const [setId, setConfig] of Object.entries(this.setEffects)) {
      const ownedCount = setConfig.treasures.filter(id => ownedIds.includes(id)).length;
      
      if (ownedCount >= 2) {
        const effect = setConfig.effects.find(e => e.count <= ownedCount);
        if (effect) {
          activeSetEffects.push({
            setId,
            setName: setConfig.name,
            effect: effect
          });
        }
      }
    }

    // 应用套装效果到角色
    this.applySetEffects(activeSetEffects);
    
    return activeSetEffects;
  }

  /**
   * 应用套装效果
   * @param {Array} setEffects 激活的套装效果
   */
  applySetEffects(setEffects) {
    const gameState = AppContext.game.gameStateManager;
    const characters = gameState.getCharacters();
    
    characters.forEach(character => {
      character.setEffects = setEffects;
      
      // 根据套装效果调整属性
      setEffects.forEach(setEffect => {
        const attributes = setEffect.effect.attributes;
        
        for (const [attrName, value] of Object.entries(attributes)) {
          if (attrName.endsWith('_percent')) {
            // 百分比加成
            const baseAttr = attrName.replace('_percent', '');
            if (character.attributes[baseAttr]) {
              character.attributes[baseAttr] *= (1 + value);
            }
          } else if (attrName === 'all_attributes_percent') {
            // 全属性百分比加成
            for (const attr of ['hp', 'attack', 'defense', 'speed']) {
              if (character.attributes[attr]) {
                character.attributes[attr] *= (1 + value);
              }
            }
          } else {
            // 固定数值加成
            if (character.treasureAttributes[attrName]) {
              character.treasureAttributes[attrName] += value;
            } else {
              character.treasureAttributes[attrName] = value;
            }
          }
        }
      });
      
      character.calculatePower();
    });
  }

  /**
   * 加载玩家古宝数据
   * @param {Array} treasureData 从数据库获取的古宝数据
   */
  loadPlayerTreasures(treasureData) {
    this.playerTreasures = treasureData || [];
    
    // 重新计算属性（所有古宝自动加成）
    this.recalculatePlayerAttributes();
    
    console.log(`加载玩家古宝数据完成，共${this.playerTreasures.length}件，全部自动生效`);
  }

  /**
   * 获取古宝详细信息（用于UI显示）
   * @param {Object} playerTreasure 玩家古宝数据
   * @returns {Object} 详细信息
   */
  getTreasureDetail(playerTreasure) {
    const config = this.getTreasureConfig(playerTreasure.treasure_id);
    if (!config) return null;

    const currentAttributes = this.calculateTreasureAttributes(playerTreasure);
    const rarityInfo = this.rarityConfig[config.rarity];
    const typeInfo = this.typeConfig[config.type];
    const categoryInfo = this.categoryConfig[config.category];

    return {
      id: playerTreasure._id,
      treasureId: playerTreasure.treasure_id,
      name: config.name,
      description: config.description,
      level: playerTreasure.level,
      star: playerTreasure.star,
      exp: playerTreasure.exp,
      isEquipped: playerTreasure.is_equipped,
      rarity: rarityInfo,
      type: typeInfo,
      category: categoryInfo,
      attributes: currentAttributes,
      icon: config.icon,
      canUpgradeStar: this.canUpgradeStar(playerTreasure),
      nextLevelExp: this.calculateUpgradeExp(playerTreasure.level)
    };
  }
}

export default TreasureManager; 