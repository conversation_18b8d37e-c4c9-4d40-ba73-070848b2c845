<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %> - 修仙六道后台管理</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
  <style>
    body { 
      font-family: 'Microsoft YaHei', sans-serif; 
      background-color: #f8f9fa; 
    }
    .sidebar {
      min-height: 100vh;
      background: linear-gradient(180deg, #343a40 0%, #495057 100%);
      position: fixed;
      top: 0;
      left: 0;
      width: 250px;
      z-index: 1000;
      transition: all 0.3s;
    }
    .main-content {
      margin-left: 250px;
      padding: 20px;
      transition: all 0.3s;
    }
    .navbar-brand {
      color: #fff !important;
      font-weight: bold;
      font-size: 1.2rem;
    }
    .nav-link {
      color: #adb5bd !important;
      padding: 12px 20px;
      border-radius: 8px;
      margin: 2px 10px;
      transition: all 0.3s;
    }
    .nav-link:hover, .nav-link.active {
      color: #fff !important;
      background-color: rgba(255,255,255,0.1);
    }
    .card {
      border: none;
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(0,0,0,0.1);
      margin-bottom: 20px;
    }
    .card-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 12px 12px 0 0 !important;
      padding: 15px 20px;
    }
    .btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 8px;
    }
    .btn-primary:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      transform: translateY(-1px);
    }
    .table th {
      background-color: #f8f9fa;
      border-top: none;
      font-weight: 600;
      color: #495057;
    }
    .player-card {
      background: white;
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      transition: all 0.3s;
    }
    .player-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    .badge-equipment {
      background: linear-gradient(135deg, #ffd89b 0%, #19547b 100%);
      color: white;
    }
    .badge-skill {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    .equipment-item, .skill-item {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 8px;
      border-left: 4px solid #007bff;
    }
    .equipment-item.legendary {
      border-left-color: #ffd700;
      background: linear-gradient(135deg, #fff9e6 0%, #fff2cc 100%);
    }
    .equipment-item.epic {
      border-left-color: #9932cc;
      background: linear-gradient(135deg, #f9f0ff 0%, #f0e6ff 100%);
    }
    .skill-level {
      background: #007bff;
      color: white;
      border-radius: 50%;
      width: 30px;
      height: 30px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: bold;
    }
    .star-rating {
      color: #ffd700;
    }
    .loading-spinner {
      text-align: center;
      padding: 40px;
    }
    .no-data {
      text-align: center;
      padding: 40px;
      color: #6c757d;
    }
    @media (max-width: 768px) {
      .sidebar {
        transform: translateX(-100%);
      }
      .main-content {
        margin-left: 0;
      }
      .sidebar.show {
        transform: translateX(0);
      }
    }
  </style>
</head>
<body>
  <!-- 侧边栏 -->
  <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
    <div class="position-sticky pt-3">
      <h5 class="text-white text-center mb-4">修仙后台</h5>
      <ul class="nav flex-column">
        <li class="nav-item"><a class="nav-link <%= title === '数据总览' ? 'active' : '' %>" href="/"><i class="bi bi-speedometer2 me-2"></i>数据总览</a></li>
        <li class="nav-item"><a class="nav-link <%= title === '数据统计' ? 'active' : '' %>" href="/dashboard"><i class="bi bi-graph-up me-2"></i>数据统计</a></li>
        <li class="nav-item"><a class="nav-link <%= title === '玩家管理' ? 'active' : '' %>" href="/players"><i class="bi bi-people me-2"></i>玩家管理</a></li>
        <li class="nav-item"><a class="nav-link <%= title === '玩家资源' ? 'active' : '' %>" href="/player-resources"><i class="bi bi-gem me-2"></i>玩家资源</a></li>
        <li class="nav-item"><a class="nav-link <%= title === '装备技能' ? 'active' : '' %>" href="/player-equipment"><i class="bi bi-shield-check me-2"></i>装备技能</a></li>
        <li class="nav-item"><a class="nav-link <%= title.includes('邮件') ? 'active' : '' %>" href="/mails"><i class="bi bi-envelope-fill me-2"></i>邮件管理</a></li>
      </ul>
    </div>
  </nav>

  <!-- 主内容区 -->
  <main class="main-content">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <h1 class="h2 mb-1"><%= title %></h1>
        <p class="text-muted mb-0">管理玩家的装备和技能数据</p>
      </div>
      <div class="d-flex gap-2">
        <button class="btn btn-outline-secondary" onclick="refreshData()">
          <i class="bi bi-arrow-clockwise me-1"></i>刷新
        </button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-funnel me-2"></i>搜索和筛选</h5>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <div class="col-md-4">
            <label class="form-label">搜索玩家</label>
            <input type="text" class="form-control" id="searchInput" placeholder="输入玩家昵称或OpenID">
          </div>
          <div class="col-md-3">
            <label class="form-label">数据类型</label>
            <select class="form-select" id="typeFilter">
              <option value="all">全部数据</option>
              <option value="treasures">仅装备</option>
              <option value="skills">仅技能</option>
            </select>
          </div>
          <div class="col-md-3">
            <label class="form-label">每页显示</label>
            <select class="form-select" id="limitSelect">
              <option value="10">10条</option>
              <option value="20" selected>20条</option>
              <option value="50">50条</option>
            </select>
          </div>
          <div class="col-md-2">
            <label class="form-label">&nbsp;</label>
            <button class="btn btn-primary w-100" onclick="searchPlayers()">
              <i class="bi bi-search me-1"></i>搜索
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 玩家列表 -->
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-people me-2"></i>玩家装备技能列表</h5>
      </div>
      <div class="card-body p-0">
        <div id="loadingSpinner" class="loading-spinner">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
          </div>
          <p class="mt-2 text-muted">正在加载玩家数据...</p>
        </div>
        <div id="playersContainer" style="display: none;"></div>
        <div id="noDataMessage" class="no-data" style="display: none;">
          <i class="bi bi-inbox display-4 text-muted"></i>
          <p class="mt-3">暂无玩家装备技能数据</p>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div id="paginationContainer" class="d-flex justify-content-center mt-4" style="display: none !important;">
      <nav aria-label="分页导航">
        <ul id="pagination" class="pagination">
        </ul>
      </nav>
    </div>
  </main>

  <!-- 装备详情模态框 -->
  <div class="modal fade" id="treasureModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">装备详情管理</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <div id="treasureContent"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- 技能详情模态框 -->
  <div class="modal fade" id="skillModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">技能详情管理</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <div id="skillContent"></div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    let currentPage = 1;
    let totalPages = 1;

    // 页面加载时获取数据
    document.addEventListener('DOMContentLoaded', function() {
      loadPlayers();
    });

    // 加载玩家装备技能数据
    async function loadPlayers(page = 1) {
      try {
        showLoading();
        
        const search = document.getElementById('searchInput').value.trim();
        const type = document.getElementById('typeFilter').value;
        const limit = document.getElementById('limitSelect').value;
        
        const params = new URLSearchParams({
          page: page,
          limit: limit,
          search: search,
          type: type
        });

        const response = await fetch(`/api/players/equipment?${params}`);
        const result = await response.json();

        if (result.success) {
          displayPlayers(result.data.players);
          updatePagination(result.data.pagination);
          currentPage = page;
          totalPages = result.data.pagination.pages;
        } else {
          throw new Error(result.error || '获取数据失败');
        }
      } catch (error) {
        console.error('加载玩家数据失败:', error);
        showError('加载玩家装备技能数据失败: ' + error.message);
      } finally {
        hideLoading();
      }
    }

    // 显示玩家列表
    function displayPlayers(players) {
      const container = document.getElementById('playersContainer');
      const noDataMessage = document.getElementById('noDataMessage');

      if (!players || players.length === 0) {
        container.style.display = 'none';
        noDataMessage.style.display = 'block';
        return;
      }

      noDataMessage.style.display = 'none';
      container.style.display = 'block';

      container.innerHTML = players.map(player => `
        <div class="player-card" data-player-id="${player._id}">
          <div class="row align-items-center">
            <div class="col-md-3">
              <h6 class="mb-1 text-primary">${escapeHtml(player.nickname || '未知玩家')}</h6>
              <small class="text-muted">ID: ${escapeHtml(player._id)}</small><br>
              <small class="text-muted">等级: ${player.level || 0} | 境界: ${escapeHtml(player.realm || '凡人')}</small>
            </div>
            <div class="col-md-2">
              <span class="badge badge-equipment">装备 ${player.treasures.length}</span><br>
              <span class="badge badge-skill mt-1">技能 ${player.skills.length}</span>
            </div>
            <div class="col-md-4">
              <div class="row">
                <div class="col-6">
                  <strong>装备预览</strong>
                  <div class="small text-muted">
                    ${player.treasures.slice(0, 2).map(t => 
                      `${escapeHtml(t.name || '未知装备')} (${t.star || 0}★)`
                    ).join('<br>') || '暂无装备'}
                    ${player.treasures.length > 2 ? '<br>...' : ''}
                  </div>
                </div>
                <div class="col-6">
                  <strong>技能预览</strong>
                  <div class="small text-muted">
                    ${player.skills.slice(0, 2).map(s => 
                      `${escapeHtml(s.name || '未知技能')} Lv.${s.level || 0}`
                    ).join('<br>') || '暂无技能'}
                    ${player.skills.length > 2 ? '<br>...' : ''}
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 text-end">
              <button class="btn btn-sm btn-outline-primary me-1" onclick="viewTreasures('${player._id}', '${escapeHtml(player.nickname)}')">
                <i class="bi bi-shield-check me-1"></i>查看装备
              </button>
              <button class="btn btn-sm btn-outline-success" onclick="viewSkills('${player._id}', '${escapeHtml(player.nickname)}')">
                <i class="bi bi-magic me-1"></i>查看技能
              </button>
            </div>
          </div>
        </div>
      `).join('');
    }

    // 查看玩家装备
    async function viewTreasures(playerId, playerName) {
      try {
        const response = await fetch(`/api/players/${playerId}/treasures`);
        const result = await response.json();

        if (result.success) {
          const modal = new bootstrap.Modal(document.getElementById('treasureModal'));
          document.querySelector('#treasureModal .modal-title').textContent = `${playerName} - 装备管理`;
          
          const content = document.getElementById('treasureContent');
          if (result.data.treasures.length === 0) {
            content.innerHTML = '<div class="text-center text-muted py-4">该玩家暂无装备</div>';
          } else {
            content.innerHTML = `
              <div class="row">
                ${result.data.treasures.map(treasure => `
                  <div class="col-md-6 mb-3">
                    <div class="equipment-item ${getEquipmentRarity(treasure.rarity)}">
                      <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="mb-0">${escapeHtml(treasure.name || '未知装备')}</h6>
                        <div class="star-rating">
                          ${'★'.repeat(treasure.star || 0)}${'☆'.repeat(Math.max(0, 5 - (treasure.star || 0)))}
                        </div>
                      </div>
                      <div class="row small">
                        <div class="col-6">
                          <div>等级: <input type="number" class="form-control form-control-sm d-inline-block w-50" value="${treasure.level || 0}" min="0" max="999" onchange="updateTreasure('${playerId}', '${treasure._id}', 'level', this.value)"></div>
                          <div class="mt-1">强化: <input type="number" class="form-control form-control-sm d-inline-block w-50" value="${treasure.enhance_level || 0}" min="0" max="20" onchange="updateTreasure('${playerId}', '${treasure._id}', 'enhance_level', this.value)"></div>
                        </div>
                        <div class="col-6">
                          <div>星级: <input type="number" class="form-control form-control-sm d-inline-block w-50" value="${treasure.star || 0}" min="0" max="5" onchange="updateTreasure('${playerId}', '${treasure._id}', 'star', this.value)"></div>
                          <div class="mt-1">
                            <div class="form-check form-check-inline">
                              <input class="form-check-input" type="checkbox" ${treasure.equipped ? 'checked' : ''} onchange="updateTreasure('${playerId}', '${treasure._id}', 'equipped', this.checked)">
                              <label class="form-check-label small">已装备</label>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                `).join('')}
              </div>
            `;
          }
          
          modal.show();
        } else {
          showError('获取装备数据失败: ' + result.error);
        }
      } catch (error) {
        console.error('查看装备失败:', error);
        showError('查看装备失败: ' + error.message);
      }
    }

    // 查看玩家技能
    async function viewSkills(playerId, playerName) {
      try {
        const response = await fetch(`/api/players/${playerId}/skills`);
        const result = await response.json();

        if (result.success) {
          const modal = new bootstrap.Modal(document.getElementById('skillModal'));
          document.querySelector('#skillModal .modal-title').textContent = `${playerName} - 技能管理`;
          
          const content = document.getElementById('skillContent');
          if (result.data.skills.length === 0) {
            content.innerHTML = '<div class="text-center text-muted py-4">该玩家暂无技能</div>';
          } else {
            content.innerHTML = `
              <div class="row">
                ${result.data.skills.map(skill => `
                  <div class="col-md-6 mb-3">
                    <div class="skill-item">
                      <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="mb-0">${escapeHtml(skill.name || '未知技能')}</h6>
                        <span class="skill-level">${skill.level || 0}</span>
                      </div>
                      <div class="row small">
                        <div class="col-6">
                          <div>等级: <input type="number" class="form-control form-control-sm d-inline-block w-50" value="${skill.level || 0}" min="0" max="999" onchange="updateSkill('${playerId}', '${skill._id}', 'level', this.value)"></div>
                          <div class="mt-1">经验: <input type="number" class="form-control form-control-sm d-inline-block w-50" value="${skill.experience || 0}" min="0" onchange="updateSkill('${playerId}', '${skill._id}', 'experience', this.value)"></div>
                        </div>
                        <div class="col-6">
                          <div>技能点: <input type="number" class="form-control form-control-sm d-inline-block w-50" value="${skill.skill_points || 0}" min="0" onchange="updateSkill('${playerId}', '${skill._id}', 'skill_points', this.value)"></div>
                          <div class="mt-1">
                            <div class="form-check form-check-inline">
                              <input class="form-check-input" type="checkbox" ${skill.equipped ? 'checked' : ''} onchange="updateSkill('${playerId}', '${skill._id}', 'equipped', this.checked)">
                              <label class="form-check-label small">已装备</label>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                `).join('')}
              </div>
            `;
          }
          
          modal.show();
        } else {
          showError('获取技能数据失败: ' + result.error);
        }
      } catch (error) {
        console.error('查看技能失败:', error);
        showError('查看技能失败: ' + error.message);
      }
    }

    // 更新装备
    async function updateTreasure(playerId, treasureId, field, value) {
      try {
        const updateData = { [field]: value };
        
        const response = await fetch(`/api/players/${playerId}/treasures/${treasureId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(updateData)
        });

        const result = await response.json();
        if (result.success) {
          showSuccess('装备更新成功');
        } else {
          showError('装备更新失败: ' + result.error);
        }
      } catch (error) {
        console.error('更新装备失败:', error);
        showError('更新装备失败: ' + error.message);
      }
    }

    // 更新技能
    async function updateSkill(playerId, skillId, field, value) {
      try {
        const updateData = { [field]: value };
        
        const response = await fetch(`/api/players/${playerId}/skills/${skillId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(updateData)
        });

        const result = await response.json();
        if (result.success) {
          showSuccess('技能更新成功');
        } else {
          showError('技能更新失败: ' + result.error);
        }
      } catch (error) {
        console.error('更新技能失败:', error);
        showError('更新技能失败: ' + error.message);
      }
    }

    // 获取装备稀有度样式
    function getEquipmentRarity(rarity) {
      if (rarity >= 4) return 'legendary';
      if (rarity >= 3) return 'epic';
      return '';
    }

    // 搜索玩家
    function searchPlayers() {
      currentPage = 1;
      loadPlayers(1);
    }

    // 刷新数据
    function refreshData() {
      loadPlayers(currentPage);
    }

    // 更新分页
    function updatePagination(pagination) {
      const paginationContainer = document.getElementById('paginationContainer');
      const paginationElement = document.getElementById('pagination');

      if (pagination.pages <= 1) {
        paginationContainer.style.display = 'none';
        return;
      }

      paginationContainer.style.display = 'block';
      
      let paginationHTML = '';
      
      // 上一页
      if (pagination.page > 1) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadPlayers(${pagination.page - 1})">上一页</a></li>`;
      }
      
      // 页码
      for (let i = Math.max(1, pagination.page - 2); i <= Math.min(pagination.pages, pagination.page + 2); i++) {
        paginationHTML += `<li class="page-item ${i === pagination.page ? 'active' : ''}">
          <a class="page-link" href="#" onclick="loadPlayers(${i})">${i}</a>
        </li>`;
      }
      
      // 下一页
      if (pagination.page < pagination.pages) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadPlayers(${pagination.page + 1})">下一页</a></li>`;
      }
      
      paginationElement.innerHTML = paginationHTML;
    }

    // 显示加载状态
    function showLoading() {
      document.getElementById('loadingSpinner').style.display = 'block';
      document.getElementById('playersContainer').style.display = 'none';
      document.getElementById('noDataMessage').style.display = 'none';
    }

    // 隐藏加载状态
    function hideLoading() {
      document.getElementById('loadingSpinner').style.display = 'none';
    }

    // 显示成功消息
    function showSuccess(message) {
      // 这里可以使用Toast或其他通知组件
      alert('✅ ' + message);
    }

    // 显示错误消息
    function showError(message) {
      alert('❌ ' + message);
    }

    // HTML转义
    function escapeHtml(text) {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    }

    // 搜索框回车事件
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        searchPlayers();
      }
    });
  </script>
</body>
</html>
