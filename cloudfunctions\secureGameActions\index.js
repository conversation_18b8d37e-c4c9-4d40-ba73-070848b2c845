/**
 * 安全游戏操作云函数
 * 所有玩家数据变更操作都在云端执行，确保数据安全性
 */

const cloud = require('wx-server-sdk');

// 初始化云环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 游戏配置和限制
 */
const GAME_RULES = {
  // 资源限制
  MAX_RESOURCES: {
    xianyu: 999999999,
    lingshi: 999999999,
    sword_intent: 999999,
    lianlidian: 999999
  },
  
  // 等级限制
  MAX_LEVEL: 999,
  
  // 境界系统
  CULTIVATION_REALMS: [
    '炼气期一层', '炼气期二层', '炼气期三层', '炼气期四层', '炼气期五层',
    '炼气期六层', '炼气期七层', '炼气期八层', '炼气期九层',
    '筑基期一层', '筑基期二层', '筑基期三层', '筑基期四层', '筑基期五层',
    '筑基期六层', '筑基期七层', '筑基期八层', '筑基期九层',
    '金丹期一层', '金丹期二层', '金丹期三层'
  ],
  
  // 升级经验需求
  EXP_REQUIREMENTS: {
    1: 100, 2: 220, 3: 360, 4: 520, 5: 700,
    // ... 更多等级的经验需求
  },
  
  // 资源消耗规则
  RESOURCE_COSTS: {
    level_up: { lingshi: 100 },
    realm_breakthrough: { xiuwei_point: 50 },
    skill_upgrade: { lianlidian: 10 }
  }
};

/**
 * 数据校验器
 */
class DataValidator {
  /**
   * 校验资源数量是否合法
   */
  static validateResources(resources) {
    for (const [key, value] of Object.entries(resources)) {
      if (typeof value !== 'number' || value < 0) {
        throw new Error(`Invalid resource value: ${key} = ${value}`);
      }
      
      if (GAME_RULES.MAX_RESOURCES[key] && value > GAME_RULES.MAX_RESOURCES[key]) {
        throw new Error(`Resource ${key} exceeds maximum limit: ${value}`);
      }
    }
    return true;
  }
  
  /**
   * 校验等级是否合法
   */
  static validateLevel(level) {
    if (typeof level !== 'number' || level < 1 || level > GAME_RULES.MAX_LEVEL) {
      throw new Error(`Invalid level: ${level}`);
    }
    return true;
  }
  
  /**
   * 校验境界是否合法
   */
  static validateCultivationRealm(realm) {
    if (!GAME_RULES.CULTIVATION_REALMS.includes(realm)) {
      throw new Error(`Invalid cultivation realm: ${realm}`);
    }
    return true;
  }
}

/**
 * 安全操作类
 */
class SecureGameActions {
  constructor(openid) {
    this.openid = openid;
  }
  
  /**
   * 安全地添加资源
   */
  async addResources(resourceChanges, reason = 'unknown') {
    try {
      // 1. 校验输入
      DataValidator.validateResources(resourceChanges);
      
      // 2. 获取当前资源
      const currentResources = await this.getCurrentResources();
      
      // 3. 计算新资源值
      const newResources = { ...currentResources };
      for (const [key, value] of Object.entries(resourceChanges)) {
        newResources[key] = (newResources[key] || 0) + value;
      }
      
      // 4. 校验新资源值
      DataValidator.validateResources(newResources);
      
      // 5. 更新数据库
      await db.collection('player_res').where({
        _openid: this.openid
      }).update({
        data: newResources
      });
      
      // 6. 记录操作日志
      await this.logAction('add_resources', {
        changes: resourceChanges,
        reason: reason,
        result: newResources
      });
      
      return {
        success: true,
        newResources: newResources,
        changes: resourceChanges
      };
      
    } catch (error) {
      console.error('添加资源失败:', error);
      throw error;
    }
  }
  
  /**
   * 安全地消耗资源
   */
  async consumeResources(resourceCosts, reason = 'unknown') {
    try {
      // 1. 校验输入
      DataValidator.validateResources(resourceCosts);
      
      // 2. 获取当前资源
      const currentResources = await this.getCurrentResources();
      
      // 3. 检查资源是否足够
      for (const [key, cost] of Object.entries(resourceCosts)) {
        const current = currentResources[key] || 0;
        if (current < cost) {
          throw new Error(`Insufficient ${key}: need ${cost}, have ${current}`);
        }
      }
      
      // 4. 计算新资源值
      const newResources = { ...currentResources };
      for (const [key, cost] of Object.entries(resourceCosts)) {
        newResources[key] -= cost;
      }
      
      // 5. 更新数据库
      await db.collection('player_res').where({
        _openid: this.openid
      }).update({
        data: newResources
      });
      
      // 6. 记录操作日志
      await this.logAction('consume_resources', {
        costs: resourceCosts,
        reason: reason,
        result: newResources
      });
      
      return {
        success: true,
        newResources: newResources,
        consumed: resourceCosts
      };
      
    } catch (error) {
      console.error('消耗资源失败:', error);
      throw error;
    }
  }
  
  /**
   * 安全地升级
   */
  async levelUp() {
    try {
      // 1. 获取当前玩家数据
      const playerData = await this.getCurrentPlayerData();
      const currentLevel = playerData.level || 1;
      const currentExp = playerData.exp || 0;
      
      // 2. 检查是否满足升级条件
      const expRequired = GAME_RULES.EXP_REQUIREMENTS[currentLevel];
      if (!expRequired) {
        throw new Error(`No exp requirement defined for level ${currentLevel}`);
      }
      
      if (currentExp < expRequired) {
        throw new Error(`Insufficient exp: need ${expRequired}, have ${currentExp}`);
      }
      
      // 3. 计算新等级和剩余经验
      const newLevel = currentLevel + 1;
      const remainingExp = currentExp - expRequired;
      
      // 4. 校验新等级
      DataValidator.validateLevel(newLevel);
      
      // 5. 计算新战力（简单算法：等级 * 100 + 基础战力）
      const newPower = (playerData.power || 0) + 100;
      
      // 6. 更新玩家数据
      await db.collection('players').where({
        _openid: this.openid
      }).update({
        data: {
          level: newLevel,
          exp: remainingExp,
          power: newPower
        }
      });
      
      // 7. 记录操作日志
      await this.logAction('level_up', {
        oldLevel: currentLevel,
        newLevel: newLevel,
        expUsed: expRequired,
        remainingExp: remainingExp,
        newPower: newPower
      });
      
      return {
        success: true,
        oldLevel: currentLevel,
        newLevel: newLevel,
        newPower: newPower,
        remainingExp: remainingExp
      };
      
    } catch (error) {
      console.error('升级失败:', error);
      throw error;
    }
  }
  
  /**
   * 安全地境界突破
   */
  async realmBreakthrough() {
    try {
      // 1. 获取当前玩家数据
      const playerData = await this.getCurrentPlayerData();
      const currentRealm = playerData.cultivation_realm || '炼气期一层';
      
      // 2. 检查当前境界在列表中的位置
      const currentIndex = GAME_RULES.CULTIVATION_REALMS.indexOf(currentRealm);
      if (currentIndex === -1) {
        throw new Error(`Invalid current realm: ${currentRealm}`);
      }
      
      if (currentIndex >= GAME_RULES.CULTIVATION_REALMS.length - 1) {
        throw new Error('Already at maximum realm');
      }
      
      // 3. 检查是否满足突破条件（这里可以添加更复杂的条件）
      const requiredLevel = (currentIndex + 1) * 10; // 简单规则：每个境界需要对应等级
      if ((playerData.level || 1) < requiredLevel) {
        throw new Error(`Level too low for breakthrough: need ${requiredLevel}, have ${playerData.level}`);
      }
      
      // 4. 消耗突破资源
      const breakthroughCost = { xiuwei_point: 50 + currentIndex * 10 };
      await this.consumeResources(breakthroughCost, 'realm_breakthrough');
      
      // 5. 突破到下一境界
      const newRealm = GAME_RULES.CULTIVATION_REALMS[currentIndex + 1];
      const newPower = (playerData.power || 0) + 200;
      
      // 6. 更新玩家数据
      await db.collection('players').where({
        _openid: this.openid
      }).update({
        data: {
          cultivation_realm: newRealm,
          power: newPower
        }
      });
      
      // 7. 记录操作日志
      await this.logAction('realm_breakthrough', {
        oldRealm: currentRealm,
        newRealm: newRealm,
        cost: breakthroughCost,
        newPower: newPower
      });
      
      return {
        success: true,
        oldRealm: currentRealm,
        newRealm: newRealm,
        newPower: newPower,
        cost: breakthroughCost
      };
      
    } catch (error) {
      console.error('境界突破失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取当前资源
   */
  async getCurrentResources() {
    const result = await db.collection('player_res').where({
      _openid: this.openid
    }).limit(1).get();
    
    if (result.data.length === 0) {
      throw new Error('Player resources not found');
    }
    
    return result.data[0];
  }
  
  /**
   * 获取当前玩家数据
   */
  async getCurrentPlayerData() {
    const result = await db.collection('players').where({
      _openid: this.openid
    }).limit(1).get();
    
    if (result.data.length === 0) {
      throw new Error('Player data not found');
    }
    
    return result.data[0];
  }
  
  /**
   * 记录操作日志
   */
  async logAction(action, details) {
    try {
      await db.collection('player_action_logs').add({
        data: {
          _openid: this.openid,
          action: action,
          details: details,
          timestamp: new Date(),
          server_time: db.serverDate()
        }
      });
    } catch (error) {
      console.error('记录日志失败:', error);
      // 日志失败不应该影响主要操作
    }
  }
}

/**
 * 云函数主入口
 */
exports.main = async (event, context) => {
  try {
    // 获取用户openid
    const wxContext = cloud.getWXContext();
    const openid = wxContext.OPENID;
    
    if (!openid) {
      throw new Error('User not authenticated');
    }
    
    const { action, data } = event;
    const secureActions = new SecureGameActions(openid);
    
    console.log(`执行安全操作: ${action}`, data);
    
    let result;
    
    switch (action) {
      case 'addResources':
        result = await secureActions.addResources(data.resources, data.reason);
        break;
        
      case 'consumeResources':
        result = await secureActions.consumeResources(data.resources, data.reason);
        break;
        
      case 'levelUp':
        result = await secureActions.levelUp();
        break;
        
      case 'realmBreakthrough':
        result = await secureActions.realmBreakthrough();
        break;
        
      case 'getPlayerData':
        const playerData = await secureActions.getCurrentPlayerData();
        const resources = await secureActions.getCurrentResources();
        result = {
          success: true,
          playerData: playerData,
          resources: resources
        };
        break;
        
      default:
        throw new Error(`Unknown action: ${action}`);
    }
    
    console.log(`操作 ${action} 执行成功:`, result);
    
    return {
      success: true,
      data: result,
      timestamp: new Date()
    };
    
  } catch (error) {
    console.error('安全操作失败:', error);
    
    return {
      success: false,
      error: error.message,
      timestamp: new Date()
    };
  }
}; 