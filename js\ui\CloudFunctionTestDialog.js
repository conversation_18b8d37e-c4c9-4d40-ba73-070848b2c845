/**
 * 云函数测试对话框
 * 提供可滑动的界面来测试各个数据表的CRUD操作
 */
class CloudFunctionTestDialog {
  constructor(ctx, screenWidth, screenHeight, onClose) {
    this.ctx = ctx
    this.screenWidth = screenWidth
    this.screenHeight = screenHeight
    this.onClose = onClose
    
    // 对话框尺寸
    this.dialogWidth = Math.min(screenWidth * 0.9, 400)
    this.dialogHeight = Math.min(screenHeight * 0.8, 600)
    this.dialogX = (screenWidth - this.dialogWidth) / 2
    this.dialogY = (screenHeight - this.dialogHeight) / 2
    
    // 滚动相关
    this.scrollY = 0
    this.maxScrollY = 0
    this.isDragging = false
    this.lastTouchY = 0
    
    // 按钮配置
    this.buttonHeight = 40
    this.buttonMargin = 10
    this.buttonsPerRow = 2
    this.buttonWidth = (this.dialogWidth - this.buttonMargin * 3) / this.buttonsPerRow
    
    // 支持的数据表
    this.tables = [
      { name: 'players', label: '玩家信息', color: '#4CAF50' },
      { name: 'player_res', label: '玩家资源', color: '#2196F3' },
      { name: 'characters', label: '角色', color: '#FF9800' },
      { name: 'player_treasures', label: '古宝', color: '#9C27B0' },
      { name: 'player_skills', label: '技能', color: '#F44336' },
      { name: 'player_items', label: '物品', color: '#795548' },
      { name: 'sword_hearts', label: '剑心', color: '#00BCD4' },
      { name: 'sword_bones', label: '剑骨', color: '#607D8B' },
      { name: 'player_dongf', label: '洞府', color: '#8BC34A' },
      { name: 'player_arena', label: '竞技场', color: '#E91E63' },
      { name: 'player_idle', label: '挂机', color: '#3F51B5' },
      { name: 'p_skill_cul', label: '技能修炼', color: '#CDDC39' },
      { name: 'recharge_rec', label: '充值记录', color: '#FFC107' },
      { name: 'gacha_records', label: '抽卡记录', color: '#673AB7' },
      { name: 'battle_records', label: '战斗记录', color: '#009688' },
      { name: 'mail_temp', label: '邮件模板', color: '#FF5722' },
      { name: 'player_mails', label: '玩家邮件', color: '#9E9E9E' },
      { name: 'game_configs', label: '游戏配置', color: '#5D4037' }
    ]
    
    // 计算内容高度
    const rowCount = Math.ceil(this.tables.length / this.buttonsPerRow)
    this.contentHeight = rowCount * (this.buttonHeight + this.buttonMargin) + this.buttonMargin * 3 + 100 // 额外空间
    this.maxScrollY = Math.max(0, this.contentHeight - (this.dialogHeight - 100))
    
    // 测试结果
    this.testResults = {}
    this.showResults = false
  }
  
  // 检查点是否在对话框内（BaseScene要求的方法）
  isPointInside(x, y) {
    // 对话框覆盖整个屏幕，拦截所有触摸事件
    return true;
  }
  
  // 渲染方法（BaseScene要求的方法）
  render() {
    this.draw();
  }
  
  // BaseScene要求的触摸事件方法（别名）
  onTouchStart(x, y) {
    return this.handleTouchStart(x, y);
  }
  
  onTouchMove(x, y) {
    return this.handleTouchMove(x, y);
  }
  
  onTouchEnd(x, y) {
    return this.handleTouchEnd(x, y);
  }
  
  // 处理触摸开始
  handleTouchStart(x, y) {
    if (!this.isInDialog(x, y)) {
      this.onClose()
      return true
    }
    
    this.isDragging = true
    this.lastTouchY = y
    return true
  }
  
  // 处理触摸移动
  handleTouchMove(x, y) {
    if (!this.isDragging) return false
    
    const deltaY = y - this.lastTouchY
    this.scrollY = Math.max(0, Math.min(this.maxScrollY, this.scrollY - deltaY))
    this.lastTouchY = y
    
    return true
  }
  
  // 处理触摸结束
  handleTouchEnd(x, y) {
    if (!this.isDragging) return false
    
    this.isDragging = false
    
    // 检查是否点击了按钮
    if (this.isInDialog(x, y)) {
      this.handleButtonClick(x, y)
    }
    
    return true
  }
  
  // 检查坐标是否在对话框内
  isInDialog(x, y) {
    return x >= this.dialogX && x <= this.dialogX + this.dialogWidth &&
           y >= this.dialogY && y <= this.dialogY + this.dialogHeight
  }
  
  // 处理按钮点击
  handleButtonClick(x, y) {
    // 关闭按钮
    const closeButtonX = this.dialogX + this.dialogWidth - 30
    const closeButtonY = this.dialogY + 10
    
    if (x >= closeButtonX && x <= closeButtonX + 20 &&
        y >= closeButtonY && y <= closeButtonY + 20) {
      this.onClose()
      return
    }
    
    // 测试结果切换按钮
    const resultButtonX = this.dialogX + 10
    const resultButtonY = this.dialogY + this.dialogHeight - 40
    
    if (x >= resultButtonX && x <= resultButtonX + 100 &&
        y >= resultButtonY && y <= resultButtonY + 30) {
      this.showResults = !this.showResults
      return
    }
    
    // 计算相对于内容区域的坐标
    const contentY = y - this.dialogY - 50 + this.scrollY
    
    // 计算点击的按钮
    const row = Math.floor(contentY / (this.buttonHeight + this.buttonMargin))
    const col = Math.floor((x - this.dialogX - this.buttonMargin) / (this.buttonWidth + this.buttonMargin))
    
    const tableIndex = row * this.buttonsPerRow + col
    
    if (tableIndex >= 0 && tableIndex < this.tables.length) {
      const table = this.tables[tableIndex]
      this.showTableOperations(table)
    }
  }
  
  // 显示表操作菜单
  showTableOperations(table) {
    const operations = [
      { name: 'create', label: '创建记录', action: () => this.testCreate(table) },
      { name: 'find', label: '查询记录', action: () => this.testFind(table) },
      { name: 'update', label: '更新记录', action: () => this.testUpdate(table) },
      { name: 'delete', label: '删除记录', action: () => this.testDelete(table) },
      { name: 'count', label: '统计记录', action: () => this.testCount(table) }
    ]
    
    // 简化版：直接执行查询操作作为演示
    this.testFind(table)
  }
  
  // 测试创建记录
  async testCreate(table) {
    try {
      console.log(`测试创建${table.label}记录`)
      
      // 根据不同表生成测试数据
      const testData = this.generateTestData(table.name)
      
      const result = await wx.cloud.callFunction({
        name: 'playerService',
        data: {
          action: 'create',
          table: table.name,
          data: testData
        }
      })
      
      this.testResults[table.name] = {
        operation: 'create',
        success: result.result.success,
        data: result.result.data,
        error: result.result.error,
        timestamp: Date.now()
      }
      
      console.log(`${table.label}创建结果:`, result.result)
      
    } catch (error) {
      console.error(`${table.label}创建失败:`, error)
      this.testResults[table.name] = {
        operation: 'create',
        success: false,
        error: error.message,
        timestamp: Date.now()
      }
    }
  }
  
  // 测试查询记录
  async testFind(table) {
    try {
      console.log(`测试查询${table.label}记录`)
      
      const result = await wx.cloud.callFunction({
        name: 'playerService',
        data: {
          action: 'find',
          table: table.name,
          query: {},
          options: { limit: 10 }
        }
      })
      
      this.testResults[table.name] = {
        operation: 'find',
        success: result.result.success,
        data: result.result.data,
        error: result.result.error,
        timestamp: Date.now()
      }
      
      console.log(`${table.label}查询结果:`, result.result)
      
    } catch (error) {
      console.error(`${table.label}查询失败:`, error)
      this.testResults[table.name] = {
        operation: 'find',
        success: false,
        error: error.message,
        timestamp: Date.now()
      }
    }
  }
  
  // 测试更新记录
  async testUpdate(table) {
    try {
      console.log(`测试更新${table.label}记录`)
      
      // 先查询一条记录
      const findResult = await wx.cloud.callFunction({
        name: 'playerService',
        data: {
          action: 'findOne',
          table: table.name,
          query: {}
        }
      })
      
      if (findResult.result.success && findResult.result.data) {
        // 更新测试数据
        const updateData = this.generateUpdateData(table.name)
        
        const result = await wx.cloud.callFunction({
          name: 'playerService',
          data: {
            action: 'update',
            table: table.name,
            query: { _id: findResult.result.data._id },
            data: updateData
          }
        })
        
        this.testResults[table.name] = {
          operation: 'update',
          success: result.result.success,
          data: result.result.data,
          error: result.result.error,
          timestamp: Date.now()
        }
        
        console.log(`${table.label}更新结果:`, result.result)
      } else {
        throw new Error('没有找到可更新的记录')
      }
      
    } catch (error) {
      console.error(`${table.label}更新失败:`, error)
      this.testResults[table.name] = {
        operation: 'update',
        success: false,
        error: error.message,
        timestamp: Date.now()
      }
    }
  }
  
  // 测试删除记录
  async testDelete(table) {
    // 注意：删除操作需要谨慎，这里只是演示
    console.log(`删除操作暂不执行，避免误删数据`)
    this.testResults[table.name] = {
      operation: 'delete',
      success: true,
      data: { message: '删除操作已跳过（保护数据）' },
      timestamp: Date.now()
    }
  }
  
  // 测试统计记录
  async testCount(table) {
    try {
      console.log(`测试统计${table.label}记录`)
      
      const result = await wx.cloud.callFunction({
        name: 'playerService',
        data: {
          action: 'count',
          table: table.name,
          query: {}
        }
      })
      
      this.testResults[table.name] = {
        operation: 'count',
        success: result.result.success,
        data: result.result.data,
        error: result.result.error,
        timestamp: Date.now()
      }
      
      console.log(`${table.label}统计结果:`, result.result)
      
    } catch (error) {
      console.error(`${table.label}统计失败:`, error)
      this.testResults[table.name] = {
        operation: 'count',
        success: false,
        error: error.message,
        timestamp: Date.now()
      }
    }
  }
  
  // 生成测试数据
  generateTestData(tableName) {
    const testData = {}
    
    switch (tableName) {
      case 'players':
        return {
          nickname: '测试玩家',
          avatar_url: '',
          server_id: 1,
          level: 1,
          exp: 0,
          power: 100,
          cultivation_realm: '炼气期一层',
          dongfu_level: 1,
          vip_level: 0,
          total_recharge: 0,
          last_vip_reward_time: null,
          last_login_time: Date.now(),
          last_offline_time: null,
          registration_time: Date.now(),
          game_settings: {}
        }
        
      case 'player_res':
        return {
          xianyu: 1000,
          lingshi: 1000,
          sword_intent: 0,
          lianlidian: 100,
          spirit_stone: 0,
          tiangang_stone: 0,
          xiuwei_point: 0,
          arena_point: 0,
          guild_contribution: 0
        }
        
      case 'characters':
        return {
          character_id: 'test_char_001',
          name: '测试角色',
          level: 1,
          exp: 0,
          star: 0,
          power: 100,
          cultivation: '炼气期一层',
          base_attributes: { attack: 100, defense: 50, hp: 500 },
          bonus_attributes: {},
          total_attributes: { attack: 100, defense: 50, hp: 500 },
          equipped_skills: [],
          breakthrough_materials: {}
        }
        
      case 'player_treasures':
        return {
          treasure_id: 'test_treasure_001',
          name: '测试古宝',
          category: 'weapon',
          rarity: 1,
          level: 1,
          max_level: 100,
          star: 0,
          base_attributes: { attack: 50 },
          current_attributes: { attack: 50 },
          is_equipped: false,
  
          acquired_time: Date.now()
        }
        
      case 'sword_hearts':
        return {
          sword_heart_id: 'test_heart_001',
          name: '测试剑心',
          level: 0,
          max_level: 10,
          advancement_level: 0,
          max_advancement_level: 9,
          sword_intent_invested: 0,
          total_attributes: {},
          color: '#4299e1',
          description: '测试用剑心'
        }
        
      case 'sword_bones':
        return {
          level: 0,
          rank: 0,
          total_attributes: {},
          upgrade_materials_used: {}
        }
        
      default:
        return {
          test_field: '测试数据',
          created_time: Date.now()
        }
    }
  }
  
  // 生成更新数据
  generateUpdateData(tableName) {
    switch (tableName) {
      case 'players':
        return { level: 2, power: 200 }
      case 'player_res':
        return { xianyu: 1500, lingshi: 1500 }
      case 'characters':
        return { level: 2, power: 150 }
      default:
        return { test_field: '更新后的测试数据' }
    }
  }
  
  // 绘制对话框
  draw() {
    // 绘制半透明背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)'
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight)
    
    // 绘制对话框背景
    this.ctx.fillStyle = '#ffffff'
    this.ctx.fillRect(this.dialogX, this.dialogY, this.dialogWidth, this.dialogHeight)
    
    // 绘制边框
    this.ctx.strokeStyle = '#cccccc'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(this.dialogX, this.dialogY, this.dialogWidth, this.dialogHeight)
    
    // 绘制标题
    this.ctx.fillStyle = '#333333'
    this.ctx.font = 'bold 18px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText('云函数测试', this.dialogX + this.dialogWidth / 2, this.dialogY + 30)
    
    // 绘制关闭按钮
    this.ctx.fillStyle = '#ff4444'
    this.ctx.fillRect(this.dialogX + this.dialogWidth - 30, this.dialogY + 10, 20, 20)
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 12px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText('×', this.dialogX + this.dialogWidth - 20, this.dialogY + 24)
    
    // 绘制滚动内容
    this.drawContent()
    
    // 绘制测试结果切换按钮
    this.ctx.fillStyle = this.showResults ? '#4CAF50' : '#757575'
    this.ctx.fillRect(this.dialogX + 10, this.dialogY + this.dialogHeight - 40, 100, 30)
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = '14px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText(this.showResults ? '隐藏结果' : '显示结果', 
                     this.dialogX + 60, this.dialogY + this.dialogHeight - 22)
  }
  
  // 绘制内容
  drawContent() {
    // 设置裁剪区域
    this.ctx.save()
    this.ctx.beginPath()
    this.ctx.rect(this.dialogX + 5, this.dialogY + 50, this.dialogWidth - 10, this.dialogHeight - 100)
    this.ctx.clip()
    
    if (this.showResults) {
      this.drawTestResults()
    } else {
      this.drawTableButtons()
    }
    
    this.ctx.restore()
  }
  
  // 绘制表格按钮
  drawTableButtons() {
    let currentY = this.dialogY + 60 - this.scrollY
    
    for (let i = 0; i < this.tables.length; i++) {
      const table = this.tables[i]
      const row = Math.floor(i / this.buttonsPerRow)
      const col = i % this.buttonsPerRow
      
      const buttonX = this.dialogX + this.buttonMargin + col * (this.buttonWidth + this.buttonMargin)
      const buttonY = currentY + row * (this.buttonHeight + this.buttonMargin)
      
      // 只绘制可见的按钮
      if (buttonY + this.buttonHeight >= this.dialogY + 50 && 
          buttonY <= this.dialogY + this.dialogHeight - 50) {
        
        // 根据测试结果确定按钮颜色
        let buttonColor = table.color
        if (this.testResults[table.name]) {
          buttonColor = this.testResults[table.name].success ? '#4CAF50' : '#F44336'
        }
        
        // 绘制按钮背景
        this.ctx.fillStyle = buttonColor
        this.ctx.fillRect(buttonX, buttonY, this.buttonWidth, this.buttonHeight)
        
        // 绘制按钮文字
        this.ctx.fillStyle = '#ffffff'
        this.ctx.font = '14px Arial'
        this.ctx.textAlign = 'center'
        this.ctx.fillText(table.label, 
                         buttonX + this.buttonWidth / 2, 
                         buttonY + this.buttonHeight / 2 + 5)
      }
    }
  }
  
  // 绘制测试结果
  drawTestResults() {
    let currentY = this.dialogY + 60 - this.scrollY
    
    this.ctx.font = '12px Arial'
    this.ctx.textAlign = 'left'
    
    for (const [tableName, result] of Object.entries(this.testResults)) {
      const table = this.tables.find(t => t.name === tableName)
      if (!table) continue
      
      // 绘制表名
      this.ctx.fillStyle = '#333333'
      this.ctx.font = 'bold 14px Arial'
      this.ctx.fillText(`${table.label} (${result.operation})`, this.dialogX + 10, currentY)
      currentY += 20
      
      // 绘制结果状态
      this.ctx.fillStyle = result.success ? '#4CAF50' : '#F44336'
      this.ctx.font = '12px Arial'
      this.ctx.fillText(`状态: ${result.success ? '成功' : '失败'}`, this.dialogX + 10, currentY)
      currentY += 15
      
      // 绘制错误信息或数据
      if (result.error) {
        this.ctx.fillStyle = '#F44336'
        this.ctx.fillText(`错误: ${result.error}`, this.dialogX + 10, currentY)
        currentY += 15
      } else if (result.data) {
        this.ctx.fillStyle = '#666666'
        const dataStr = typeof result.data === 'object' ? 
                       JSON.stringify(result.data).substring(0, 50) + '...' :
                       String(result.data).substring(0, 50)
        this.ctx.fillText(`数据: ${dataStr}`, this.dialogX + 10, currentY)
        currentY += 15
      }
      
      currentY += 10 // 间距
    }
    
    // 更新内容高度
    this.contentHeight = currentY - this.dialogY + 50
    this.maxScrollY = Math.max(0, this.contentHeight - (this.dialogHeight - 100))
  }
}

export default CloudFunctionTestDialog 