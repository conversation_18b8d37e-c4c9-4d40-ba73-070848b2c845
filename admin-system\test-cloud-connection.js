/**
 * 云函数连接测试脚本
 * 测试CloudFunctionAdapter和DataService的功能
 */

console.log('🚀 启动云函数连接测试...\n');

const CloudFunctionTest = require('./lib/CloudFunctionTest');
const DataService = require('./lib/DataService');

async function runTests() {
  console.log('='.repeat(60));
  console.log('🧪 任务2：CloudFunctionAdapter数据连接层开发测试');
  console.log('='.repeat(60));
  
  try {
    // 1. 测试CloudFunctionAdapter
    console.log('\n📦 1. 测试CloudFunctionAdapter...');
    const cloudTest = new CloudFunctionTest();
    
    // 快速健康检查
    console.log('\n⚡ 快速连接测试:');
    await cloudTest.quickTest();
    
    // 2. 测试DataService
    console.log('\n📦 2. 测试DataService...');
    const dataService = new DataService({
      enableLogging: true,
      enablePerformanceMonitoring: true,
      enableCache: false // 测试时禁用缓存
    });
    
    // 系统健康检查
    console.log('\n💚 系统健康检查:');
    const healthResult = await dataService.getSystemHealth();
    console.log('健康状态:', healthResult.success ? '✅ 正常' : '❌ 异常');
    if (healthResult.success) {
      console.log('- 系统状态:', healthResult.data.status);
      console.log('- 配置表数量:', healthResult.data.tables.length);
      console.log('- 缓存状态:', healthResult.data.cache.enabled ? '启用' : '禁用');
    }
    
    // 系统统计
    console.log('\n📊 系统统计数据:');
    const statsResult = await dataService.getSystemStats();
    console.log('统计结果:', statsResult.success ? '✅ 成功' : '❌ 失败');
    if (statsResult.success) {
      const { overview } = statsResult.data;
      console.log('- 玩家总数:', overview.totalPlayers);
      console.log('- 资源记录:', overview.totalResources);
      console.log('- 古宝记录:', overview.totalTreasures);
      console.log('- 邮件数量:', overview.totalMails);
    }
    
    // 玩家列表测试（前5个）
    console.log('\n👥 玩家列表测试:');
    const playersResult = await dataService.getPlayerList({ limit: 5 });
    console.log('获取玩家:', playersResult.success ? '✅ 成功' : '❌ 失败');
    if (playersResult.success) {
      const { players, pagination } = playersResult.data;
      console.log(`- 获取到 ${players.length} 个玩家 (总计: ${pagination.total})`);
      
      if (players.length > 0) {
        const player = players[0];
        console.log('- 示例玩家:', {
          nickname: player.nickname,
          level: player.level,
          realm: player.cultivation_realm,
          active: player.is_active ? '活跃' : '不活跃'
        });
      }
    }
    
    // 技能模板测试
    console.log('\n⚔️ 技能模板测试:');
    const skillsResult = await dataService.getSkillTemplates({ limit: 3 });
    console.log('获取技能:', skillsResult.success ? '✅ 成功' : '❌ 失败');
    if (skillsResult.success) {
      const { skills } = skillsResult.data;
      console.log(`- 技能模板数量: ${skills.length}`);
      
      if (skills.length > 0) {
        const skill = skills[0];
        console.log('- 示例技能:', {
          name: skill.name,
          type: skill.type,
          level: skill.level,
          damage: skill.damage
        });
      }
    }
    
    // 古宝模板测试
    console.log('\n🏺 古宝模板测试:');
    const treasuresResult = await dataService.getTreasureTemplates({ limit: 3 });
    console.log('获取古宝:', treasuresResult.success ? '✅ 成功' : '❌ 失败');
    if (treasuresResult.success) {
      const { treasures } = treasuresResult.data;
      console.log(`- 古宝模板数量: ${treasures.length}`);
      
      if (treasures.length > 0) {
        const treasure = treasures[0];
        console.log('- 示例古宝:', {
          name: treasure.name,
          type: treasure.type,
          grade: treasure.grade,
          attack: treasure.base_attack
        });
      }
    }
    
    console.log('\n' + '='.repeat(60));
    console.log('🎉 任务2测试完成！CloudFunctionAdapter和DataService运行正常');
    console.log('='.repeat(60));
    
  } catch (error) {
    console.error('\n❌ 测试过程中发生错误:', error.message);
    console.error('错误详情:', error);
    console.log('\n' + '='.repeat(60));
    console.log('⚠️ 测试未完全通过，请检查配置和网络连接');
    console.log('='.repeat(60));
  }
}

// 运行测试
runTests().then(() => {
  console.log('\n✨ 测试脚本执行完成');
}).catch(error => {
  console.error('\n💥 测试脚本执行失败:', error.message);
}); 