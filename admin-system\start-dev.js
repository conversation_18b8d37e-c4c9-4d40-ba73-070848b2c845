#!/usr/bin/env node

/**
 * 开发环境启动脚本
 * 设置环境变量并启动服务器
 */

// 设置开发环境变量
process.env.NODE_ENV = 'development';
process.env.USE_MOCK_DB = 'true';
process.env.PORT = process.env.PORT || '3000';

console.log('🚀 启动修仙游戏后台管理系统 - 开发模式');
console.log('📊 环境配置:');
console.log(`   - 运行模式: ${process.env.NODE_ENV}`);
console.log(`   - 数据库: 模拟数据库`);
console.log(`   - 端口: ${process.env.PORT}`);
console.log('');

// 启动服务器
require('./server.js'); 