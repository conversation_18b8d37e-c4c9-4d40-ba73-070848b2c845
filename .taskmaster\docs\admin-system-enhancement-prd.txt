# 修仙六道后台管理系统完善 - 产品需求文档

## 项目概述

### 产品名称
修仙六道微信小游戏后台管理系统增强版

### 产品目标
将现有的基于模拟数据的后台管理系统升级为连接真实游戏数据库的完整管理平台，支持游戏运营数据分析、玩家管理、内容管理等核心功能。

### 技术背景
- 游戏端：微信小游戏原生框架 + 云开发数据库(cloud1-9gzbxxbff827656f)
- 现有后台：Node.js + Express + EJS，使用mockDatabase模拟数据
- 云函数：databaseService支持完整的CRUD操作
- 数据表：15+核心数据表，包括玩家、资源、装备、技能等

## 核心问题分析

### 现存问题
1. **数据源问题**：后台管理系统使用mockDatabase，无法访问真实游戏数据
2. **环境不一致**：后台配置环境(prod-3g8ag9vk8ca8b6dc)与游戏环境(cloud1-9gzbxxbff827656f)不同
3. **功能缺失**：缺少玩家数据管理、实时统计、数据监控等核心运营功能
4. **数据权限**：需要建立安全的数据访问机制

### 解决方案概览
通过云函数代理的方式，让后台管理系统调用游戏端的databaseService云函数，实现数据的统一管理。

## 功能需求

### 1. 数据连接层重构
**优先级：P0（最高）**

#### 1.1 云函数连接适配器
- 创建CloudFunctionAdapter类，封装对databaseService的调用
- 支持所有databaseService的action：create、read、update、delete、list、count
- 实现错误处理和重试机制
- 添加请求日志和性能监控

#### 1.2 环境配置统一
- 更新环境配置，统一使用cloud1-9gzbxxbff827656f
- 添加环境变量验证和错误处理
- 支持开发/生产环境切换

#### 1.3 数据访问安全
- 实现管理员身份验证
- 添加操作权限控制
- 记录所有数据操作的审计日志

### 2. 玩家数据管理功能
**优先级：P0（最高）**

#### 2.1 玩家信息管理
- 玩家基础信息查看和编辑（players表）
- 玩家资源管理（player_res表）：仙玉、灵石、修为点等
- 玩家等级和境界管理
- 封号/解封功能

#### 2.2 玩家装备管理
- 玩家古宝查看（player_treasures表）
- 装备强化记录
- 稀有装备统计

#### 2.3 玩家技能管理
- 玩家技能查看（player_skill表）
- 技能等级和星级管理
- 技能修炼进度监控

### 3. 实时数据统计面板
**优先级：P1（高）**

#### 3.1 核心指标监控
- 在线玩家数统计
- 新增/活跃玩家数据
- 玩家等级分布
- 消费数据统计

#### 3.2 游戏内容统计
- 各类模板使用率统计
- 热门装备/技能排行
- 充值和消费数据分析

#### 3.3 数据可视化
- 使用Chart.js实现数据图表
- 实时数据刷新
- 可导出数据报表

### 4. 游戏内容管理优化
**优先级：P1（高）**

#### 4.1 模板管理增强
- 功法模板管理（skill_temp）
- 剑心模板管理（s_heart_temp）
- 古宝模板管理（treasure_tmp）
- 批量导入/导出功能

#### 4.2 邮件系统管理
- 邮件模板管理（mail_temp）
- 批量发送邮件
- 邮件发送记录查看

### 5. 系统运营工具
**优先级：P2（中）**

#### 5.1 数据备份和恢复
- 定期数据备份机制
- 数据恢复功能
- 备份文件管理

#### 5.2 系统监控
- 云函数调用监控
- 错误日志收集
- 性能指标监控

#### 5.3 批量操作工具
- 批量修改玩家数据
- 批量发放奖励
- 数据清理工具

## 技术实现方案

### 架构设计
```
后台管理系统 → CloudFunctionAdapter → 微信云函数调用 → databaseService → 云开发数据库
```

### 关键技术栈
- **后端**：Node.js + Express + EJS
- **云函数调用**：微信云开发HTTP API
- **前端增强**：Bootstrap 5 + Chart.js + jQuery
- **数据可视化**：Chart.js + ECharts
- **安全认证**：JWT + 管理员角色权限

### 数据表映射
| 功能模块 | 数据表 | 说明 |
|---------|--------|------|
| 玩家管理 | players, player_res | 基础信息和资源 |
| 装备管理 | player_treasures | 玩家古宝数据 |
| 技能管理 | player_skill, p_skill_cul | 技能和修炼数据 |
| 模板管理 | skill_temp, treasure_tmp, s_heart_temp | 游戏内容模板 |
| 邮件系统 | mail_temp, player_mails | 邮件管理 |
| 系统监控 | admin_logs | 操作审计 |

## 开发计划

### 第一阶段：数据连接层（1-2天）
1. 创建CloudFunctionAdapter
2. 环境配置统一
3. 基础数据访问测试

### 第二阶段：玩家管理功能（2-3天）
1. 玩家列表和搜索
2. 玩家详情页面
3. 玩家数据编辑功能
4. 玩家资源管理

### 第三阶段：数据统计和监控（2-3天）
1. 实时统计面板
2. 数据可视化图表
3. 运营数据报表

### 第四阶段：系统优化和测试（1-2天）
1. 性能优化
2. 安全测试
3. 功能测试
4. 文档完善

## 验收标准

### 基础功能验收
- [ ] 成功连接游戏真实数据库
- [ ] 能够查看和管理所有玩家数据
- [ ] 统计面板显示准确的实时数据
- [ ] 所有CRUD操作正常工作

### 性能要求
- 页面加载时间 < 3秒
- 数据查询响应时间 < 2秒
- 支持1000+玩家数据管理
- 99%的接口调用成功率

### 安全要求
- 管理员身份验证机制
- 操作权限控制
- 完整的操作审计日志
- 敏感数据访问保护

## 风险评估

### 技术风险
- 云函数调用频率限制
- 数据库访问权限问题
- 微信云开发API稳定性

### 业务风险
- 数据操作错误影响游戏
- 性能问题影响用户体验
- 安全漏洞导致数据泄露

### 风险缓解措施
- 实现完整的错误处理和重试机制
- 添加数据操作确认和回滚功能
- 建立完善的权限和审计体系
- 在测试环境充分验证后再部署

## 后续规划

### 第二期功能
- 游戏内消息推送系统
- 活动配置和管理
- 数据分析和BI报表
- 移动端管理应用

### 技术演进
- 微服务架构升级
- 实时数据推送
- AI辅助数据分析
- 自动化运营工具 