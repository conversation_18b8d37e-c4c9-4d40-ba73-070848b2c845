/**
 * 云数据库客户端封装
 * 负责与云函数进行数据交互
 */

class CloudDBClient {
  constructor() {
    // 操作类型常量
    this.OPERATIONS = {
      CREATE: 'create',
      CREATE_MANY: 'createMany',
      UPDATE: 'update',
      UPDATE_MANY: 'updateMany',
      DELETE: 'delete',
      DELETE_MANY: 'deleteMany',
      GET: 'get',
      LIST: 'list',
      COUNT: 'count',
      AGGREGATE: 'aggregate'
    }
    
    // 数据表常量
    this.TABLES = {
      PLAYERS: 'players',
      PLAYER_RESOURCES: 'player_resources',
      CHARACTERS: 'characters',
      PLAYER_TREASURES: 'player_treasures',
      PLAYER_SKILLS: 'player_skills',
      PLAYER_ITEMS: 'player_items',
      PLAYER_SWORD_HEARTS: 'player_sword_hearts',
      PLAYER_SWORD_BONES: 'player_sword_bones',
      PLAYER_DONGFU: 'player_dongfu',
      PLAYER_ARENA: 'player_arena',
      PLAYER_IDLE: 'player_idle',
      PLAYER_SKILL_CULTIVATION: 'player_skill_cultivation',
      RECHARGE_RECORDS: 'recharge_records',
      GACHA_RECORDS: 'gacha_records',
      BATTLE_RECORDS: 'battle_records',
      MAIL_TEMPLATES: 'mail_templates',
      PLAYER_MAILS: 'player_mails',
      DAILY_TASKS: 'daily_tasks',
      ACTIVITY_PARTICIPATION: 'activity_participation',
      GAME_CONFIGS: 'game_configs'
    }
    
    // 玩家服务操作常量
    this.PLAYER_OPERATIONS = {
      GET_PLAYER_FULL_DATA: 'getPlayerFullData',
      CREATE_PLAYER: 'createPlayer',
      UPDATE_PLAYER_BASIC: 'updatePlayerBasic',
      UPDATE_RESOURCES: 'updateResources',
      ADD_RESOURCES: 'addResources',
      CONSUME_RESOURCES: 'consumeResources',
      GET_PLAYER_SUMMARY: 'getPlayerSummary',
      SYNC_PLAYER_DATA: 'syncPlayerData',
      BACKUP_PLAYER_DATA: 'backupPlayerData'
    }
    
    // 错误重试配置
    this.retryConfig = {
      maxRetries: 3,
      retryDelay: 1000
    }
    
    // 本地缓存
    this.cache = new Map()
    this.cacheTimeout = 5 * 60 * 1000 // 5分钟缓存
  }
  
  /**
   * 调用通用数据库服务
   */
  async callDBService(table, operation, params = {}, options = {}) {
    const { skipAuth = false, useCache = false, cacheKey = null } = options
    
    // 检查缓存
    if (useCache && cacheKey) {
      const cached = this.getFromCache(cacheKey)
      if (cached) {
        return cached
      }
    }
    
    try {
      const result = await this.retryOperation(async () => {
        return await wx.cloud.callFunction({
          name: 'dbService',
          data: {
            table,
            operation,
            params,
            skipAuth
          }
        })
      })
      
      if (!result.result.success) {
        throw new Error(`数据库操作失败: ${result.result.error}`)
      }
      
      const data = result.result.data
      
      // 设置缓存
      if (useCache && cacheKey) {
        this.setCache(cacheKey, data)
      }
      
      return data
      
    } catch (error) {
      console.error(`数据库操作失败 [${table}.${operation}]:`, error)
      throw error
    }
  }
  
  /**
   * 调用玩家服务
   */
  async callPlayerService(operation, params = {}) {
    try {
      const result = await this.retryOperation(async () => {
        return await wx.cloud.callFunction({
          name: 'playerService',
          data: {
            operation,
            params
          }
        })
      })
      
      if (!result.result.success) {
        throw new Error(`玩家服务操作失败: ${result.result.error}`)
      }
      
      return result.result.data
      
    } catch (error) {
      console.error(`玩家服务操作失败 [${operation}]:`, error)
      throw error
    }
  }
  
  /**
   * 重试机制
   */
  async retryOperation(operation) {
    let lastError
    
    for (let i = 0; i <= this.retryConfig.maxRetries; i++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error
        
        // 如果是最后一次重试，直接抛出错误
        if (i === this.retryConfig.maxRetries) {
          break
        }
        
        // 检查是否是可重试的错误
        if (!this.isRetryableError(error)) {
          break
        }
        
        // 等待重试延迟
        await this.sleep(this.retryConfig.retryDelay * (i + 1))
      }
    }
    
    throw lastError
  }
  
  /**
   * 判断是否为可重试的错误
   */
  isRetryableError(error) {
    const retryableErrors = [
      'network error',
      'timeout',
      'server error',
      'busy'
    ]
    
    const errorMessage = error.message.toLowerCase()
    return retryableErrors.some(keyword => errorMessage.includes(keyword))
  }
  
  /**
   * 延迟函数
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
  
  /**
   * 缓存操作
   */
  setCache(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }
  
  getFromCache(key) {
    const cached = this.cache.get(key)
    if (!cached) return null
    
    // 检查缓存是否过期
    if (Date.now() - cached.timestamp > this.cacheTimeout) {
      this.cache.delete(key)
      return null
    }
    
    return cached.data
  }
  
  clearCache(pattern = null) {
    if (pattern) {
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key)
        }
      }
    } else {
      this.cache.clear()
    }
  }
  
  // ==================== 便捷方法 ====================
  
  /**
   * 创建记录
   */
  async create(table, data) {
    return await this.callDBService(table, this.OPERATIONS.CREATE, { data })
  }
  
  /**
   * 批量创建记录
   */
  async createMany(table, dataArray) {
    return await this.callDBService(table, this.OPERATIONS.CREATE_MANY, { data: dataArray })
  }
  
  /**
   * 更新记录
   */
  async update(table, filter, data) {
    return await this.callDBService(table, this.OPERATIONS.UPDATE, { filter, data })
  }
  
  /**
   * 批量更新记录
   */
  async updateMany(table, filter, data) {
    return await this.callDBService(table, this.OPERATIONS.UPDATE_MANY, { filter, data })
  }
  
  /**
   * 删除记录
   */
  async delete(table, filter) {
    return await this.callDBService(table, this.OPERATIONS.DELETE, { filter })
  }
  
  /**
   * 批量删除记录
   */
  async deleteMany(table, filter) {
    return await this.callDBService(table, this.OPERATIONS.DELETE_MANY, { filter })
  }
  
  /**
   * 获取单条记录
   */
  async get(table, filter, fields = null, useCache = false) {
    const cacheKey = useCache ? `get_${table}_${JSON.stringify(filter)}` : null
    return await this.callDBService(
      table, 
      this.OPERATIONS.GET, 
      { filter, fields },
      { useCache, cacheKey }
    )
  }
  
  /**
   * 获取列表记录
   */
  async list(table, filter = {}, options = {}) {
    const {
      fields = null,
      pageSize = 20,
      pageNumber = 1,
      getCount = false,
      useCache = false
    } = options
    
    const cacheKey = useCache ? 
      `list_${table}_${JSON.stringify(filter)}_${pageNumber}_${pageSize}` : null
      
    return await this.callDBService(
      table,
      this.OPERATIONS.LIST,
      { filter, fields, pageSize, pageNumber, getCount },
      { useCache, cacheKey }
    )
  }
  
  /**
   * 统计记录数量
   */
  async count(table, filter = {}) {
    return await this.callDBService(table, this.OPERATIONS.COUNT, { filter })
  }
  
  // ==================== 玩家数据相关方法 ====================
  
  /**
   * 获取玩家完整数据
   */
  async getPlayerFullData(options = {}) {
    return await this.callPlayerService(this.PLAYER_OPERATIONS.GET_PLAYER_FULL_DATA, options)
  }
  
  /**
   * 创建新玩家
   */
  async createPlayer(playerInfo) {
    return await this.callPlayerService(this.PLAYER_OPERATIONS.CREATE_PLAYER, playerInfo)
  }
  
  /**
   * 更新玩家基础信息
   */
  async updatePlayerBasic(data) {
    return await this.callPlayerService(this.PLAYER_OPERATIONS.UPDATE_PLAYER_BASIC, { data })
  }
  
  /**
   * 更新玩家资源
   */
  async updateResources(resources) {
    return await this.callPlayerService(this.PLAYER_OPERATIONS.UPDATE_RESOURCES, { resources })
  }
  
  /**
   * 添加资源
   */
  async addResources(resources, reason = '系统奖励') {
    return await this.callPlayerService(this.PLAYER_OPERATIONS.ADD_RESOURCES, { resources, reason })
  }
  
  /**
   * 消耗资源
   */
  async consumeResources(resources, reason = '系统消耗', allowNegative = false) {
    return await this.callPlayerService(this.PLAYER_OPERATIONS.CONSUME_RESOURCES, { 
      resources, 
      reason, 
      allowNegative 
    })
  }
  
  /**
   * 获取玩家简要信息
   */
  async getPlayerSummary() {
    return await this.callPlayerService(this.PLAYER_OPERATIONS.GET_PLAYER_SUMMARY)
  }
  
  /**
   * 同步玩家数据
   */
  async syncPlayerData(gameState, userInfo = null) {
    return await this.callPlayerService(this.PLAYER_OPERATIONS.SYNC_PLAYER_DATA, { 
      gameState,
      userInfo
    })
  }
  
  /**
   * 备份玩家数据
   */
  async backupPlayerData(reason = '手动备份') {
    return await this.callPlayerService(this.PLAYER_OPERATIONS.BACKUP_PLAYER_DATA, { reason })
  }
  
  // ==================== 角色相关方法 ====================
  
  /**
   * 获取玩家角色列表
   */
  async getPlayerCharacters() {
    return await this.list(this.TABLES.CHARACTERS, {}, {
      orderBy: [{ field: 'position_in_formation', direction: 'asc' }],
      useCache: true
    })
  }
  
  /**
   * 添加角色
   */
  async addCharacter(characterData) {
    const result = await this.create(this.TABLES.CHARACTERS, characterData)
    this.clearCache('characters')
    return result
  }
  
  /**
   * 更新角色
   */
  async updateCharacter(characterId, updateData) {
    const result = await this.update(
      this.TABLES.CHARACTERS,
      { where: { _id: characterId } },
      updateData
    )
    this.clearCache('characters')
    return result
  }
  
  // ==================== 古宝相关方法 ====================
  
  /**
   * 获取玩家古宝列表
   */
  async getPlayerTreasures(category = null, rarity = null) {
    const filter = { where: {} }
    
    if (category) {
      filter.where.category = category
    }
    
    if (rarity) {
      filter.where.rarity = rarity
    }
    
    return await this.list(this.TABLES.PLAYER_TREASURES, filter, {
      orderBy: [
        { field: 'rarity', direction: 'desc' },
        { field: 'level', direction: 'desc' }
      ]
    })
  }
  
  /**
   * 添加古宝
   */
  async addTreasure(treasureData) {
    return await this.create(this.TABLES.PLAYER_TREASURES, treasureData)
  }
  
  /**
   * 升级古宝
   */
  async upgradeTreasure(treasureId, newLevel, newAttributes) {
    return await this.update(
      this.TABLES.PLAYER_TREASURES,
      { where: { _id: treasureId } },
      { 
        level: newLevel, 
        current_attributes: newAttributes,
        updated_at: new Date()
      }
    )
  }
  
  // ==================== 技能相关方法 ====================
  
  /**
   * 获取玩家技能列表
   */
  async getPlayerSkills() {
    return await this.list(this.TABLES.PLAYER_SKILLS, {}, {
      orderBy: [
        { field: 'quality', direction: 'desc' },
        { field: 'level', direction: 'desc' }
      ]
    })
  }
  
  /**
   * 添加技能
   */
  async addSkill(skillData) {
    return await this.create(this.TABLES.PLAYER_SKILLS, skillData)
  }
  
  /**
   * 升级技能
   */
  async upgradeSkill(skillId, newLevel, newAttributes) {
    return await this.update(
      this.TABLES.PLAYER_SKILLS,
      { where: { _id: skillId } },
      { 
        level: newLevel,
        current_attributes: newAttributes,
        updated_at: new Date()
      }
    )
  }
  
  // ==================== 背包相关方法 ====================
  
  /**
   * 获取背包物品
   */
  async getPlayerItems(type = null) {
    const filter = { where: {} }
    
    if (type) {
      filter.where.type = type
    }
    
    return await this.list(this.TABLES.PLAYER_ITEMS, filter, {
      orderBy: [{ field: 'acquired_time', direction: 'desc' }]
    })
  }
  
  /**
   * 添加物品
   */
  async addItem(itemData) {
    return await this.create(this.TABLES.PLAYER_ITEMS, itemData)
  }
  
  /**
   * 更新物品数量
   */
  async updateItemCount(itemId, count) {
    return await this.update(
      this.TABLES.PLAYER_ITEMS,
      { where: { _id: itemId } },
      { count: count }
    )
  }
  
  /**
   * 使用物品
   */
  async useItem(itemId, count = 1) {
    // 先获取当前物品信息
    const item = await this.get(
      this.TABLES.PLAYER_ITEMS,
      { where: { _id: itemId } }
    )
    
    if (!item) {
      throw new Error('物品不存在')
    }
    
    if (item.count < count) {
      throw new Error('物品数量不足')
    }
    
    const newCount = item.count - count
    
    if (newCount <= 0) {
      // 如果数量为0，删除物品
      return await this.delete(
        this.TABLES.PLAYER_ITEMS,
        { where: { _id: itemId } }
      )
    } else {
      // 更新数量
      return await this.updateItemCount(itemId, newCount)
    }
  }
  
  // ==================== 系统数据相关方法 ====================
  
  /**
   * 获取洞府系统数据
   */
  async getDongfuData() {
    return await this.get(this.TABLES.PLAYER_DONGFU, {}, null, true)
  }
  
  /**
   * 更新洞府系统数据
   */
  async updateDongfuData(updateData) {
    return await this.update(
      this.TABLES.PLAYER_DONGFU,
      { where: {} },
      updateData
    )
  }
  
  /**
   * 获取竞技场数据
   */
  async getArenaData() {
    return await this.get(this.TABLES.PLAYER_ARENA, {}, null, true)
  }
  
  /**
   * 更新竞技场数据
   */
  async updateArenaData(updateData) {
    return await this.update(
      this.TABLES.PLAYER_ARENA,
      { where: {} },
      updateData
    )
  }
  
  /**
   * 获取剑心数据
   */
  async getSwordHeartsData() {
    return await this.list(this.TABLES.PLAYER_SWORD_HEARTS, {})
  }
  
  /**
   * 获取剑骨数据
   */
  async getSwordBoneData() {
    return await this.get(this.TABLES.PLAYER_SWORD_BONES, {}, null, true)
  }
  
  // ==================== 记录相关方法 ====================
  
  /**
   * 添加抽卡记录
   */
  async addGachaRecord(recordData) {
    return await this.create(this.TABLES.GACHA_RECORDS, recordData)
  }
  
  /**
   * 添加战斗记录
   */
  async addBattleRecord(recordData) {
    return await this.create(this.TABLES.BATTLE_RECORDS, recordData)
  }
  
  /**
   * 添加充值记录
   */
  async addRechargeRecord(recordData) {
    return await this.create(this.TABLES.RECHARGE_RECORDS, recordData)
  }
  
  // ==================== 邮件相关方法 ====================
  
  /**
   * 获取玩家邮件列表
   */
  async getPlayerMails(includeDeleted = false) {
    const filter = { where: {} }
    
    if (!includeDeleted) {
      filter.where.is_deleted = false
    }
    
    return await this.list(this.TABLES.PLAYER_MAILS, filter, {
      orderBy: [{ field: 'created_at', direction: 'desc' }]
    })
  }
  
  /**
   * 标记邮件为已读
   */
  async markMailAsRead(mailId) {
    return await this.update(
      this.TABLES.PLAYER_MAILS,
      { where: { _id: mailId } },
      { 
        is_read: true,
        read_time: new Date()
      }
    )
  }
  
  /**
   * 领取邮件奖励
   */
  async claimMailReward(mailId) {
    return await this.update(
      this.TABLES.PLAYER_MAILS,
      { where: { _id: mailId } },
      { 
        is_claimed: true,
        claim_time: new Date()
      }
    )
  }
  
  /**
   * 删除邮件
   */
  async deleteMail(mailId) {
    return await this.update(
      this.TABLES.PLAYER_MAILS,
      { where: { _id: mailId } },
      { 
        is_deleted: true,
        delete_time: new Date()
      }
    )
  }
}

// 导出类
export default CloudDBClient

// 兼容CommonJS
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CloudDBClient
} 

// 在全局环境中设置类（不是实例）
if (typeof window !== 'undefined') {
  window.CloudDBClient = CloudDBClient
}