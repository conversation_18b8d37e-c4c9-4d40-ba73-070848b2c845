# 云函数部署说明

## 概述

本项目使用微信云函数安全获取用户openid，符合微信官方推荐的最佳实践。已创建和优化了以下云函数：

## 云函数列表

### 1. getOpenId 云函数（新增）
- **路径**: `cloudfunctions/getOpenId/`
- **功能**: 专门用于安全获取用户openid
- **优势**: 
  - 由微信云开发自动注入可信的openid
  - 无需处理敏感凭证（如AppSecret）
  - 包含完整的错误处理和日志记录

### 2. login 云函数（已优化）
- **路径**: `cloudfunctions/login/`
- **功能**: 用户登录，获取openid、appid等信息
- **改进**: 
  - 增加了错误处理
  - 保持向后兼容性
  - 添加了详细的日志记录

## 部署步骤

### 1. 在微信开发者工具中部署

1. 打开微信开发者工具
2. 进入云开发控制台
3. 选择"云函数"标签页

#### 部署 getOpenId 云函数：
1. 右键点击 `cloudfunctions/getOpenId` 文件夹
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成

#### 更新 login 云函数：
1. 右键点击 `cloudfunctions/login` 文件夹
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成

### 2. 验证部署

在云开发控制台的"云函数"页面中，应该能看到：
- ✅ getOpenId (新增)
- ✅ login (已更新)
- ✅ claimMailReward
- ✅ giveReward
- ✅ updateUserData

### 3. 测试云函数

可以在云开发控制台中测试云函数：

#### 测试 getOpenId：
```json
{
  "timestamp": 1703123456789,
  "source": "test"
}
```

预期返回：
```json
{
  "success": true,
  "openid": "用户的openid",
  "appid": "小程序的appid",
  "timestamp": 1703123456789,
  "message": "成功获取用户openid"
}
```

## 代码改进

### 1. LoginManager.js 改进
- ✅ 优先使用 `getOpenId` 云函数
- ✅ 失败时自动回退到 `login` 云函数
- ✅ 增加了隐私权限检查（符合2023年微信要求）
- ✅ 完善的错误处理机制

### 2. 隐私权限合规
- ✅ 添加了 `checkPrivacyAuthStatus()` 方法
- ✅ 在调用云函数前进行隐私授权
- ✅ 在 `project.config.json` 中启用 `__usePrivacyCheck__`

### 3. 安全性提升
- ✅ 使用云函数获取openid，避免前端直接处理敏感信息
- ✅ 云函数由微信托管，自动注入可信的openid
- ✅ 无需在前端存储或传递AppSecret

## 注意事项

### 1. 云函数计费
- 微信云函数有免费额度（每月前100万次调用免费）
- 超出后按量计费（约0.0003元/次）
- 建议结合本地缓存，避免重复调用

### 2. 调试建议
- 在云开发控制台的"日志管理"中查看调用记录
- 使用 `console.log` 输出调试信息
- 测试时注意检查返回的数据格式

### 3. 错误处理
- 云函数包含完整的错误处理
- 前端代码有回退机制
- 用户体验友好的错误提示

## 使用方式

部署完成后，游戏会自动使用新的云函数获取openid：

1. 用户首次进入游戏
2. 显示隐私权限授权弹窗
3. 用户同意后，调用 `getOpenId` 云函数
4. 安全获取用户openid
5. 完成登录流程

这种方式完全符合微信官方的安全规范和最佳实践。
