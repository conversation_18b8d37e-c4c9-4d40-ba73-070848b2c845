/**
 * 邮箱场景
 * 显示玩家的邮件列表，支持查看邮件详情和领取奖励
 */

import BaseScene from './BaseScene.js';
import game from '../../game.js';

class MailScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager) {
    super(ctx, screenWidth, screenHeight, sceneManager);

    // 邮件数据
    this.mails = [];
    this.selectedMail = null;

    // UI状态
    this.currentPage = 0;
    this.mailsPerPage = 8;
    this.showingDetail = false;

    // UI元素
    this.backButton = null;
    this.refreshButton = null;
    this.prevPageButton = null;
    this.nextPageButton = null;
    this.mailDetailPanel = null;

    // 选中的导航项索引
    this.selectedTabIndex = -1;
  }

  // 场景显示时的回调
  async onShow(params) {
    console.log('MailScene.onShow() 打开邮箱页面');

    // 清空UI元素
    this.clearUIElements();

    // 初始化UI
    this.initUI();

    // 加载邮件数据
    await this.loadMails();

    // 设置为可见
    this.visible = true;
  }

  // 场景隐藏时的回调
  onHide() {
    console.log('MailScene.onHide() 关闭邮箱页面');

    // 清空UI元素
    this.clearUIElements();

    // 设置为不可见
    this.visible = false;
  }

  // 初始化UI
  initUI() {
    // 创建返回按钮
    this.backButton = {
      x: 20,
      y: 20,
      width: 60,
      height: 40,
      text: '返回',
      isEnabled: true
    };

    // 创建刷新按钮
    this.refreshButton = {
      x: this.screenWidth - 80,
      y: 20,
      width: 60,
      height: 40,
      text: '刷新',
      isEnabled: true
    };

    // 创建翻页按钮
    this.prevPageButton = {
      x: 50,
      y: this.screenHeight - 120,
      width: 80,
      height: 40,
      text: '上一页',
      isEnabled: false
    };

    this.nextPageButton = {
      x: this.screenWidth - 130,
      y: this.screenHeight - 120,
      width: 80,
      height: 40,
      text: '下一页',
      isEnabled: false
    };
  }

  // 加载邮件数据
  async loadMails() {
    try {
      console.log('开始加载邮件数据...');

      // 检查game对象和databaseManager是否存在
      if (!game) {
        console.error('game对象不存在');
        this.mails = [];
        this.updatePageButtons();
        return;
      }

      if (!game.databaseManager) {
        console.error('databaseManager不存在');
        this.mails = [];
        this.updatePageButtons();
        return;
      }

      // 检查用户是否已登录
      const openid = game.databaseManager.getCurrentOpenId();
      if (!openid) {
        console.warn('用户未登录，无法加载邮件数据');
        wx.showModal({
          title: '提示',
          content: '请先完成登录后再查看邮箱',
          showCancel: false,
          confirmText: '确定',
          success: () => {
            // 返回主页面
            this.sceneManager.showScene('main');
          }
        });
        return;
      }

      // 从数据库获取邮件列表
      this.mails = await game.databaseManager.getPlayerMails();

      console.log('邮件数据加载完成，邮件数量:', this.mails.length);

      // 更新翻页按钮状态
      this.updatePageButtons();

    } catch (error) {
      console.error('加载邮件数据失败:', error);

      // 显示更详细的错误信息
      let errorMessage = '加载邮件失败';
      if (error.message) {
        errorMessage += ': ' + error.message;
      }

      wx.showToast({
        title: errorMessage,
        icon: 'error',
        duration: 3000
      });

      // 设置空邮件列表
      this.mails = [];
      this.updatePageButtons();
    }
  }

  // 更新翻页按钮状态
  updatePageButtons() {
    const totalPages = Math.ceil(this.mails.length / this.mailsPerPage);

    this.prevPageButton.isEnabled = this.currentPage > 0;
    this.nextPageButton.isEnabled = this.currentPage < totalPages - 1;
  }

  // 获取当前页面的邮件
  getCurrentPageMails() {
    const startIndex = this.currentPage * this.mailsPerPage;
    const endIndex = Math.min(startIndex + this.mailsPerPage, this.mails.length);

    return this.mails.slice(startIndex, endIndex);
  }

  // 子类实现的绘制逻辑
  drawScene() {
    // 绘制背景
    this.ctx.fillStyle = '#f0f0f0';
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);

    if (this.showingDetail && this.selectedMail) {
      // 显示邮件详情
      this.drawMailDetail();
    } else {
      // 显示邮件列表
      this.drawMailList();
    }

    // 绘制UI按钮
    this.drawButtons();
  }

  // 绘制邮件列表
  drawMailList() {
    // 绘制标题
    this.ctx.fillStyle = '#333';
    this.ctx.font = 'bold 24px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('邮箱', this.screenWidth / 2, 50);

    // 绘制邮件统计信息
    this.ctx.font = '16px Arial';
    this.ctx.textAlign = 'left';
    const unreadCount = this.mails.filter(mail => !mail.is_read).length;
    this.ctx.fillText(`总邮件: ${this.mails.length} | 未读: ${unreadCount}`, 20, 80);

    // 绘制邮件列表
    const currentMails = this.getCurrentPageMails();
    const mailHeight = 80;
    const startY = 100;

    currentMails.forEach((mail, index) => {
      const y = startY + index * mailHeight;
      this.drawMailItem(mail, y, mailHeight);
    });

    // 绘制页码信息
    const totalPages = Math.ceil(this.mails.length / this.mailsPerPage);
    this.ctx.fillStyle = '#666';
    this.ctx.font = '14px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(
      `第 ${this.currentPage + 1} 页 / 共 ${totalPages} 页`,
      this.screenWidth / 2,
      this.screenHeight - 80
    );
  }

  // 绘制单个邮件项
  drawMailItem(mail, y, height) {
    const padding = 10;
    const itemWidth = this.screenWidth - padding * 2;

    // 绘制背景
    this.ctx.fillStyle = mail.is_read ? '#ffffff' : '#e8f4fd';
    this.ctx.fillRect(padding, y, itemWidth, height - 5);

    // 绘制边框
    this.ctx.strokeStyle = '#ddd';
    this.ctx.lineWidth = 1;
    this.ctx.strokeRect(padding, y, itemWidth, height - 5);

    // 绘制邮件标题
    this.ctx.fillStyle = mail.is_read ? '#666' : '#333';
    this.ctx.font = mail.is_read ? '16px Arial' : 'bold 16px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(mail.title || '无标题', padding + 10, y + 25);

    // 绘制发送者
    this.ctx.fillStyle = '#888';
    this.ctx.font = '14px Arial';
    this.ctx.fillText(`发送者: ${mail.sender || '系统'}`, padding + 10, y + 45);

    // 绘制时间
    const timeStr = this.formatTime(mail.created_at);
    this.ctx.fillText(timeStr, padding + 10, y + 65);

    // 绘制状态标识
    if (mail.has_rewards && !mail.is_claimed) {
      this.ctx.fillStyle = '#ff6b6b';
      this.ctx.font = 'bold 12px Arial';
      this.ctx.textAlign = 'right';
      this.ctx.fillText('有奖励', this.screenWidth - padding - 10, y + 25);
    }

    if (!mail.is_read) {
      this.ctx.fillStyle = '#4dabf7';
      this.ctx.font = 'bold 12px Arial';
      this.ctx.fillText('未读', this.screenWidth - padding - 10, y + 45);
    }
  }

  // 格式化时间
  formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;

    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) { // 1天内
      return `${Math.floor(diff / 3600000)}小时前`;
    } else {
      return `${date.getMonth() + 1}/${date.getDate()}`;
    }
  }

  // 绘制按钮
  drawButtons() {
    const buttons = [this.backButton, this.refreshButton];

    if (!this.showingDetail) {
      buttons.push(this.prevPageButton, this.nextPageButton);
    }

    buttons.forEach(button => {
      if (button) {
        this.drawButton(button);
      }
    });
  }

  // 绘制单个按钮
  drawButton(button) {
    // 绘制按钮背景
    this.ctx.fillStyle = button.isEnabled ? '#4dabf7' : '#ccc';
    this.ctx.fillRect(button.x, button.y, button.width, button.height);

    // 绘制按钮文字
    this.ctx.fillStyle = button.isEnabled ? '#fff' : '#999';
    this.ctx.font = '14px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(
      button.text,
      button.x + button.width / 2,
      button.y + button.height / 2 + 5
    );
  }

  // 绘制邮件详情
  drawMailDetail() {
    // 绘制半透明背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);

    // 绘制详情面板
    const panelWidth = this.screenWidth - 40;
    const panelHeight = this.screenHeight - 100;
    const panelX = 20;
    const panelY = 50;

    this.ctx.fillStyle = '#fff';
    this.ctx.fillRect(panelX, panelY, panelWidth, panelHeight);

    // 绘制面板边框
    this.ctx.strokeStyle = '#ddd';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(panelX, panelY, panelWidth, panelHeight);

    // 绘制邮件标题
    this.ctx.fillStyle = '#333';
    this.ctx.font = 'bold 20px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(this.selectedMail.title || '无标题', this.screenWidth / 2, panelY + 40);

    // 绘制发送者和时间
    this.ctx.fillStyle = '#666';
    this.ctx.font = '14px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`发送者: ${this.selectedMail.sender || '系统'}`, panelX + 20, panelY + 70);

    const timeStr = this.formatTime(this.selectedMail.created_at);
    this.ctx.fillText(`时间: ${timeStr}`, panelX + 20, panelY + 90);

    // 绘制邮件内容
    this.ctx.fillStyle = '#333';
    this.ctx.font = '16px Arial';
    this.drawWrappedText(
      this.selectedMail.content || '无内容',
      panelX + 20,
      panelY + 120,
      panelWidth - 40,
      20
    );

    // 绘制奖励信息
    if (this.selectedMail.has_rewards) {
      this.drawRewards(panelX, panelY, panelWidth);
    }

    // 绘制操作按钮
    this.drawDetailButtons(panelX, panelY, panelWidth, panelHeight);
  }

  // 绘制换行文本
  drawWrappedText(text, x, y, maxWidth, lineHeight) {
    const words = text.split('');
    let line = '';
    let currentY = y;

    for (let i = 0; i < words.length; i++) {
      const testLine = line + words[i];
      const metrics = this.ctx.measureText(testLine);
      const testWidth = metrics.width;

      if (testWidth > maxWidth && i > 0) {
        this.ctx.fillText(line, x, currentY);
        line = words[i];
        currentY += lineHeight;
      } else {
        line = testLine;
      }
    }
    this.ctx.fillText(line, x, currentY);
  }

  // 绘制奖励信息
  drawRewards(panelX, panelY, panelWidth) {
    const rewardsY = panelY + 250;

    // 绘制奖励标题
    this.ctx.fillStyle = '#333';
    this.ctx.font = 'bold 16px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.fillText('附件奖励:', panelX + 20, rewardsY);

    // 绘制奖励背景
    this.ctx.fillStyle = '#f8f9fa';
    this.ctx.fillRect(panelX + 20, rewardsY + 10, panelWidth - 40, 80);

    // 绘制奖励内容
    if (this.selectedMail.rewards) {
      let rewardText = '';
      const rewards = this.selectedMail.rewards;

      if (rewards.xianyu) rewardText += `仙玉 x${rewards.xianyu}  `;
      if (rewards.lingshi) rewardText += `灵石 x${rewards.lingshi}  `;
      if (rewards.lianlidian) rewardText += `历练点 x${rewards.lianlidian}  `;
      if (rewards.sword_intent) rewardText += `剑意 x${rewards.sword_intent}  `;

      if (rewards.items && rewards.items.length > 0) {
        rewards.items.forEach(item => {
          rewardText += `${item.name} x${item.count}  `;
        });
      }

      this.ctx.fillStyle = '#333';
      this.ctx.font = '14px Arial';
      this.drawWrappedText(rewardText, panelX + 30, rewardsY + 35, panelWidth - 60, 18);
    }

    // 绘制领取状态
    if (this.selectedMail.is_claimed) {
      this.ctx.fillStyle = '#28a745';
      this.ctx.font = 'bold 14px Arial';
      this.ctx.textAlign = 'right';
      this.ctx.fillText('已领取', panelX + panelWidth - 30, rewardsY + 35);
    }
  }

  // 绘制详情页按钮
  drawDetailButtons(panelX, panelY, panelWidth, panelHeight) {
    const buttonY = panelY + panelHeight - 60;
    const buttonHeight = 40;

    // 关闭按钮
    const closeButton = {
      x: panelX + 20,
      y: buttonY,
      width: 80,
      height: buttonHeight,
      text: '关闭',
      isEnabled: true
    };

    // 删除按钮
    const deleteButton = {
      x: panelX + 120,
      y: buttonY,
      width: 80,
      height: buttonHeight,
      text: '删除',
      isEnabled: true
    };

    // 领取按钮（仅当有奖励且未领取时显示）
    let claimButton = null;
    if (this.selectedMail.has_rewards && !this.selectedMail.is_claimed) {
      claimButton = {
        x: panelX + panelWidth - 100,
        y: buttonY,
        width: 80,
        height: buttonHeight,
        text: '领取',
        isEnabled: true
      };
    }

    // 绘制按钮
    this.drawButton(closeButton);
    this.drawButton(deleteButton);
    if (claimButton) {
      this.drawButton(claimButton);
    }

    // 保存按钮引用以便点击检测
    this.detailButtons = {
      close: closeButton,
      delete: deleteButton,
      claim: claimButton
    };
  }

  // 子类实现的触摸开始事件处理
  handleTouchStart(x, y) {
    if (this.showingDetail) {
      return this.handleDetailTouch(x, y);
    } else {
      return this.handleListTouch(x, y);
    }
  }

  // 处理详情页面的触摸事件
  handleDetailTouch(x, y) {
    if (this.detailButtons) {
      // 检查关闭按钮
      if (this.isPointInButton(x, y, this.detailButtons.close)) {
        this.closeMailDetail();
        return true;
      }

      // 检查删除按钮
      if (this.isPointInButton(x, y, this.detailButtons.delete)) {
        this.deleteMail();
        return true;
      }

      // 检查领取按钮
      if (this.detailButtons.claim && this.isPointInButton(x, y, this.detailButtons.claim)) {
        this.claimReward();
        return true;
      }
    }

    return false;
  }

  // 处理列表页面的触摸事件
  handleListTouch(x, y) {
    // 检查返回按钮
    if (this.isPointInButton(x, y, this.backButton)) {
      this.sceneManager.showScene('main');
      return true;
    }

    // 检查刷新按钮
    if (this.isPointInButton(x, y, this.refreshButton)) {
      this.loadMails();
      return true;
    }

    // 检查翻页按钮
    if (this.prevPageButton.isEnabled && this.isPointInButton(x, y, this.prevPageButton)) {
      this.currentPage--;
      this.updatePageButtons();
      return true;
    }

    if (this.nextPageButton.isEnabled && this.isPointInButton(x, y, this.nextPageButton)) {
      this.currentPage++;
      this.updatePageButtons();
      return true;
    }

    // 检查邮件项点击
    const mail = this.getMailAtPosition(x, y);
    if (mail) {
      this.showMailDetail(mail);
      return true;
    }

    return false;
  }

  // 检查点是否在按钮内
  isPointInButton(x, y, button) {
    return x >= button.x && x <= button.x + button.width &&
           y >= button.y && y <= button.y + button.height;
  }

  // 获取指定位置的邮件
  getMailAtPosition(x, y) {
    const mailHeight = 80;
    const startY = 100;
    const currentMails = this.getCurrentPageMails();

    for (let i = 0; i < currentMails.length; i++) {
      const mailY = startY + i * mailHeight;
      if (y >= mailY && y <= mailY + mailHeight - 5) {
        return currentMails[i];
      }
    }

    return null;
  }

  // 显示邮件详情
  async showMailDetail(mail) {
    this.selectedMail = mail;
    this.showingDetail = true;

    // 标记为已读
    if (!mail.is_read) {
      try {
        await game.databaseManager.markMailAsRead(mail.mail_id);
        mail.is_read = true;
        mail.read_at = new Date();
      } catch (error) {
        console.error('标记邮件已读失败:', error);
      }
    }
  }

  // 关闭邮件详情
  closeMailDetail() {
    this.selectedMail = null;
    this.showingDetail = false;
    this.detailButtons = null;
  }

  // 删除邮件
  async deleteMail() {
    try {
      await game.databaseManager.deleteMail(this.selectedMail.mail_id);

      // 从本地列表中移除
      const index = this.mails.findIndex(mail => mail.mail_id === this.selectedMail.mail_id);
      if (index !== -1) {
        this.mails.splice(index, 1);
      }

      // 关闭详情页
      this.closeMailDetail();

      // 更新翻页按钮
      this.updatePageButtons();

      wx.showToast({
        title: '邮件已删除',
        icon: 'success',
        duration: 1500
      });
    } catch (error) {
      console.error('删除邮件失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'error',
        duration: 2000
      });
    }
  }

  // 领取奖励
  async claimReward() {
    try {
      // 调用云函数领取奖励
      const result = await game.cloud.callFunction({
        name: 'claimMailReward',
        data: {
          mailId: this.selectedMail.mail_id
        }
      });

      if (result.result.success) {
        // 更新本地邮件状态
        this.selectedMail.is_claimed = true;
        this.selectedMail.claimed_at = new Date();

        // 更新玩家资源
        if (result.result.rewards) {
          this.updatePlayerResources(result.result.rewards);
        }

        wx.showToast({
          title: '奖励领取成功',
          icon: 'success',
          duration: 2000
        });
      } else {
        throw new Error(result.result.error || '领取失败');
      }
    } catch (error) {
      console.error('领取奖励失败:', error);
      wx.showToast({
        title: '领取失败',
        icon: 'error',
        duration: 2000
      });
    }
  }

  // 更新玩家资源
  updatePlayerResources(rewards) {
    const player = game.gameStateManager.getPlayer();
    const resources = player.resources || {};

    // 更新基础资源
    if (rewards.xianyu) resources.xianyu = (resources.xianyu || 0) + rewards.xianyu;
    if (rewards.lingshi) resources.lingshi = (resources.lingshi || 0) + rewards.lingshi;
    if (rewards.lianlidian) resources.lianlidian = (resources.lianlidian || 0) + rewards.lianlidian;
    if (rewards.swordIntent) resources.swordIntent = (resources.swordIntent || 0) + rewards.swordIntent;

    // 更新物品
    if (rewards.items && rewards.items.length > 0) {
      rewards.items.forEach(item => {
        game.gameStateManager.addItem(item);
      });
    }

    // 保存玩家数据
    game.gameStateManager.setPlayer({
      ...player,
      resources: resources
    });

    game.gameStateManager.saveGameState();
  }
}

export default MailScene;
