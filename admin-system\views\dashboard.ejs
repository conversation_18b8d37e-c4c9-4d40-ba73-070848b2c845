<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %> - 修仙六道后台管理系统</title>
  
  <!-- Bootstrap 5 -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
  
  <!-- Chart.js -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
  
  <style>
    body {
      background-color: #f8f9fa;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    .sidebar {
      min-height: 100vh;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    }
    
    .sidebar .nav-link {
      color: rgba(255,255,255,0.8);
      padding: 12px 20px;
      border-radius: 8px;
      margin: 4px 8px;
      transition: all 0.3s ease;
    }
    
    .sidebar .nav-link:hover {
      background-color: rgba(255,255,255,0.1);
      color: white;
      transform: translateX(5px);
    }
    
    .sidebar .nav-link.active {
      background-color: rgba(255,255,255,0.2);
      color: white;
      font-weight: bold;
    }
    
    .stats-card {
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0,0,0,0.07);
      border: none;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .stats-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .stats-number {
      font-size: 2.5rem;
      font-weight: bold;
      color: #495057;
    }
    
    .stats-label {
      color: #6c757d;
      font-size: 0.95rem;
      margin-top: 5px;
    }
    
    .chart-container {
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0,0,0,0.07);
      padding: 25px;
      margin-bottom: 25px;
      position: relative;
      height: 400px;
    }
    
    .chart-title {
      font-size: 1.25rem;
      font-weight: bold;
      color: #495057;
      margin-bottom: 20px;
      text-align: center;
    }
    
    .loading-spinner {
      display: none;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 10;
    }
  </style>
</head>
<body>
  
  <!-- 顶部导航栏 -->
  <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom">
    <div class="container-fluid">
      <button class="navbar-toggler d-lg-none" type="button" id="sidebarToggle">
        <span class="navbar-toggler-icon"></span>
      </button>
      <span class="navbar-brand mb-0 h1">
        <i class="bi bi-graph-up text-primary"></i>
        数据统计面板
      </span>
      <div class="navbar-nav ms-auto">
        <span class="nav-link">
          <i class="bi bi-person-circle"></i>
          <%= user.username || 'admin' %>
        </span>
      </div>
    </div>
  </nav>

  <div class="container-fluid">
    <div class="row">
      
      <!-- 侧边栏 -->
      <nav class="col-lg-2 d-lg-block sidebar collapse" id="sidebar">
        <div class="position-sticky pt-3">
          <ul class="nav flex-column">
            <li class="nav-item">
              <a class="nav-link" href="/">
                <i class="bi bi-house"></i> 首页
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link active" href="/dashboard">
                <i class="bi bi-graph-up"></i> 数据统计
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/players">
                <i class="bi bi-people"></i> 玩家管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/player-resources">
                <i class="bi bi-gem"></i> 玩家资源
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/player-equipment">
                <i class="bi bi-shield"></i> 装备技能
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/mails">
                <i class="bi bi-envelope"></i> 邮件管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/skills">
                <i class="bi bi-book"></i> 功法模板
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/sword-hearts">
                <i class="bi bi-heart"></i> 剑心模板
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/treasures">
                <i class="bi bi-star"></i> 古宝模板
              </a>
            </li>
          </ul>
        </div>
      </nav>

      <!-- 主要内容 -->
      <main class="col-lg-10 ms-sm-auto px-md-4" style="padding: 20px;">
        
        <!-- 概览统计卡片 -->
        <div class="row mb-4">
          <div class="col-md-3 mb-3">
            <div class="card stats-card">
              <div class="card-body position-relative" style="padding: 25px;">
                <div class="stats-number" id="totalPlayers">-</div>
                <div class="stats-label">总玩家数</div>
                <i class="bi bi-people" style="font-size: 3rem; opacity: 0.2; position: absolute; right: 20px; top: 50%; transform: translateY(-50%); color: #007bff;"></i>
              </div>
            </div>
          </div>
          <div class="col-md-3 mb-3">
            <div class="card stats-card">
              <div class="card-body position-relative" style="padding: 25px;">
                <div class="stats-number" id="activePlayers">-</div>
                <div class="stats-label">活跃玩家（7天）</div>
                <i class="bi bi-person-check" style="font-size: 3rem; opacity: 0.2; position: absolute; right: 20px; top: 50%; transform: translateY(-50%); color: #28a745;"></i>
              </div>
            </div>
          </div>
          <div class="col-md-3 mb-3">
            <div class="card stats-card">
              <div class="card-body position-relative" style="padding: 25px;">
                <div class="stats-number" id="newPlayers">-</div>
                <div class="stats-label">新注册（7天）</div>
                <i class="bi bi-person-plus" style="font-size: 3rem; opacity: 0.2; position: absolute; right: 20px; top: 50%; transform: translateY(-50%); color: #17a2b8;"></i>
              </div>
            </div>
          </div>
          <div class="col-md-3 mb-3">
            <div class="card stats-card">
              <div class="card-body position-relative" style="padding: 25px;">
                <div class="stats-number" id="totalMails">-</div>
                <div class="stats-label">系统邮件</div>
                <i class="bi bi-envelope" style="font-size: 3rem; opacity: 0.2; position: absolute; right: 20px; top: 50%; transform: translateY(-50%); color: #ffc107;"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- 图表区域 -->
        <div class="row">
          
          <!-- 等级分布图表 -->
          <div class="col-lg-6 mb-4">
            <div class="chart-container">
              <div class="loading-spinner" id="levelChartSpinner">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">加载中...</span>
                </div>
              </div>
              <div class="chart-title">玩家等级分布</div>
              <canvas id="levelDistributionChart"></canvas>
            </div>
          </div>

          <!-- 境界分布图表 -->
          <div class="col-lg-6 mb-4">
            <div class="chart-container">
              <div class="loading-spinner" id="realmChartSpinner">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">加载中...</span>
                </div>
              </div>
              <div class="chart-title">玩家境界分布</div>
              <canvas id="realmDistributionChart"></canvas>
            </div>
          </div>

          <!-- 资源统计图表 -->
          <div class="col-lg-6 mb-4">
            <div class="chart-container">
              <div class="loading-spinner" id="resourceChartSpinner">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">加载中...</span>
                </div>
              </div>
              <div class="chart-title">平均资源分布</div>
              <canvas id="resourceStatsChart"></canvas>
            </div>
          </div>

          <!-- 活动统计图表 -->
          <div class="col-lg-6 mb-4">
            <div class="chart-container">
              <div class="loading-spinner" id="activityChartSpinner">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">加载中...</span>
                </div>
              </div>
              <div class="chart-title">系统活动统计</div>
              <canvas id="activityStatsChart"></canvas>
            </div>
          </div>

        </div>

        <!-- 最后更新时间 -->
        <div style="color: #6c757d; font-size: 0.85rem; text-align: center; margin-top: 10px;" id="lastUpdate">
          数据加载中...
        </div>

             </main>
     </div>
   </div>

   <!-- Bootstrap JS -->
   <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

   <script>
     // 全局变量
     let charts = {};
     let refreshInterval;
     
     // 页面加载完成后初始化
     document.addEventListener('DOMContentLoaded', function() {
       console.log('🚀 数据统计面板初始化...');
       
       // 初始化图表
       initCharts();
       
       // 加载数据
       loadDashboardData();
       
       // 设置自动刷新（每5分钟）
       refreshInterval = setInterval(loadDashboardData, 5 * 60 * 1000);
       
       // 侧边栏切换（移动端）
       const sidebarToggle = document.getElementById('sidebarToggle');
       const sidebar = document.getElementById('sidebar');
       
       if (sidebarToggle) {
         sidebarToggle.addEventListener('click', function() {
           sidebar.classList.toggle('show');
         });
       }
     });

     // 初始化图表
     function initCharts() {
       // 等级分布图表
       const levelCtx = document.getElementById('levelDistributionChart').getContext('2d');
       charts.levelDistribution = new Chart(levelCtx, {
         type: 'bar',
         data: {
           labels: [],
           datasets: [{
             label: '玩家数量',
             data: [],
             backgroundColor: 'rgba(54, 162, 235, 0.6)',
             borderColor: 'rgba(54, 162, 235, 1)',
             borderWidth: 1
           }]
         },
         options: {
           responsive: true,
           maintainAspectRatio: false,
           plugins: {
             legend: {
               display: false
             }
           },
           scales: {
             y: {
               beginAtZero: true,
               ticks: {
                 stepSize: 1
               }
             }
           }
         }
       });

       // 境界分布图表
       const realmCtx = document.getElementById('realmDistributionChart').getContext('2d');
       charts.realmDistribution = new Chart(realmCtx, {
         type: 'doughnut',
         data: {
           labels: [],
           datasets: [{
             data: [],
             backgroundColor: [
               '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', 
               '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
             ],
             borderWidth: 2,
             borderColor: '#fff'
           }]
         },
         options: {
           responsive: true,
           maintainAspectRatio: false,
           plugins: {
             legend: {
               position: 'bottom'
             }
           }
         }
       });

       // 资源统计图表
       const resourceCtx = document.getElementById('resourceStatsChart').getContext('2d');
       charts.resourceStats = new Chart(resourceCtx, {
         type: 'radar',
         data: {
           labels: ['平均仙玉', '平均灵石', '平均修为'],
           datasets: [{
             label: '平均资源',
             data: [],
             backgroundColor: 'rgba(75, 192, 192, 0.2)',
             borderColor: 'rgba(75, 192, 192, 1)',
             borderWidth: 2,
             pointBackgroundColor: 'rgba(75, 192, 192, 1)'
           }]
         },
         options: {
           responsive: true,
           maintainAspectRatio: false,
           plugins: {
             legend: {
               display: false
             }
           },
           scales: {
             r: {
               beginAtZero: true
             }
           }
         }
       });

       // 活动统计图表
       const activityCtx = document.getElementById('activityStatsChart').getContext('2d');
       charts.activityStats = new Chart(activityCtx, {
         type: 'bar',
         data: {
           labels: ['系统邮件', '未读邮件', '古宝模板', '功法模板'],
           datasets: [{
             label: '数量',
             data: [],
             backgroundColor: [
               'rgba(255, 99, 132, 0.6)',
               'rgba(255, 159, 64, 0.6)',
               'rgba(255, 205, 86, 0.6)',
               'rgba(75, 192, 192, 0.6)'
             ],
             borderColor: [
               'rgba(255, 99, 132, 1)',
               'rgba(255, 159, 64, 1)',
               'rgba(255, 205, 86, 1)',
               'rgba(75, 192, 192, 1)'
             ],
             borderWidth: 1
           }]
         },
         options: {
           responsive: true,
           maintainAspectRatio: false,
           plugins: {
             legend: {
               display: false
             }
           },
           scales: {
             y: {
               beginAtZero: true,
               ticks: {
                 stepSize: 1
               }
             }
           }
         }
       });

       console.log('📊 图表初始化完成');
     }

     // 加载统计数据
     async function loadDashboardData() {
       try {
         console.log('📊 开始加载统计数据...');
         
         // 显示加载动画
         showLoadingSpinners();
         
         const response = await fetch('/api/dashboard/statistics');
         const result = await response.json();
         
         if (result.success) {
           updateDashboard(result.data);
           console.log('✅ 统计数据加载成功');
         } else {
           console.error('❌ 获取统计数据失败:', result.error);
         }
         
       } catch (error) {
         console.error('❌ 加载统计数据错误:', error);
       } finally {
         hideLoadingSpinners();
       }
     }

     // 更新仪表板数据
     function updateDashboard(data) {
       // 更新概览数据
       document.getElementById('totalPlayers').textContent = data.overview.totalPlayers || 0;
       document.getElementById('activePlayers').textContent = data.overview.activePlayers || 0;
       document.getElementById('newPlayers').textContent = data.overview.newPlayers || 0;
       document.getElementById('totalMails').textContent = data.activityStats.totalMails || 0;

       // 更新等级分布图表
       const levelLabels = data.levelDistribution.map(item => item.range);
       const levelData = data.levelDistribution.map(item => item.count);
       
       charts.levelDistribution.data.labels = levelLabels;
       charts.levelDistribution.data.datasets[0].data = levelData;
       charts.levelDistribution.update('none');

       // 更新境界分布图表
       const realmLabels = data.realmDistribution.map(item => item.realm);
       const realmData = data.realmDistribution.map(item => item.count);
       
       charts.realmDistribution.data.labels = realmLabels;
       charts.realmDistribution.data.datasets[0].data = realmData;
       charts.realmDistribution.update('none');

       // 更新资源统计图表
       const resourceData = [
         Math.log10(data.resourceStats.avgXianyu + 1) * 100,  // 对数缩放
         Math.log10(data.resourceStats.avgLingshi + 1) * 100,
         Math.log10(data.resourceStats.avgXiuwei + 1) * 100
       ];
       
       charts.resourceStats.data.datasets[0].data = resourceData;
       charts.resourceStats.update('none');

       // 更新活动统计图表
       const activityData = [
         data.activityStats.totalMails,
         data.activityStats.unreadMails,
         data.activityStats.totalTreasures,
         data.activityStats.totalSkills
       ];
       
       charts.activityStats.data.datasets[0].data = activityData;
       charts.activityStats.update('none');

       // 更新最后更新时间
       document.getElementById('lastUpdate').textContent = 
         '最后更新: ' + new Date(data.timestamp).toLocaleString('zh-CN');
     }

     // 显示加载动画
     function showLoadingSpinners() {
       const spinners = ['levelChartSpinner', 'realmChartSpinner', 'resourceChartSpinner', 'activityChartSpinner'];
       spinners.forEach(id => {
         const spinner = document.getElementById(id);
         if (spinner) spinner.style.display = 'block';
       });
     }

     // 隐藏加载动画
     function hideLoadingSpinners() {
       const spinners = ['levelChartSpinner', 'realmChartSpinner', 'resourceChartSpinner', 'activityChartSpinner'];
       spinners.forEach(id => {
         const spinner = document.getElementById(id);
         if (spinner) spinner.style.display = 'none';
       });
     }

     // 页面卸载时清理定时器
     window.addEventListener('beforeunload', function() {
       if (refreshInterval) {
         clearInterval(refreshInterval);
       }
     });
   </script>

</body>
</html> 