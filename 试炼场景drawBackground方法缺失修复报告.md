# 试炼场景drawBackground方法缺失修复报告

## 问题描述
用户点击试炼按钮后遇到运行时错误：
```
TypeError: this.drawBackground is not a function
    at TrialScene.drawScene (TrialScene.js? [sm]:606)
    at TrialScene.render (BaseScene.js? [sm]:160)
    at SceneManager.render (SceneManager.js? [sm]:594)
    at Function.loop (game.js? [sm]:949)
```

## 问题分析
1. **主要问题**：TrialScene在drawScene方法中调用了`this.drawBackground()`，但该类中没有实现这个方法
2. **扩展问题**：通过代码搜索发现，BackpackScene、StoryScene、IdleScene也存在同样的问题
3. **根本原因**：这些场景在重构过程中缺少了标准的drawBackground方法实现

## 修复方案

### 1. TrialScene.js修复
为TrialScene添加了完整的背景绘制和页面结构方法：

#### 添加的方法：
- `drawBackground()` - 绘制背景图或默认渐变背景
- `drawDefaultBackground()` - 绘制默认渐变色背景
- `drawHeader()` - 绘制顶部导航栏，包含安全区域和试炼标题
- `drawTrialInfo()` - 根据当前试炼模式绘制不同的信息内容

#### 特性：
- **背景处理**：优先使用mainBg资源，失败时使用渐变色背景
- **安全区域**：添加40px黑色安全区域防止被摄像头遮挡
- **试炼信息**：根据trialMode显示不同内容（main/detail/demon/sword）
- **玩家信息**：显示玩家昵称、等级和仙玉资源

### 2. BackpackScene.js修复
为背包场景添加了背景绘制方法：

#### 添加的方法：
- `drawBackground()` - 标准背景绘制逻辑
- `drawDefaultBackground()` - 默认渐变背景

### 3. StoryScene.js修复
为主线剧情场景添加了完整的页面结构方法：

#### 添加的方法：
- `drawBackground()` - 背景绘制
- `drawDefaultBackground()` - 默认背景
- `drawHeader()` - 顶部导航栏，显示"主线剧情"标题
- `drawChapterSelection()` - 章节选择区域信息
- `drawLevelInfo()` - 关卡信息显示

### 4. IdleScene.js修复
为闲置修炼场景添加了完整的页面结构方法：

#### 添加的方法：
- `drawBackground()` - 背景绘制
- `drawDefaultBackground()` - 默认背景
- `drawHeader()` - 顶部导航栏，显示"闲置修炼"标题
- `drawIdleInfo()` - 闲置修炼信息，包括当前地点和奖励预览

## 技术实现细节

### 背景绘制逻辑
```javascript
drawBackground() {
  // 优先使用背景图资源
  if (this.resources && this.resources.mainBg) {
    try {
      this.ctx.drawImage(
        this.resources.mainBg,
        0, 0,
        this.screenWidth,
        this.screenHeight
      );
    } catch (error) {
      console.error('绘制背景图失败', error);
      this.drawDefaultBackground();
    }
  } else {
    // 使用渐变色背景
    this.drawDefaultBackground();
  }
}
```

### 默认背景渐变
```javascript
drawDefaultBackground() {
  const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
  gradient.addColorStop(0, '#1a2a6c');
  gradient.addColorStop(0.5, '#b21f1f');
  gradient.addColorStop(1, '#fdbb2d');

  this.ctx.fillStyle = gradient;
  this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
}
```

### 安全区域设计
```javascript
// 绘制40px黑色安全区域
this.ctx.fillStyle = '#000000';
this.ctx.fillRect(0, 0, this.screenWidth, safeAreaHeight);

// 绘制半透明导航栏背景
this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
this.ctx.fillRect(0, safeAreaHeight, this.screenWidth, headerHeight - safeAreaHeight);
```

## 测试验证
1. **TrialScene测试**：点击试炼按钮能正常进入，显示试炼界面
2. **BackpackScene测试**：背包界面正常显示背景
3. **StoryScene测试**：主线剧情界面正常显示
4. **IdleScene测试**：闲置修炼界面正常显示

## 修复结果
- ✅ 解决了TrialScene的drawBackground方法缺失问题
- ✅ 统一了所有场景的背景绘制逻辑
- ✅ 添加了完整的页面结构和信息显示
- ✅ 保持了与其他场景一致的UI风格
- ✅ 所有场景都能正常显示，不再报错

## 代码质量改进
1. **标准化**：所有场景现在都有统一的背景绘制逻辑
2. **容错性**：添加了资源加载失败的容错处理
3. **可维护性**：方法结构清晰，便于后续维护
4. **用户体验**：添加了安全区域和合理的信息布局

## 注意事项
- 所有修改的场景都保持了原有的功能逻辑不变
- 新增的方法遵循了项目现有的代码风格
- 背景绘制逻辑与MainScene保持一致
- 安全区域设计考虑了真机环境的摄像头遮挡问题

修复完成后，用户可以正常使用试炼功能以及其他所有场景，不再出现drawBackground方法缺失的错误。 