// 云函数入口文件
const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
  env: 'cloud1-9gzbxxbff827656f'
});

/**
 * 获取用户OpenID的云函数
 * 这是推荐的安全方式，由微信云开发自动注入可信的openid
 * 无需处理敏感凭证（如AppSecret）
 */
exports.main = async (event, context) => {
  try {
    // 获取微信调用上下文，包含用户的openid等信息
    const wxContext = cloud.getWXContext();
    
    // 记录调用日志（便于调试）
    console.log('getOpenId云函数被调用', {
      timestamp: new Date().toISOString(),
      openid: wxContext.OPENID ? '已获取' : '未获取',
      appid: wxContext.APPID ? '已获取' : '未获取'
    });

    // 检查是否成功获取到openid
    if (!wxContext.OPENID) {
      console.error('未能获取到用户openid');
      return {
        success: false,
        error: '未能获取到用户openid，请确保用户已授权',
        code: 'NO_OPENID'
      };
    }

    // 返回成功结果
    return {
      success: true,
      openid: wxContext.OPENID,
      appid: wxContext.APPID,
      timestamp: Date.now(),
      message: '成功获取用户openid'
    };

  } catch (error) {
    // 错误处理
    console.error('getOpenId云函数执行出错:', error);
    
    return {
      success: false,
      error: error.message || '云函数执行出错',
      code: 'FUNCTION_ERROR',
      timestamp: Date.now()
    };
  }
};
