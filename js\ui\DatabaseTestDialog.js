/**
 * 数据库测试对话框
 * 用于测试数据库的CRUD操作
 */
class DatabaseTestDialog {
  constructor(ctx, screenWidth, screenHeight, onClose) {
    this.ctx = ctx;
    this.screenWidth = screenWidth;
    this.screenHeight = screenHeight;
    this.onClose = onClose;
    
    // 对话框尺寸
    this.dialogWidth = Math.min(screenWidth - 40, 600);
    this.dialogHeight = Math.min(screenHeight - 80, 700);
    this.dialogX = (screenWidth - this.dialogWidth) / 2;
    this.dialogY = (screenHeight - this.dialogHeight) / 2;
    
    // 当前选中的表（支持多选）
    this.selectedTables = new Set(['sword_bones']);
    
    // 操作类型
    this.selectedOperation = 'get';
    
    // 输入字段
    this.inputFields = {
      _id: '',
      level: '1',
      rank: '1',
      envType: 'prod'
    };
    
    // 测试结果
    this.testResult = '';
    this.isLoading = false;
    this.batchResults = []; // 批量执行结果
    this.currentBatchIndex = 0; // 当前执行的表索引
    this.batchProgress = 0; // 批量执行进度 (0-100)
    
    // 可用的数据表
    this.availableTables = [
      'players', 'player_res', 'player_treasures',
      'player_skill', 'player_items', 'sword_hearts', 'sword_bones',
      'player_dongf', 'player_arena', 'player_idle', 'p_skill_cul',
      'recharge_rec', 'gacha_record', 'battle_recor',
      'mail_temp', 'player_mails', 'daily_tasks', 'activity_par',
      'game_configs'
    ];
    
    // 操作类型
    this.operations = [
      { key: 'get', name: '查询 (GET)' },
      { key: 'create', name: '创建 (CREATE)' },
      { key: 'update', name: '更新 (UPDATE)' },
      { key: 'delete', name: '删除 (DELETE)' },
      { key: 'list', name: '列表 (LIST)' }
    ];
    
    // 滚动位置
    this.scrollY = 0;
    this.maxScrollY = 0;
    
    // 按钮位置（相对于内容区域顶部）
    this.quickExecuteButtonY = undefined;
    this.detailedExecuteButtonY = undefined;
    
    // 预设的测试数据
    this.testDataTemplates = {
      sword_bones: {
        level: 1,
        rank: 1,
        total_attributes: { attack: 100, defense: 50 },
        upgrade_materials_used: { iron: 10, gold: 5 }
      },
      players: {
        nickname: '测试玩家',
        level: 10,
        exp: 1000,
        power: 500,
        cultivation_realm: '筑基期一层'
      },
      player_res: {
        xianyu: 2000,
        lingshi: 3000,
        sword_intent: 100
      }
    };
    
    console.log('数据库测试对话框已创建');
  }
  
  // 检查点是否在对话框内（BaseScene要求的方法）
  isPointInside(x, y) {
    // 对话框覆盖整个屏幕，拦截所有触摸事件
    return true;
  }
  
  // 渲染方法（BaseScene要求的方法）
  render() {
    this.draw();
  }
  
  // BaseScene要求的触摸事件方法（别名）
  onTouchStart(x, y) {
    return this.handleTouchStart(x, y);
  }
  
  onTouchMove(x, y) {
    return this.handleTouchMove(x, y);
  }
  
  onTouchEnd(x, y) {
    return this.handleTouchEnd(x, y);
  }
  
  // 绘制对话框
  draw() {
    // 绘制遮罩层
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
    
    // 绘制对话框背景
    this.ctx.fillStyle = '#2d3748';
    this.ctx.fillRect(this.dialogX, this.dialogY, this.dialogWidth, this.dialogHeight);
    
    // 绘制边框
    this.ctx.strokeStyle = '#4a5568';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(this.dialogX, this.dialogY, this.dialogWidth, this.dialogHeight);
    
    // 绘制标题栏
    this.ctx.fillStyle = '#1a202c';
    this.ctx.fillRect(this.dialogX, this.dialogY, this.dialogWidth, 50);
    
    // 绘制标题文字
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = 'bold 18px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText('数据库测试工具', this.dialogX + 20, this.dialogY + 25);
    
    // 绘制关闭按钮
    this.ctx.fillStyle = '#e53e3e';
    this.ctx.fillRect(this.dialogX + this.dialogWidth - 40, this.dialogY + 10, 30, 30);
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = 'bold 16px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('×', this.dialogX + this.dialogWidth - 25, this.dialogY + 25);
    
    // 绘制内容区域
    this.drawContent();
  }
  
  // 绘制内容
  drawContent() {
    const contentY = this.dialogY + 60;
    const contentHeight = this.dialogHeight - 70;
    
    // 设置裁剪区域
    this.ctx.save();
    this.ctx.beginPath();
    this.ctx.rect(this.dialogX, contentY, this.dialogWidth, contentHeight);
    this.ctx.clip();
    
    let currentY = contentY - this.scrollY;
    const leftMargin = this.dialogX + 20;
    const rightMargin = this.dialogX + this.dialogWidth - 20;
    const itemHeight = 35;
    
    // 1. 数据表选择
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = 'bold 14px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`选择数据表 (已选择 ${this.selectedTables.size} 个):`, leftMargin, currentY + 20);
    
    // 全选/取消全选按钮
    const selectAllButtonWidth = 80;
    this.ctx.fillStyle = this.selectedTables.size === this.availableTables.length ? '#38a169' : '#4a5568';
    this.ctx.fillRect(leftMargin + 250, currentY + 5, selectAllButtonWidth, 25);
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = '12px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(this.selectedTables.size === this.availableTables.length ? '取消全选' : '全选', 
                      leftMargin + 250 + selectAllButtonWidth / 2, currentY + 17);
    
    // 保存全选按钮位置
    this.selectAllButtonY = currentY + 5;
    currentY += 35;
    
    // 绘制表选择按钮
    const tableButtonWidth = 100;
    const tablesPerRow = Math.floor((this.dialogWidth - 40) / (tableButtonWidth + 10));
    
    for (let i = 0; i < this.availableTables.length; i++) {
      const table = this.availableTables[i];
      const row = Math.floor(i / tablesPerRow);
      const col = i % tablesPerRow;
      const buttonX = leftMargin + col * (tableButtonWidth + 10);
      const buttonY = currentY + row * (itemHeight + 5);
      
      // 按钮背景（多选状态）
      const isSelected = this.selectedTables.has(table);
      this.ctx.fillStyle = isSelected ? '#3182ce' : '#4a5568';
      this.ctx.fillRect(buttonX, buttonY, tableButtonWidth, itemHeight - 5);
      
      // 选中标记
      if (isSelected) {
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = 'bold 12px Arial';
        this.ctx.textAlign = 'right';
        this.ctx.textBaseline = 'top';
        this.ctx.fillText('✓', buttonX + tableButtonWidth - 5, buttonY + 3);
      }
      
      // 按钮文字
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = '12px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText(table, buttonX + tableButtonWidth / 2, buttonY + (itemHeight - 5) / 2);
    }
    
    currentY += Math.ceil(this.availableTables.length / tablesPerRow) * (itemHeight + 5) + 20;
    
    // 2. 操作类型选择
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = 'bold 14px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.fillText('选择操作:', leftMargin, currentY + 20);
    currentY += 30;
    
    // 绘制操作选择按钮
    const operationButtonWidth = 120;
    const operationsPerRow = Math.floor((this.dialogWidth - 40) / (operationButtonWidth + 10));
    
    for (let i = 0; i < this.operations.length; i++) {
      const operation = this.operations[i];
      const row = Math.floor(i / operationsPerRow);
      const col = i % operationsPerRow;
      const buttonX = leftMargin + col * (operationButtonWidth + 10);
      const buttonY = currentY + row * (itemHeight + 5);
      
      // 按钮背景
      this.ctx.fillStyle = operation.key === this.selectedOperation ? '#38a169' : '#4a5568';
      this.ctx.fillRect(buttonX, buttonY, operationButtonWidth, itemHeight - 5);
      
      // 按钮文字
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = '12px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText(operation.name, buttonX + operationButtonWidth / 2, buttonY + (itemHeight - 5) / 2);
    }
    
    currentY += Math.ceil(this.operations.length / operationsPerRow) * (itemHeight + 5) + 10;
    
    // 3. 快速执行按钮（如果已选择表和操作）
    if (this.selectedTables.size > 0 && this.selectedOperation) {
      const buttonText = this.isLoading ? 
        `执行中... (${this.currentBatchIndex}/${this.selectedTables.size})` : 
        `🚀 批量执行 (${this.selectedTables.size}个表)`;
      
      this.ctx.fillStyle = this.isLoading ? '#4a5568' : '#e53e3e';
      this.ctx.fillRect(leftMargin, currentY, 200, 35);
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = 'bold 14px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText(buttonText, leftMargin + 100, currentY + 17);
      
      // 进度条（如果正在执行）
      if (this.isLoading && this.selectedTables.size > 1) {
        const progressBarY = currentY + 40;
        const progressBarWidth = 200;
        const progressBarHeight = 6;
        
        // 进度条背景
        this.ctx.fillStyle = '#4a5568';
        this.ctx.fillRect(leftMargin, progressBarY, progressBarWidth, progressBarHeight);
        
        // 进度条填充
        const progressWidth = (this.batchProgress / 100) * progressBarWidth;
        this.ctx.fillStyle = '#38a169';
        this.ctx.fillRect(leftMargin, progressBarY, progressWidth, progressBarHeight);
        
        // 进度文字
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(`${Math.round(this.batchProgress)}%`, leftMargin + progressBarWidth / 2, progressBarY + 20);
        
        currentY += 30;
      }
      
      // 保存快速执行按钮位置（相对于内容区域顶部的位置）
      this.quickExecuteButtonY = currentY - (contentY - this.scrollY);
      currentY += 45;
    }
    
    // 4. 参数输入区域
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = 'bold 14px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.fillText('参数设置:', leftMargin, currentY + 20);
    currentY += 30;
    
    // 环境类型选择
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = '12px Arial';
    this.ctx.fillText('环境类型:', leftMargin, currentY + 20);
    
    // prod环境按钮
    this.ctx.fillStyle = this.inputFields.envType === 'prod' ? '#3182ce' : '#4a5568';
    this.ctx.fillRect(leftMargin + 80, currentY + 5, 60, 25);
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('正式环境', leftMargin + 110, currentY + 17);
    
    // pre环境按钮
    this.ctx.fillStyle = this.inputFields.envType === 'pre' ? '#3182ce' : '#4a5568';
    this.ctx.fillRect(leftMargin + 150, currentY + 5, 60, 25);
    this.ctx.fillStyle = '#ffffff';
    this.ctx.fillText('体验环境', leftMargin + 180, currentY + 17);
    
    currentY += 40;
    
    // 根据操作类型显示不同的输入字段
    if (this.selectedOperation === 'get' || this.selectedOperation === 'update' || this.selectedOperation === 'delete') {
      // 需要_id字段
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = '12px Arial';
      this.ctx.textAlign = 'left';
      this.ctx.fillText('记录ID (_id):', leftMargin, currentY + 20);
      
      // 输入框
      this.ctx.fillStyle = '#1a202c';
      this.ctx.fillRect(leftMargin + 100, currentY + 5, 200, 25);
      this.ctx.strokeStyle = '#4a5568';
      this.ctx.strokeRect(leftMargin + 100, currentY + 5, 200, 25);
      
      // 输入文字
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = '12px Arial';
      this.ctx.textAlign = 'left';
      this.ctx.fillText(this.inputFields._id || '留空查询所有', leftMargin + 105, currentY + 17);
      
      currentY += 35;
    }
    
    if (this.selectedOperation === 'create' || this.selectedOperation === 'update') {
      // 显示数据输入区域
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = '12px Arial';
      this.ctx.textAlign = 'left';
      this.ctx.fillText('数据字段:', leftMargin, currentY + 20);
      currentY += 25;
      
      // 根据选中的表显示相应的字段
      const template = this.testDataTemplates[this.selectedTable];
      if (template) {
        Object.keys(template).forEach(key => {
          this.ctx.fillStyle = '#ffffff';
          this.ctx.font = '12px Arial';
          this.ctx.textAlign = 'left';
          this.ctx.fillText(`${key}:`, leftMargin, currentY + 20);
          
          // 输入框
          this.ctx.fillStyle = '#1a202c';
          this.ctx.fillRect(leftMargin + 100, currentY + 5, 200, 25);
          this.ctx.strokeStyle = '#4a5568';
          this.ctx.strokeRect(leftMargin + 100, currentY + 5, 200, 25);
          
          // 输入文字
          this.ctx.fillStyle = '#ffffff';
          this.ctx.font = '12px Arial';
          this.ctx.textAlign = 'left';
          const value = this.inputFields[key] || (typeof template[key] === 'object' ? JSON.stringify(template[key]) : template[key].toString());
          this.ctx.fillText(value, leftMargin + 105, currentY + 17);
          
          currentY += 35;
        });
      }
      
      // 使用模板数据按钮
      this.ctx.fillStyle = '#805ad5';
      this.ctx.fillRect(leftMargin, currentY, 120, 30);
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = '12px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('使用模板数据', leftMargin + 60, currentY + 15);
      
      currentY += 40;
    }
    
    // 5. 详细执行按钮
    this.ctx.fillStyle = this.isLoading ? '#4a5568' : '#38a169';
    this.ctx.fillRect(leftMargin, currentY, 120, 35);
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = 'bold 14px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(this.isLoading ? '执行中...' : '📝 详细执行', leftMargin + 60, currentY + 17);
    
    // 保存详细执行按钮位置（相对于内容区域顶部的位置）
    this.detailedExecuteButtonY = currentY - (contentY - this.scrollY);
    currentY += 45;
    
    // 6. 结果显示区域
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = 'bold 14px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.textBaseline = 'top';
    
    const hasResults = this.testResult || this.batchResults.length > 0;
    const resultTitle = this.batchResults.length > 0 ? 
      `批量执行结果 (${this.batchResults.length}/${this.selectedTables.size}):` : 
      '测试结果:';
    this.ctx.fillText(resultTitle, leftMargin, currentY + 20);
    currentY += 30;
    
    // 结果背景
    const resultHeight = this.batchResults.length > 0 ? 250 : 150;
    this.ctx.fillStyle = '#1a202c';
    this.ctx.fillRect(leftMargin, currentY, this.dialogWidth - 40, resultHeight);
    this.ctx.strokeStyle = '#4a5568';
    this.ctx.strokeRect(leftMargin, currentY, this.dialogWidth - 40, resultHeight);
    
    // 结果文字
    if (this.batchResults.length > 0) {
      // 显示批量执行结果
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = '11px monospace';
      this.ctx.textAlign = 'left';
      this.ctx.textBaseline = 'top';
      
      let lineIndex = 0;
      const maxLines = Math.floor((resultHeight - 20) / 12);
      
      this.batchResults.forEach((result, index) => {
        if (lineIndex >= maxLines) return;
        
        // 表名标题
        this.ctx.fillStyle = result.success ? '#68d391' : '#fc8181';
        this.ctx.fillText(`${index + 1}. ${result.table}: ${result.success ? '✓' : '✗'}`, 
                          leftMargin + 10, currentY + 10 + lineIndex * 12);
        lineIndex++;
        
        if (lineIndex >= maxLines) return;
        
        // 结果详情
        this.ctx.fillStyle = '#e2e8f0';
        const message = result.message || (result.success ? '操作成功' : '操作失败');
        this.ctx.fillText(`   ${message.substring(0, 70)}`, 
                          leftMargin + 10, currentY + 10 + lineIndex * 12);
        lineIndex++;
        
        if (lineIndex < maxLines && index < this.batchResults.length - 1) {
          lineIndex++; // 空行分隔
        }
      });
      
      // 如果还有正在执行的，显示当前状态
      if (this.isLoading && this.currentBatchIndex < this.selectedTables.size) {
        if (lineIndex < maxLines) {
          this.ctx.fillStyle = '#fbb6ce';
          const currentTable = Array.from(this.selectedTables)[this.currentBatchIndex];
          this.ctx.fillText(`${this.currentBatchIndex + 1}. ${currentTable}: 执行中...`, 
                            leftMargin + 10, currentY + 10 + lineIndex * 12);
        }
      }
    } else if (this.testResult) {
      // 显示单次执行结果
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = '11px monospace';
      this.ctx.textAlign = 'left';
      this.ctx.textBaseline = 'top';
      
      const lines = this.testResult.split('\n');
      for (let i = 0; i < lines.length && i < 12; i++) {
        this.ctx.fillText(lines[i].substring(0, 80), leftMargin + 10, currentY + 10 + i * 12);
      }
    }
    
    currentY += resultHeight + 20;
    
    // 更新最大滚动距离
    this.maxScrollY = Math.max(0, currentY - (contentY + contentHeight));
    
    this.ctx.restore();
  }
  
  // 处理触摸开始事件
  handleTouchStart(x, y) {
    // 记录触摸开始位置，用于touchend时验证
    this.touchStartX = x;
    this.touchStartY = y;
    
    // 检查是否在对话框内
    if (x >= this.dialogX && x <= this.dialogX + this.dialogWidth &&
        y >= this.dialogY && y <= this.dialogY + this.dialogHeight) {
      return true;
    }
    
    return false;
  }
  
  // 处理触摸移动事件
  handleTouchMove(x, y) {
    // 处理滚动
    return true;
  }
  
  // 处理触摸结束事件
  handleTouchEnd(x, y) {
    // 检查触摸位置是否与开始位置接近（防止滑动误触）
    if (this.touchStartX && this.touchStartY) {
      const deltaX = Math.abs(x - this.touchStartX);
      const deltaY = Math.abs(y - this.touchStartY);
      
      // 如果移动距离太大，认为是滑动而不是点击
      if (deltaX > 10 || deltaY > 10) {
        return true;
      }
    }
    
    console.log('DatabaseTestDialog touchend处理:', x, y);
    
    // 检查是否点击关闭按钮
    if (x >= this.dialogX + this.dialogWidth - 40 && x <= this.dialogX + this.dialogWidth - 10 &&
        y >= this.dialogY + 10 && y <= this.dialogY + 40) {
      console.log('点击关闭按钮');
      this.onClose();
      return true;
    }
    
    // 检查是否在对话框内
    if (x >= this.dialogX && x <= this.dialogX + this.dialogWidth &&
        y >= this.dialogY && y <= this.dialogY + this.dialogHeight) {
      
      const contentY = this.dialogY + 60;
      let currentY = contentY - this.scrollY;
      const leftMargin = this.dialogX + 20;
      const itemHeight = 35;
      
      // 跳过标题
      currentY += 35; // 调整为新的标题高度
      
      // 检查全选/取消全选按钮
      const selectAllButtonWidth = 80;
      if (x >= leftMargin + 250 && x <= leftMargin + 250 + selectAllButtonWidth &&
          y >= currentY && y <= currentY + 25) {
        console.log('点击全选/取消全选按钮');
        if (this.selectedTables.size === this.availableTables.length) {
          // 取消全选
          this.selectedTables.clear();
        } else {
          // 全选
          this.selectedTables = new Set(this.availableTables);
        }
        this.testResult = '';
        this.batchResults = [];
        return true;
      }
      
      currentY += 35; // 全选按钮后的间距
      
      // 检查数据表选择（支持多选）
      const tableButtonWidth = 100;
      const tablesPerRow = Math.floor((this.dialogWidth - 40) / (tableButtonWidth + 10));
      const tableRows = Math.ceil(this.availableTables.length / tablesPerRow);
      
      if (y >= currentY && y <= currentY + tableRows * (itemHeight + 5)) {
        for (let i = 0; i < this.availableTables.length; i++) {
          const row = Math.floor(i / tablesPerRow);
          const col = i % tablesPerRow;
          const buttonX = leftMargin + col * (tableButtonWidth + 10);
          const buttonY = currentY + row * (itemHeight + 5);
          
          if (x >= buttonX && x <= buttonX + tableButtonWidth &&
              y >= buttonY && y <= buttonY + itemHeight - 5) {
            const table = this.availableTables[i];
            console.log('切换数据表选择状态:', table);
            
            // 切换选择状态
            if (this.selectedTables.has(table)) {
              this.selectedTables.delete(table);
            } else {
              this.selectedTables.add(table);
            }
            
            this.testResult = '';
            this.batchResults = [];
            return true;
          }
        }
      }
      
      currentY += tableRows * (itemHeight + 5) + 50;
      
      // 检查操作类型选择
      const operationButtonWidth = 120;
      const operationsPerRow = Math.floor((this.dialogWidth - 40) / (operationButtonWidth + 10));
      const operationRows = Math.ceil(this.operations.length / operationsPerRow);
      
      if (y >= currentY && y <= currentY + operationRows * (itemHeight + 5)) {
        for (let i = 0; i < this.operations.length; i++) {
          const operation = this.operations[i];
          const row = Math.floor(i / operationsPerRow);
          const col = i % operationsPerRow;
          const buttonX = leftMargin + col * (operationButtonWidth + 10);
          const buttonY = currentY + row * (itemHeight + 5);
          
          if (x >= buttonX && x <= buttonX + operationButtonWidth &&
              y >= buttonY && y <= buttonY + itemHeight - 5) {
            console.log('选择操作类型:', operation.key);
            this.selectedOperation = operation.key;
            this.testResult = '';
            return true;
          }
        }
      }
      
      currentY += operationRows * (itemHeight + 5) + 50;
      
      // 检查环境类型选择
      if (y >= currentY + 5 && y <= currentY + 30) {
        // prod环境按钮
        if (x >= leftMargin + 80 && x <= leftMargin + 140) {
          console.log('选择prod环境');
          this.inputFields.envType = 'prod';
          return true;
        }
        // pre环境按钮
        if (x >= leftMargin + 150 && x <= leftMargin + 210) {
          console.log('选择pre环境');
          this.inputFields.envType = 'pre';
          return true;
        }
      }
      
      currentY += 40;
      
      // 检查快速执行按钮
      if (this.selectedTables.size > 0 && this.selectedOperation && this.quickExecuteButtonY !== undefined) {
        const contentY = this.dialogY + 60;
        const quickButtonY = contentY + this.quickExecuteButtonY - this.scrollY;
        console.log(`批量执行按钮检测: 按钮Y=${quickButtonY}, 点击Y=${y}, 按钮区域=${quickButtonY}-${quickButtonY + 35}`);
        if (x >= leftMargin && x <= leftMargin + 200 &&
            y >= quickButtonY && y <= quickButtonY + 35) {
          console.log('点击批量执行按钮');
          if (!this.isLoading) {
            this.executeBatchTest();
          }
          return true;
        }
      }
      
      // 检查使用模板数据按钮
      const firstSelectedTable = Array.from(this.selectedTables)[0];
      if ((this.selectedOperation === 'create' || this.selectedOperation === 'update') &&
          firstSelectedTable && this.testDataTemplates[firstSelectedTable]) {
        const template = this.testDataTemplates[firstSelectedTable];
        const templateButtonY = currentY + Object.keys(template).length * 35;
        
        if (x >= leftMargin && x <= leftMargin + 120 &&
            y >= templateButtonY && y <= templateButtonY + 30) {
          console.log('点击使用模板数据按钮');
          this.loadTemplateData();
          return true;
        }
      }
      
      // 检查详细执行按钮
      if (this.detailedExecuteButtonY !== undefined) {
        const contentY = this.dialogY + 60;
        const detailedButtonY = contentY + this.detailedExecuteButtonY - this.scrollY;
        console.log(`详细执行按钮检测: 按钮Y=${detailedButtonY}, 点击Y=${y}, 按钮区域=${detailedButtonY}-${detailedButtonY + 35}`);
        if (x >= leftMargin && x <= leftMargin + 120 &&
            y >= detailedButtonY && y <= detailedButtonY + 35) {
          console.log('点击详细执行按钮');
          if (!this.isLoading) {
            this.executeTest();
          }
          return true;
        }
      }
      
      return true;
    }
    
    // 点击对话框外部，关闭对话框
    console.log('点击对话框外部，关闭对话框');
    this.onClose();
    return true;
  }
  
  // 获取执行按钮的Y坐标
  getExecuteButtonY() {
    const contentY = this.dialogY + 60;
    let currentY = contentY - this.scrollY + 50; // 标题
    
    // 数据表选择区域
    const tablesPerRow = Math.floor((this.dialogWidth - 40) / 110);
    const tableRows = Math.ceil(this.availableTables.length / tablesPerRow);
    currentY += tableRows * 40 + 50;
    
    // 操作类型选择区域
    const operationsPerRow = Math.floor((this.dialogWidth - 40) / 130);
    const operationRows = Math.ceil(this.operations.length / operationsPerRow);
    currentY += operationRows * 40 + 50;
    
    // 参数设置区域
    currentY += 70; // 环境选择
    
    if (this.selectedOperation === 'get' || this.selectedOperation === 'update' || this.selectedOperation === 'delete') {
      currentY += 35; // _id输入
    }
    
    if (this.selectedOperation === 'create' || this.selectedOperation === 'update') {
      const firstSelectedTable = Array.from(this.selectedTables)[0];
      const template = this.testDataTemplates[firstSelectedTable];
      if (template) {
        currentY += Object.keys(template).length * 35 + 40; // 字段输入 + 模板按钮
      }
    }
    
    return currentY;
  }
  
  // 加载模板数据
  loadTemplateData(tableName = null) {
    const targetTable = tableName || Array.from(this.selectedTables)[0];
    const template = this.testDataTemplates[targetTable];
    if (template) {
      Object.keys(template).forEach(key => {
        this.inputFields[key] = typeof template[key] === 'object' 
          ? JSON.stringify(template[key]) 
          : template[key].toString();
      });
      console.log(`已加载表 ${targetTable} 的模板数据:`, template);
    }
  }
  
  // 批量执行测试
  async executeBatchTest() {
    if (this.isLoading) return;
    
    const selectedTablesArray = Array.from(this.selectedTables);
    console.log(`批量执行${this.selectedOperation}操作，表：`, selectedTablesArray);
    
    this.isLoading = true;
    this.batchResults = [];
    this.currentBatchIndex = 0;
    this.batchProgress = 0;
    this.testResult = '';
    
    try {
      for (let i = 0; i < selectedTablesArray.length; i++) {
        const table = selectedTablesArray[i];
        this.currentBatchIndex = i;
        this.batchProgress = (i / selectedTablesArray.length) * 100;
        
        console.log(`执行第 ${i + 1}/${selectedTablesArray.length} 个表: ${table}`);
        
        try {
          const result = await this.executeSingleTableTest(table);
          this.batchResults.push({
            table: table,
            success: true,
            message: result.message,
            data: result.data
          });
        } catch (error) {
          console.error(`表 ${table} 执行失败:`, error);
          this.batchResults.push({
            table: table,
            success: false,
            message: error.message,
            error: error
          });
        }
        
        // 更新进度
        this.batchProgress = ((i + 1) / selectedTablesArray.length) * 100;
        
        // 短暂延迟，避免请求过快
        if (i < selectedTablesArray.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }
      
      console.log('批量执行完成，结果:', this.batchResults);
      
      // 生成汇总结果
      const successCount = this.batchResults.filter(r => r.success).length;
      const failCount = this.batchResults.length - successCount;
      
      this.testResult = `批量执行完成！\n成功: ${successCount} 个\n失败: ${failCount} 个\n总计: ${this.batchResults.length} 个表`;
      
    } catch (error) {
      console.error('批量执行失败:', error);
      this.testResult = `批量执行失败: ${error.message}`;
    } finally {
      this.isLoading = false;
      this.currentBatchIndex = 0;
      this.batchProgress = 100;
    }
  }
  
  // 执行单个表的测试
  async executeSingleTableTest(tableName) {
    console.log(`执行${this.selectedOperation}操作，表：${tableName}`);
    
    // 准备请求参数
    const requestData = {
      action: this.selectedOperation,
      tableName: tableName,
      envType: this.inputFields.envType
    };
    
    // 根据操作类型添加参数
    if (this.selectedOperation === 'get' || this.selectedOperation === 'update' || this.selectedOperation === 'delete') {
      if (this.inputFields._id) {
        requestData._id = this.inputFields._id;
      }
    }
    
    if (this.selectedOperation === 'create' || this.selectedOperation === 'update') {
      // 构建数据对象
      const data = {};
      const template = this.testDataTemplates[tableName];
      if (template) {
        Object.keys(template).forEach(key => {
          if (this.inputFields[key]) {
            try {
              // 尝试解析JSON，如果失败则作为字符串处理
              if (this.inputFields[key].startsWith('{') || this.inputFields[key].startsWith('[')) {
                data[key] = JSON.parse(this.inputFields[key]);
              } else {
                data[key] = isNaN(this.inputFields[key]) ? this.inputFields[key] : Number(this.inputFields[key]);
              }
            } catch (error) {
              data[key] = this.inputFields[key];
            }
          } else {
            // 使用模板默认值
            data[key] = template[key];
          }
        });
      }
      requestData.data = data;
    }
    
    console.log(`表 ${tableName} 请求参数:`, requestData);
    
    // 调用云函数
    const result = await wx.cloud.callFunction({
      name: 'databaseService',
      data: requestData
    });
    
    console.log(`表 ${tableName} 云函数返回结果:`, result);
    
    // 处理结果
    if (result.result) {
      if (result.result.success) {
        return {
          message: result.result.data.message || '操作成功',
          data: result.result.data
        };
      } else {
        throw new Error(result.result.error || '操作失败');
      }
    } else {
      throw new Error(result.errMsg || '云函数调用失败');
    }
  }
  
  // 快速执行测试（使用默认参数）- 保留兼容性
  async executeQuickTest() {
    // 如果选择了多个表，执行批量测试
    if (this.selectedTables.size > 1) {
      return this.executeBatchTest();
    }
    
    // 单表快速执行
    if (this.isLoading) return;
    
    const firstSelectedTable = Array.from(this.selectedTables)[0];
    console.log(`快速执行${this.selectedOperation}操作，表：${firstSelectedTable}`);
    
    // 对于create操作，自动加载模板数据
    if (this.selectedOperation === 'create' && this.testDataTemplates[firstSelectedTable]) {
      this.loadTemplateData(firstSelectedTable);
    }
    
    // 执行测试
    await this.executeTest();
  }
  
  // 执行测试
  async executeTest() {
    if (this.isLoading) return;
    
    const firstSelectedTable = Array.from(this.selectedTables)[0];
    if (!firstSelectedTable) {
      console.error('没有选择任何数据表');
      return;
    }
    
    this.isLoading = true;
    this.testResult = '正在执行测试...';
    
    try {
      console.log(`执行${this.selectedOperation}操作，表：${firstSelectedTable}`);
      
      // 准备请求参数
      const requestData = {
        action: this.selectedOperation,
        tableName: firstSelectedTable,
        envType: this.inputFields.envType
      };
      
      // 根据操作类型添加参数
      if (this.selectedOperation === 'get' || this.selectedOperation === 'update' || this.selectedOperation === 'delete') {
        if (this.inputFields._id) {
          requestData._id = this.inputFields._id;
        }
      }
      
      if (this.selectedOperation === 'create' || this.selectedOperation === 'update') {
        // 构建数据对象
        const data = {};
        const template = this.testDataTemplates[firstSelectedTable];
        if (template) {
          Object.keys(template).forEach(key => {
            if (this.inputFields[key]) {
              try {
                // 尝试解析JSON，如果失败则作为字符串处理
                if (this.inputFields[key].startsWith('{') || this.inputFields[key].startsWith('[')) {
                  data[key] = JSON.parse(this.inputFields[key]);
                } else {
                  data[key] = isNaN(this.inputFields[key]) ? this.inputFields[key] : Number(this.inputFields[key]);
                }
              } catch (error) {
                data[key] = this.inputFields[key];
              }
            }
          });
        }
        requestData.data = data;
      }
      
      console.log('请求参数:', requestData);
      
      // 调用云函数
      const result = await wx.cloud.callFunction({
        name: 'databaseService',
        data: requestData
      });
      
      console.log('云函数返回结果:', result);
      
      // 格式化结果显示
      let resultText = '';
      if (result.result) {
        if (result.result.success) {
          resultText = `✅ 操作成功\n`;
          resultText += `消息: ${result.result.data.message}\n`;
          
          if (result.result.data.records) {
            resultText += `记录数: ${result.result.data.count}\n`;
            if (result.result.data.records.length > 0) {
              resultText += `数据预览:\n${JSON.stringify(result.result.data.records[0], null, 2)}`;
            }
          } else if (result.result.data.id) {
            resultText += `创建的记录ID: ${result.result.data.id}\n`;
          } else if (result.result.data.count !== undefined) {
            resultText += `影响的记录数: ${result.result.data.count}\n`;
          }
        } else {
          resultText = `❌ 操作失败\n`;
          resultText += `错误: ${result.result.error}\n`;
          resultText += `错误码: ${result.result.code}`;
        }
      } else {
        resultText = `❌ 云函数调用失败\n`;
        resultText += `错误: ${result.errMsg || '未知错误'}`;
      }
      
      this.testResult = resultText;
      
    } catch (error) {
      console.error('测试执行失败:', error);
      this.testResult = `❌ 测试执行失败\n错误: ${error.message}`;
    } finally {
      this.isLoading = false;
    }
  }
}

export default DatabaseTestDialog; 