/**
 * 古宝升级云函数
 * 处理古宝等级提升逻辑
 */

const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

// 升级经验计算公式
function calculateUpgradeExp(currentLevel) {
  return Math.floor(100 * Math.pow(currentLevel, 1.5));
}

// 物品经验值配置
const itemExpValues = {
  'exp_stone_small': 50,    // 小经验石
  'exp_stone_medium': 150,  // 中经验石
  'exp_stone_large': 500,   // 大经验石
  'spirit_herb': 30,        // 灵草
  'crystal_ore': 80         // 晶石矿
};

exports.main = async (event, context) => {
  const { treasureRecordId, items } = event;
  const { OPENID } = cloud.getWXContext();

  try {
    console.log('古宝升级请求:', { treasureRecordId, items, openid: OPENID });

    // 1. 验证参数
    if (!treasureRecordId || !items || typeof items !== 'object') {
      return {
        success: false,
        error: 'INVALID_PARAMS',
        message: '参数错误'
      };
    }

    // 2. 获取玩家古宝当前状态
    const treasureResult = await db.collection('player_treasures')
      .doc(treasureRecordId)
      .get();

    if (!treasureResult.data) {
      return {
        success: false,
        error: 'TREASURE_NOT_FOUND',
        message: '古宝不存在'
      };
    }

    const treasure = treasureResult.data;

    // 验证古宝所属权
    if (treasure.player_id !== OPENID) {
      return {
        success: false,
        error: 'ACCESS_DENIED',
        message: '无权操作此古宝'
      };
    }

    // 检查等级上限
    if (treasure.level >= 100) {
      return {
        success: false,
        error: 'MAX_LEVEL_REACHED',
        message: '已达到最高等级'
      };
    }

    // 3. 验证消耗物品
    const itemUpdates = [];
    let totalExpGain = 0;

    for (const [itemId, count] of Object.entries(items)) {
      if (count <= 0) continue;

      // 查询玩家物品
      const itemResult = await db.collection('items')
        .where({
          _openid: OPENID,
          item_id: itemId
        })
        .get();

      if (itemResult.data.length === 0) {
        return {
          success: false,
          error: 'ITEM_NOT_FOUND',
          message: `物品${itemId}不存在`
        };
      }

      const item = itemResult.data[0];
      if (item.count < count) {
        return {
          success: false,
          error: 'INSUFFICIENT_ITEMS',
          message: `${item.name}数量不足，需要${count}个，当前${item.count}个`
        };
      }

      // 计算经验值增益
      const expValue = itemExpValues[itemId] || 50; // 默认经验值
      totalExpGain += expValue * count;

      // 记录物品更新操作
      itemUpdates.push({
        _id: item._id,
        newCount: item.count - count
      });
    }

    // 4. 计算升级结果
    let newExp = treasure.exp + totalExpGain;
    let newLevel = treasure.level;
    let levelUps = 0;

    while (newLevel < 100) {
      const requiredExp = calculateUpgradeExp(newLevel);
      if (newExp >= requiredExp) {
        newExp -= requiredExp;
        newLevel += 1;
        levelUps += 1;
      } else {
        break;
      }
    }

    // 5. 使用事务更新数据
    const transaction = await db.startTransaction();

    try {
      // 更新古宝数据
      await transaction.collection('player_treasures')
        .doc(treasureRecordId)
        .update({
          data: {
            level: newLevel,
            exp: newExp,
            updated_at: new Date()
          }
        });

      // 更新消耗的物品
      for (const update of itemUpdates) {
        if (update.newCount <= 0) {
          // 删除数量为0的物品
          await transaction.collection('items')
            .doc(update._id)
            .remove();
        } else {
          // 更新物品数量
          await transaction.collection('items')
            .doc(update._id)
            .update({
              data: {
                count: update.newCount,
                updated_at: new Date()
              }
            });
        }
      }

      // 记录升级日志
      await transaction.collection('player_logs').add({
        data: {
          player_id: OPENID,
          action: 'upgrade_treasure',
          details: {
            treasure_record_id: treasureRecordId,
            old_level: treasure.level,
            new_level: newLevel,
            level_ups: levelUps,
            exp_gained: totalExpGain,
            items_consumed: items
          },
          timestamp: new Date()
        }
      });

      // 提交事务
      await transaction.commit();

      console.log('古宝升级成功:', {
        treasureRecordId,
        oldLevel: treasure.level,
        newLevel,
        levelUps,
        expGained: totalExpGain
      });

      return {
        success: true,
        data: {
          oldLevel: treasure.level,
          newLevel: newLevel,
          levelUps: levelUps,
          currentExp: newExp,
          nextLevelExp: newLevel < 100 ? calculateUpgradeExp(newLevel) : 0,
          expGained: totalExpGain,
          message: levelUps > 0 ? `古宝升级成功！提升了${levelUps}级` : '经验值增加'
        }
      };

    } catch (transactionError) {
      // 回滚事务
      await transaction.rollback();
      throw transactionError;
    }

  } catch (error) {
    console.error('古宝升级失败:', error);
    return {
      success: false,
      error: 'SERVER_ERROR',
      message: '服务器错误，请稍后重试'
    };
  }
}; 