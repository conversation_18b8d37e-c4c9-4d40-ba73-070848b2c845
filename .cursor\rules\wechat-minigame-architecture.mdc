---
description: 
globs: 
alwaysApply: false
---
# WeChat Mini-Game Architecture Guide

## Project Overview
This is a cultivation-themed card battle WeChat mini-game called "修仙六道" built with the native WeChat mini-game framework.

## Core Architecture

### Entry Point
The main game entry is [game.js](mdc:game.js), which initializes the entire game system including:
- Canvas and rendering context
- Game state management
- Scene management
- Resource loading
- Cloud development integration

### Module Structure
```
js/
├── managers/          # Core management systems
├── scenes/           # Game scenes/pages
├── models/           # Data models and business logic
├── battle/           # Combat system
├── ui/               # UI components
├── utils/            # Utility functions
└── config/           # Configuration files
```

### Key Managers
- **GameStateManager** ([js/managers/GameStateManager.js](mdc:js/managers/GameStateManager.js)): Central data management
- **SceneManager** ([js/managers/SceneManager.js](mdc:js/managers/SceneManager.js)): Scene navigation and lifecycle
- **LoginManager** ([js/managers/LoginManager.js](mdc:js/managers/LoginManager.js)): User authentication and data sync
- **DatabaseManager** ([js/managers/DatabaseManager.js](mdc:js/managers/DatabaseManager.js)): Cloud database operations
- **AutoSaveManager** ([js/managers/AutoSaveManager.js](mdc:js/managers/AutoSaveManager.js)): Automatic data persistence

### Main Scenes
- **MainScene** ([js/scenes/MainScene.js](mdc:js/scenes/MainScene.js)): Primary game interface with navigation
- **CharacterDetailScene** ([js/scenes/CharacterDetailScene.js](mdc:js/scenes/CharacterDetailScene.js)): Character management and development
- **TrialScene** ([js/scenes/TrialScene.js](mdc:js/scenes/TrialScene.js)): Combat challenges and trials
- **BackpackScene** ([js/scenes/BackpackScene.js](mdc:js/scenes/BackpackScene.js)): Inventory management

## Development Patterns

### Scene Pattern
All scenes extend [BaseScene](mdc:js/scenes/BaseScene.js) and follow lifecycle methods:
- `onShow()`: Called when scene becomes active
- `onHide()`: Called when scene is hidden
- `updateScene()`: Game loop updates
- `drawScene()`: Rendering logic

### Data Models
Core game entities are defined as classes:
- [Character.js](mdc:js/models/Character.js): Player characters with attributes and skills
- [Equipment.js](mdc:js/models/Equipment.js): Weapons, armor, and accessories
- [Item.js](mdc:js/models/Item.js): Consumables and materials
- [Skill.js](mdc:js/models/Skill.js): Character abilities and techniques

### Cloud Integration
WeChat cloud development is configured in [project.config.json](mdc:project.config.json) with:
- Environment ID: cloud1-9gzbxxbff827656f
- Database collections for game data
- Cloud functions in [cloudfunctions/](mdc:cloudfunctions) directory

## Game Configuration
- [game.json](mdc:game.json): WeChat mini-game settings
- [project.config.json](mdc:project.config.json): Development tools configuration
- [database_design.md](mdc:database_design.md): Complete database schema documentation

