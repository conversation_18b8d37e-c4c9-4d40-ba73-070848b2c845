# 数据库表设计过时字段清理报告

## 清理概述

根据游戏系统更新，删除了与旧战斗系统和布阵系统相关的过时字段，简化数据库结构，适配单角色战斗模式。

## 修复内容

### 1. 数据库连接环境修正 ✅

**问题**: 云函数使用`envType: "pre"`连接体验环境而非正式数据库
**解决**: 修改为`envType: "prod"`连接正式生产环境

```javascript
// 修改前
const createResult = await models.players.create({
  data: completePlayerData,
  envType: "pre" // 使用体验环境
})

// 修改后
const createResult = await models.players.create({
  data: completePlayerData,
  envType: "prod" // 使用正式生产环境
})
```

### 2. 删除过时的战斗系统字段 ✅

#### characters表字段清理

**删除的字段**:
- `is_main_character` - 玩家只操作一个角色，必定是主角
- `position_in_formation` - 不再需要布阵系统
- `unlock_requirements` - 与旧仙友系统相关，已废弃
- `is_unlocked` - 与旧仙友系统相关，已废弃

```sql
-- 删除的字段
is_main_character Boolean false 是否主角 索引
position_in_formation Number -1 布阵位置(-1未布阵) -
unlock_requirements Object {} 解锁条件 -
is_unlocked Boolean true 是否已解锁 -
```

#### player_skills表字段清理

**删除的字段**:
- `unlock_requirements` - 与旧技能解锁系统相关
- `is_unlocked` - 与旧技能解锁系统相关

```sql
-- 删除的字段
unlock_requirements Object {} 解锁条件 -
is_unlocked Boolean true 是否已解锁 -
```

#### player_sword_hearts表字段清理

**删除的字段**:
- `is_unlocked` - 与旧解锁系统相关
- `unlock_requirements` - 与旧解锁系统相关

```sql
-- 删除的字段
is_unlocked Boolean false 是否解锁 -
unlock_requirements Object {} 解锁条件 -
```

#### players表字段清理

**删除的字段**:
- `formation` - 不再需要布阵数据

```sql
-- 删除的字段
formation Array [] 布阵数据 -
```

#### player_arena表字段清理

**删除的字段**:
- `defense_formation` - 不再需要防守阵容

```sql
-- 删除的字段  
defense_formation Array [] 防守阵容 -
```

#### player_idle表字段清理

**删除的字段**:
- `battle_formation` - 不再需要战斗阵容

```sql
-- 删除的字段
battle_formation Array [] 战斗阵容 -
```

#### battle_records表字段清理

**删除的字段**:
- `player_formation` - 不再记录玩家阵容
- `opponent_formation` - 不再记录对手阵容

```sql
-- 删除的字段
player_formation Array [] 玩家阵容 -
opponent_formation Array [] 对手阵容 -
```

## 系统架构变更

### 旧战斗系统 vs 新战斗系统

| 特性 | 旧系统 | 新系统 |
|------|--------|--------|
| **角色数量** | 多角色布阵 | 单角色操作 |
| **布阵机制** | 六人轮盘布阵 | 无需布阵 |
| **主角概念** | 区分主角/仙友 | 只有一个主角 |
| **解锁系统** | 复杂解锁条件 | 简化或移除 |
| **战斗记录** | 记录双方阵容 | 记录战斗结果 |
| **竞技场** | 防守阵容设置 | 简化战斗模式 |

### 数据表结构优化

#### 简化前后对比

**characters表** (10个字段 → 6个字段):
```sql
-- 保留字段
_id, _openid, character_id, name, level, exp, star, power, 
cultivation, base_attributes, bonus_attributes, total_attributes, 
equipped_skills, breakthrough_materials, createdAt, updatedAt

-- 删除字段  
is_main_character, position_in_formation, unlock_requirements, is_unlocked
```

**players表** (18个字段 → 17个字段):
```sql
-- 删除字段
formation
```

**其他表** 类似简化，删除了布阵和旧解锁相关字段

## 代码影响分析

### 1. 云函数修改 ✅

**createPlayerData云函数**:
- 删除`formation`字段的创建
- 修改数据库连接环境为生产环境

### 2. 客户端代码需要修改 ⚠️

**需要检查和修改的代码**:
- 删除所有与`formation`相关的代码
- 删除所有与`position_in_formation`相关的代码
- 删除所有与`is_main_character`判断相关的代码
- 删除所有与`is_unlocked`状态检查相关的代码
- 简化角色管理逻辑，只处理单个主角

### 3. 数据库操作修改 ⚠️

**需要更新的查询**:
```javascript
// 需要删除的查询字段
formation, position_in_formation, is_main_character, 
is_unlocked, unlock_requirements, defense_formation, 
battle_formation, player_formation, opponent_formation
```

## 性能优化效果

### 1. 数据库存储优化
- **减少字段数量**: 总共删除约15个字段
- **简化索引**: 删除`is_main_character`等索引字段
- **减少存储空间**: 每条记录减少约200-500字节

### 2. 查询性能提升
- **简化查询条件**: 无需复杂的布阵相关查询
- **减少JOIN操作**: 简化表间关联查询
- **提高缓存效率**: 更小的数据结构便于缓存

### 3. 开发维护简化
- **减少字段维护**: 少量字段降低维护复杂度
- **简化业务逻辑**: 移除复杂的布阵和解锁逻辑
- **提高代码可读性**: 清晰的单角色模式

## 迁移建议

### 1. 数据库迁移步骤
1. **备份现有数据**: 完整备份当前数据库
2. **字段标记**: 将删除字段标记为deprecated
3. **逐步清理**: 分批删除过时字段
4. **验证数据**: 确保数据完整性

### 2. 代码迁移步骤
1. **代码审查**: 全面搜索过时字段的使用
2. **功能重构**: 重构相关功能适配新结构
3. **测试验证**: 全面测试新的战斗系统
4. **渐进发布**: 分阶段发布新版本

### 3. 兼容性处理
- **API兼容**: 保持客户端API向后兼容
- **数据转换**: 处理现有用户的数据转换
- **错误处理**: 优雅处理缺失字段

## 注意事项

### 1. 数据安全
- 确保删除字段前完成数据备份
- 验证删除操作不影响核心功能
- 保留必要的历史数据引用

### 2. 功能完整性
- 确认新战斗系统完全替代旧系统
- 验证所有相关功能正常工作
- 测试边界情况和异常处理

### 3. 用户体验
- 确保游戏体验不受影响
- 处理可能的数据丢失情况
- 提供必要的用户通知

这次清理大幅简化了数据库结构，为单角色战斗系统提供了更简洁高效的数据支持。 