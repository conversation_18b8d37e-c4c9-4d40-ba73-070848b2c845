/**
 * 认证相关路由
 * 提供管理员登录、用户信息查询、权限验证等功能
 */

const express = require('express');
const rateLimit = require('express-rate-limit');
const { 
  authenticateUser, 
  generateToken, 
  authenticateToken, 
  requireAdmin,
  ADMIN_USERS 
} = require('../middleware/auth');
const { auditLogger } = require('../middleware/audit');

const router = express.Router();

// 登录频率限制 - 防止暴力破解
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 10, // 最多10次尝试（增加限制）
  message: {
    success: false,
    error: '登录尝试过于频繁，请15分钟后再试',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // 基于IP和用户名限制
    return `${req.ip}-${req.body?.username || 'unknown'}`;
  }
});

// Token验证频率限制 - 针对verify-token接口
const tokenVerifyLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 30, // 每分钟最多30次验证
  message: {
    success: false,
    error: 'Token验证请求过于频繁，请稍后再试',
    code: 'TOKEN_VERIFY_RATE_LIMIT'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// 用户登录
router.post('/login', 
  loginLimiter,
  async (req, res) => {
    try {
      const { username, password } = req.body;
      
      // 验证输入
      if (!username || !password) {
        return res.status(400).json({
          success: false,
          error: '用户名和密码不能为空',
          code: 'MISSING_CREDENTIALS'
        });
      }

      // 简单的用户验证（只给您个人使用）
      const validUsers = {
        'admin': 'admin123',
        'operator': 'operator123'
      };

      if (!validUsers[username] || validUsers[username] !== password) {
        return res.status(401).json({
          success: false,
          error: '用户名或密码错误',
          code: 'INVALID_CREDENTIALS'
        });
      }

      // 设置session
      req.session.isLoggedIn = true;
      req.session.user = {
        id: username,
        username: username,
        role: username === 'admin' ? 'admin' : 'operator',
        loginTime: new Date()
      };

      // 异步记录登录日志（不影响响应）
      setImmediate(async () => {
        try {
          await auditLogger.recordAuditLog({
            admin_id: username,
            admin_username: username,
            action: 'LOGIN',
            target_type: 'auth',
            target_id: '',
            status: 'success',
            description: '管理员登录成功',
            ip_address: req.ip || 'unknown',
            user_agent: req.get('User-Agent') || '',
            timestamp: new Date()
          });
        } catch (error) {
          console.warn('登录日志记录失败（已忽略）:', error.message);
        }
      });

      res.json({
        success: true,
        message: '登录成功',
        user: req.session.user
      });

    } catch (error) {
      console.error('登录处理错误:', error);
      res.status(500).json({
        success: false,
        error: '登录处理失败',
        code: 'LOGIN_ERROR'
      });
    }
  }
);

// 获取当前用户信息
router.get('/me', authenticateToken, (req, res) => {
  try {
    const user = ADMIN_USERS[req.user.username];
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: '用户不存在',
        code: 'USER_NOT_FOUND'
      });
    }

    res.json({
      success: true,
      user: {
        id: user.id,
        username: user.username,
        role: user.role,
        permissions: user.permissions,
        created_at: user.created_at,
        last_login: user.last_login
      }
    });

  } catch (error) {
    console.error('获取用户信息错误:', error);
    res.status(500).json({
      success: false,
      error: '获取用户信息失败',
      code: 'GET_USER_ERROR'
    });
  }
});

// 验证Token有效性
router.post('/verify-token', authenticateToken, tokenVerifyLimiter, (req, res) => {
  res.json({
    success: true,
    message: 'Token有效',
    user: {
      id: req.user.userId,
      username: req.user.username,
      role: req.user.role,
      permissions: req.user.permissions
    },
    expires_at: new Date(req.user.exp * 1000)
  });
});

// 刷新Token（延长有效期）
router.post('/refresh-token', authenticateToken, (req, res) => {
  try {
    const user = ADMIN_USERS[req.user.username];
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: '用户不存在',
        code: 'USER_NOT_FOUND'
      });
    }

    // 生成新的Token
    const newToken = generateToken({
      id: user.id,
      username: user.username,
      role: user.role,
      permissions: user.permissions
    });

    res.json({
      success: true,
      message: 'Token刷新成功',
      token: newToken
    });

  } catch (error) {
    console.error('Token刷新错误:', error);
    res.status(500).json({
      success: false,
      error: 'Token刷新失败',
      code: 'REFRESH_TOKEN_ERROR'
    });
  }
});

// 注销登录
router.post('/logout', async (req, res) => {
  try {
    // 清除session
    req.session.destroy((err) => {
      if (err) {
        console.error('销毁session失败:', err);
      }
    });

    res.json({
      success: true,
      message: '注销成功'
    });

  } catch (error) {
    console.error('注销处理错误:', error);
    res.status(500).json({
      success: false,
      error: '注销处理失败',
      code: 'LOGOUT_ERROR'
    });
  }
});

// 获取所有管理员列表（仅超级管理员可访问）
router.get('/admin-users', authenticateToken, requireAdmin, (req, res) => {
  try {
    const users = Object.values(ADMIN_USERS).map(user => ({
      id: user.id,
      username: user.username,
      role: user.role,
      permissions: user.permissions,
      created_at: user.created_at,
      last_login: user.last_login
    }));

    res.json({
      success: true,
      data: users,
      total: users.length
    });

  } catch (error) {
    console.error('获取管理员列表错误:', error);
    res.status(500).json({
      success: false,
      error: '获取管理员列表失败',
      code: 'GET_ADMIN_USERS_ERROR'
    });
  }
});

// 修改密码
router.post('/change-password', authenticateToken, async (req, res) => {
  try {
    const { current_password, new_password } = req.body;
    
    // 验证输入
    if (!current_password || !new_password) {
      return res.status(400).json({
        success: false,
        error: '当前密码和新密码不能为空',
        code: 'MISSING_PASSWORDS'
      });
    }

    // 验证新密码强度
    if (new_password.length < 8 || new_password.length > 50) {
      return res.status(400).json({
        success: false,
        error: '新密码长度必须在8-50个字符之间',
        code: 'WEAK_PASSWORD'
      });
    }

    const user = ADMIN_USERS[req.user.username];
    if (!user) {
      return res.status(404).json({
        success: false,
        error: '用户不存在',
        code: 'USER_NOT_FOUND'
      });
    }

    // 验证当前密码
    const crypto = require('crypto');
    const currentPasswordHash = crypto.createHash('sha256').update(current_password).digest('hex');
    
    if (user.password !== currentPasswordHash) {
      return res.status(401).json({
        success: false,
        error: '当前密码不正确',
        code: 'INVALID_CURRENT_PASSWORD'
      });
    }

    // 更新密码
    const newPasswordHash = crypto.createHash('sha256').update(new_password).digest('hex');
    user.password = newPasswordHash;

    // 记录密码修改操作
    await auditLogger.recordAuditLog({
      admin_id: req.user.userId,
      admin_username: req.user.username,
      action: 'UPDATE',
      target_type: 'admin_password',
      target_id: req.user.userId,
      status: 'success',
      description: '管理员修改密码',
      ip_address: req.ip || req.connection?.remoteAddress || 'unknown',
      user_agent: req.get('User-Agent') || '',
      timestamp: new Date()
    });

    res.json({
      success: true,
      message: '密码修改成功'
    });

  } catch (error) {
    console.error('修改密码错误:', error);
    res.status(500).json({
      success: false,
      error: '修改密码失败',
      code: 'CHANGE_PASSWORD_ERROR'
    });
  }
});

module.exports = router; 