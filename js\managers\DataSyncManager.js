/**
 * 数据同步管理器
 * 负责玩家数据的完整生命周期管理：初始化、加载、同步、保存
 * 版本: v2.0 - 添加仙友系统数据同步支持
 */

import game from '../../game.js';

class DataSyncManager {
  constructor(gameStateManager) {
    this.gameStateManager = gameStateManager;
    this.isInitialized = false;
    this.isInitializing = false;
    this.autoSaveInterval = null;
    this.lastSyncTime = 0;
    this.syncQueue = [];
    this.isSyncing = false;
    
    // 自动保存间隔（5分钟）
    this.AUTO_SAVE_INTERVAL = 5 * 60 * 1000;
    
    // 数据同步延迟（避免频繁同步）
    this.SYNC_DELAY = 3000;
    
    // 仙友数据同步配置
    this.xianyouSyncConfig = {
      enableSync: true,
      batchSize: 10,
      syncFields: [
        'ownedXianyou',
        'favorabilityLevels', 
        'favorabilityPoints',
        'starLevels',
        'placedInDongfu',
        'gachaPity',
        'lastInteractionTime'
      ]
    };
    
    console.log('数据同步管理器构造完成');
  }
  
  /**
   * 初始化数据同步管理器
   */
  async initialize() {
    try {
      // 防止重复初始化
      if (this.isInitialized) {
        console.log('数据同步管理器已初始化，跳过重复初始化');
        return true;
      }
      
      // 防止并发初始化
      if (this.isInitializing) {
        console.log('数据同步管理器正在初始化中，等待完成...');
        // 等待初始化完成
        while (this.isInitializing) {
          await this.delay(100);
        }
        return this.isInitialized;
      }
      
      this.isInitializing = true;
      
      console.log('开始初始化数据同步管理器...');
      
      // 检查用户登录状态
      const gameInstance = this.gameStateManager.game || game;
      if (!gameInstance.user || !gameInstance.user.openid) {
        console.log('用户未登录，等待登录后初始化');
        this.isInitializing = false;
        return false;
      }
      
      // 执行首次登录数据处理
      const success = await this.handleFirstTimeLogin();
      
      if (success) {
        // 启动自动保存
        this.startAutoSave();
        
        // 绑定数据变更监听
        this.bindDataChangeListeners();
        
        // 绑定仙友系统数据变更监听
        this.bindXianyouDataListeners();
        
        this.isInitialized = true;
        console.log('数据同步管理器初始化完成（包含仙友系统支持）');
      } else {
        console.error('数据同步管理器初始化失败');
      }
      
      this.isInitializing = false;
      return success;
      
    } catch (error) {
      console.error('数据同步管理器初始化异常:', error);
      this.isInitializing = false;
      return false;
    }
  }
  
  /**
   * 处理首次登录的数据初始化
   */
  async handleFirstTimeLogin() {
    try {
      console.log('处理首次登录数据初始化...');
      
      // 1. 尝试从云端加载数据
      const cloudDataExists = await this.loadDataFromCloud();
      
      if (cloudDataExists) {
        console.log('从云端加载数据成功，玩家为老玩家');
        wx.showToast({
          title: '数据加载成功',
          icon: 'success',
          duration: 2000
        });
        return true;
      } else {
        console.log('云端无数据，开始创建新玩家数据...');
        
        // 2. 创建新玩家数据到云端
        const initSuccess = await this.initializeNewPlayerData();
        
        if (initSuccess) {
          console.log('新玩家数据创建成功');
          wx.showToast({
            title: '欢迎新玩家！',
            icon: 'success',
            duration: 2000
          });
          return true;
        } else {
          console.error('新玩家数据创建失败');
          return false;
        }
      }
      
    } catch (error) {
      console.error('首次登录数据处理失败:', error);
      return false;
    }
  }
  
  /**
   * 从云端加载玩家数据
   */
  async loadDataFromCloud() {
    try {
      console.log('开始从云端加载玩家数据...');
      
      // 查询玩家基础数据 - 添加环境类型参数
      const playerResult = await wx.cloud.callFunction({
        name: 'databaseService',
        data: {
          action: 'get',
          tableName: 'players',
          conditions: {}, // 云函数会自动添加_openid过滤条件
          envType: 'prod' // 确保连接到正式环境
        }
      });
      
      console.log('玩家数据查询结果:', playerResult);
      console.log('云函数返回结构详细信息:');
      console.log('- playerResult.result:', playerResult.result);
      console.log('- playerResult.result.success:', playerResult.result?.success);
      console.log('- playerResult.result.data:', playerResult.result?.data);
      console.log('- playerResult.result.data.records:', playerResult.result?.data?.records);
      
      // 检查查询是否成功
      if (!playerResult.result || !playerResult.result.success) {
        console.error('查询玩家数据失败:', playerResult.result?.error || playerResult.errMsg);
        return false;
      }
      
      // 检查是否有数据记录 - 添加更详细的调试
      const resultData = playerResult.result.data;
      console.log('resultData的类型:', typeof resultData);
      console.log('resultData的内容:', resultData);
      
      let records;
      if (resultData && resultData.records) {
        // 检查records是数组还是单个对象
        if (Array.isArray(resultData.records)) {
          records = resultData.records;
          console.log('使用标准records数组格式');
        } else if (resultData.records && typeof resultData.records === 'object') {
          // records是单个对象，包装成数组
          records = [resultData.records];
          console.log('检测到records是单个对象，包装为数组');
        } else {
          records = [];
          console.log('records格式异常');
        }
      } else if (Array.isArray(resultData)) {
        // 有些云函数可能直接返回数组
        records = resultData;
        console.log('检测到直接返回数组格式');
      } else if (resultData && typeof resultData === 'object' && !Array.isArray(resultData)) {
        // 如果resultData是单个对象，包装成数组
        records = [resultData];
        console.log('检测到单个对象格式，包装为数组');
      } else {
        records = [];
        console.log('未找到有效的records数据，可能是新用户');
      }
      
      console.log('最终解析的records:', records);
      console.log('查询到的记录数量:', records.length);
      
      if (records.length === 0) {
        console.log('云端无玩家数据，用户可能是新用户');
        return false;
      }
      
      const playerData = records[0];
      console.log('找到云端玩家数据:', playerData);
      
      // 验证数据完整性
      if (!playerData || typeof playerData !== 'object') {
        console.error('云端玩家数据格式错误:', playerData);
        return false;
      }
      
      // 验证必要字段
      if (!playerData._openid) {
        console.error('玩家数据缺少_openid字段');
        return false;
      }
      
      // 加载玩家基础信息
      await this.loadPlayerBasicData(playerData);
      
      // 加载玩家资源
      await this.loadPlayerResources();
      
      // 加载其他数据
      await this.loadOtherPlayerData();
      
      // 更新本地游戏状态
      this.updateLocalGameState();
      
      console.log('云端数据加载完成');
      return true;
      
    } catch (error) {
      console.error('从云端加载数据失败:', error);
      return false;
    }
  }
  
  /**
   * 加载玩家基础数据
   */
  async loadPlayerBasicData(playerData) {
    // 添加数据有效性检查
    if (!playerData) {
      console.error('playerData为空，无法加载玩家基础数据');
      return;
    }
    
    console.log('正在加载玩家基础数据:', playerData);
    
    const gameState = this.gameStateManager.state;
    
    // 更新玩家基础信息
    gameState.player = {
      ...gameState.player,
      nickname: playerData.nickname || '修仙者',
      avatarUrl: playerData.avatar_url || '',
      level: playerData.level || 1,
      exp: playerData.exp || 0,
      power: playerData.power || 0,
      cultivation_realm: playerData.cultivation_realm || '炼气期一层',
      dongfu_level: playerData.dongfu_level || 1,
      vip_level: playerData.vip_level || 0,
      total_recharge: playerData.total_recharge || 0,
      last_vip_reward_time: playerData.last_vip_reward_time,
      last_login_time: playerData.last_login_time,
      lastOfflineTime: playerData.last_offline_time || null, // 添加离线时间字段
      registration_time: playerData.registration_time,
      game_settings: playerData.game_settings || {}
    };
    
    console.log('玩家基础数据加载完成');
  }
  
  /**
   * 加载玩家资源数据
   */
  async loadPlayerResources() {
    try {
      console.log('开始加载玩家资源数据...');
      
      const result = await wx.cloud.callFunction({
        name: 'databaseService',
        data: {
          action: 'get',
          tableName: 'player_res',
          conditions: {}
        }
      });
      
      console.log('player_res云函数返回结果:', result);
      
      if (!result.result || !result.result.success) {
        console.log('player_res查询失败或无数据');
        return;
      }
      
      // 处理数据格式兼容性
      const resultData = result.result.data;
      let records = [];
      
      if (Array.isArray(resultData.records)) {
        records = resultData.records;
      } else if (resultData.records && typeof resultData.records === 'object') {
        records = [resultData.records];
      } else {
        records = [];
      }
      
      if (records.length === 0) {
        console.log('player_res表中无数据');
        return;
      }
      
      const resourceData = records[0];
      console.log('找到玩家资源数据:', resourceData);
      
      // 更新资源数据
      this.gameStateManager.state.player.resources = {
        xianyu: resourceData.xianyu || 0,
        lingshi: resourceData.lingshi || 0,
        swordIntent: resourceData.sword_intent || 0,
        lianlidian: resourceData.lianlidian || 0,
        spiritStone: resourceData.spirit_stone || 0,
        tiangangStone: resourceData.tiangang_stone || 0,
        xiuweiPoint: resourceData.xiuwei_point || 0,
        arenaPoint: resourceData.arena_point || 0,
        guildContribution: resourceData.guild_contribution || 0,
        lingli: resourceData.lingli || 0  // 添加灵力数据
      };
      
      console.log('玩家资源数据加载完成:', this.gameStateManager.state.player.resources);
      
    } catch (error) {
      console.error('加载玩家资源失败:', error);
    }
  }
  
  /**
   * 加载洞府灵力数据
   */
  async loadPlayerDongfuData() {
    try {
      console.log('开始加载洞府数据...');
      
      const result = await wx.cloud.callFunction({
        name: 'databaseService',
        data: {
          action: 'get',
          tableName: 'player_dongf',
          conditions: {}
        }
      });
      
      console.log('player_dongf云函数返回结果:', result);
      
      if (!result.result || !result.result.success) {
        console.log('player_dongf查询失败或无数据');
        return;
      }
      
      // 处理数据格式兼容性
      const resultData = result.result.data;
      let records = [];
      
      if (Array.isArray(resultData.records)) {
        records = resultData.records;
      } else if (resultData.records && typeof resultData.records === 'object') {
        records = [resultData.records];
      } else {
        records = [];
      }
      
      if (records.length === 0) {
        console.log('player_dongf表中无数据');
        return;
      }
      
      const dongfuData = records[0];
      console.log('找到洞府数据:', dongfuData);
      
      // 更新洞府数据，包括灵力
      if (!this.gameStateManager.state.player.dongfu) {
        this.gameStateManager.state.player.dongfu = {};
      }
      
      this.gameStateManager.state.player.dongfu = {
        level: dongfuData.level || 1,
        current_lingqi: dongfuData.current_lingqi || 0,
        max_lingqi: dongfuData.max_lingqi || 1000,
        lingqi_per_hour: dongfuData.lingqi_per_hour || 10,
        last_collect_time: dongfuData.last_collect_time,
        cultivation_start_time: dongfuData.cultivation_start_time,
        cultivation_character_id: dongfuData.cultivation_character_id,
        cultivation_speed_bonus: dongfuData.cultivation_speed_bonus || 1.0,
        buildings: dongfuData.buildings || {},
        upgrade_materials: dongfuData.upgrade_materials || {}
      };
      
      // 同时更新玩家的灵力资源（从洞府数据同步）
      if (!this.gameStateManager.state.player.resources) {
        this.gameStateManager.state.player.resources = {};
      }
      this.gameStateManager.state.player.resources.lingli = dongfuData.current_lingqi || 0;
      
      console.log('洞府数据加载完成:', this.gameStateManager.state.player.dongfu);
      
    } catch (error) {
      console.error('加载洞府数据失败:', error);
    }
  }
  
  /**
   * 加载其他玩家数据
   */
  async loadOtherPlayerData() {
    try {
      // 加载洞府数据（包含灵力）
      await this.loadPlayerDongfuData();
      
      // 可以在这里加载古宝、技能、剑心等其他数据
      console.log('其他玩家数据加载完成');
    } catch (error) {
      console.error('加载其他玩家数据失败:', error);
    }
  }
  
  /**
   * 初始化新玩家数据
   */
  async initializeNewPlayerData() {
    try {
      console.log('开始创建新玩家数据...');
      
      const currentTime = Date.now();
      const gameState = this.gameStateManager.state;
      const userInfo = this.getUserInfo();
      
      // 创建玩家基础信息
      const playerData = {
        nickname: userInfo.nickname || gameState.player?.nickname || '修仙者',
        avatar_url: userInfo.avatarUrl || gameState.player?.avatarUrl || '',
        server_id: 1,
        level: gameState.player?.level || 1,
        exp: gameState.player?.exp || 0,
        power: gameState.player?.power || 100,
        cultivation_realm: gameState.player?.cultivation_realm || '炼气期一层',
        dongfu_level: gameState.player?.dongfu_level || 1,
        vip_level: gameState.player?.vip_level || 0,
        total_recharge: gameState.player?.total_recharge || 0,
        last_vip_reward_time: null,
        last_login_time: currentTime,
        last_offline_time: null,
        registration_time: currentTime,
        game_settings: gameState.player?.game_settings || {}
      };
      
      // 创建玩家资源
      const resourceData = {
        xianyu: gameState.player?.resources?.xianyu || 1000,
        lingshi: gameState.player?.resources?.lingshi || 1000,
        sword_intent: gameState.player?.resources?.swordIntent || 0,
        lianlidian: gameState.player?.resources?.lianlidian || 100,
        spirit_stone: gameState.player?.resources?.spiritStone || 0,
        tiangang_stone: gameState.player?.resources?.tiangangStone || 0,
        xiuwei_point: gameState.player?.resources?.xiuweiPoint || 0,
        arena_point: gameState.player?.resources?.arenaPoint || 0,
        guild_contribution: gameState.player?.resources?.guildContribution || 0,
        lingli: gameState.player?.resources?.lingli || 0  // 添加灵力字段
      };
      
      // 创建洞府数据
      const dongfuData = {
        level: gameState.player?.dongfu_level || 1,
        current_lingqi: gameState.player?.resources?.lingli || 0,
        max_lingqi: 1000,
        lingqi_per_hour: 10,
        last_collect_time: null,
        cultivation_start_time: null,
        cultivation_character_id: null,
        cultivation_speed_bonus: 1.0,
        buildings: {},
        upgrade_materials: {}
      };
      
      // 批量创建数据表
      const creationTasks = [
        { table: 'players', data: playerData },
        { table: 'player_res', data: resourceData },
        { table: 'player_dongf', data: dongfuData }
      ];
      
      let successCount = 0;
      for (const task of creationTasks) {
        try {
          console.log(`创建 ${task.table} 数据...`);
          
          const result = await wx.cloud.callFunction({
            name: 'databaseService',
            data: {
              action: 'create',
              tableName: task.table,
              data: task.data
            }
          });
          
          if (result.result.success) {
            console.log(`✓ ${task.table} 创建成功`);
            successCount++;
          } else {
            console.error(`✗ ${task.table} 创建失败:`, result.result.error);
          }
        } catch (error) {
          console.error(`✗ ${task.table} 创建异常:`, error);
        }
        
        // 添加延迟
        await this.delay(200);
      }
      
      if (successCount > 0) {
        // 更新本地游戏状态
        this.gameStateManager.state.player = {
          ...this.gameStateManager.state.player,
          ...playerData,
          resources: resourceData
        };
        
        this.updateLocalGameState();
        
        console.log(`新玩家数据创建完成: ${successCount}/${creationTasks.length} 成功`);
        return true;
      } else {
        console.error('新玩家数据创建失败');
        return false;
      }
      
    } catch (error) {
      console.error('初始化新玩家数据失败:', error);
      return false;
    }
  }
  
  /**
   * 更新本地游戏状态
   */
  updateLocalGameState() {
    try {
      // 触发数据更新事件
      this.gameStateManager.emit('playerDataChanged', this.gameStateManager.state.player);
      
      // 保存到本地存储
      this.gameStateManager.saveGameStateLocal();
      
      console.log('本地游戏状态更新完成');
    } catch (error) {
      console.error('更新本地游戏状态失败:', error);
    }
  }
  
  /**
   * 启动自动保存
   */
  startAutoSave() {
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval);
    }
    
    this.autoSaveInterval = setInterval(() => {
      this.performAutoSave();
    }, this.AUTO_SAVE_INTERVAL);
    
    console.log(`自动保存已启动，间隔: ${this.AUTO_SAVE_INTERVAL / 1000}秒`);
  }
  
  /**
   * 执行自动保存
   */
  async performAutoSave() {
    try {
      console.log('执行自动保存...');
      
      const success = await this.syncDataToCloud();
      
      if (success) {
        console.log('自动保存成功');
      } else {
        console.error('自动保存失败');
      }
    } catch (error) {
      console.error('自动保存异常:', error);
    }
  }
  
  /**
   * 绑定数据变更监听
   */
  bindDataChangeListeners() {
    // 监听资源变更
    this.gameStateManager.on('resourcesChanged', (data) => {
      this.queueDataSync('resources', data);
    });
    
    // 监听玩家升级
    this.gameStateManager.on('playerLevelUp', (data) => {
      this.queueDataSync('level', data);
    });
    
    // 监听境界突破
    this.gameStateManager.on('realmBreakthrough', (data) => {
      this.queueDataSync('realm', data);
    });
    
    // 监听其他数据变更...
    
    console.log('数据变更监听器绑定完成');
  }
  
  /**
   * 队列化数据同步
   */
  queueDataSync(type, data) {
    this.syncQueue.push({
      type: type,
      data: data,
      timestamp: Date.now()
    });
    
    // 延迟同步，避免频繁操作
    if (!this.syncTimeout) {
      this.syncTimeout = setTimeout(() => {
        this.processSyncQueue();
        this.syncTimeout = null;
      }, this.SYNC_DELAY);
    }
  }
  
  /**
   * 处理同步队列
   */
  async processSyncQueue() {
    if (this.isSyncing || this.syncQueue.length === 0) {
      return;
    }
    
    this.isSyncing = true;
    
    try {
      console.log(`处理同步队列，待同步项目: ${this.syncQueue.length}`);
      
      // 执行数据同步
      const success = await this.syncDataToCloud();
      
      if (success) {
        // 清空队列
        this.syncQueue = [];
        this.lastSyncTime = Date.now();
        console.log('数据同步队列处理完成');
      } else {
        console.error('数据同步失败，保留队列');
      }
    } catch (error) {
      console.error('处理同步队列失败:', error);
    } finally {
      this.isSyncing = false;
    }
  }
  
  /**
   * 同步数据到云端
   */
  async syncDataToCloud() {
    try {
      const gameInstance = this.gameStateManager.game || game;
      if (!gameInstance.user || !gameInstance.user.openid) {
        console.log('用户未登录，跳过云端同步');
        return false;
      }
      
      console.log('开始同步数据到云端...');
      
      const gameState = this.gameStateManager.state;
      const player = gameState.player;
      
      // 同步玩家基础信息
      const playerUpdateData = {
        nickname: player.nickname,
        avatar_url: player.avatarUrl,
        level: player.level,
        exp: player.exp,
        power: player.power,
        cultivation_realm: player.cultivation_realm,
        dongfu_level: player.dongfu_level,
        vip_level: player.vip_level,
        total_recharge: player.total_recharge,
        last_login_time: Date.now(),
        last_offline_time: player.lastOfflineTime || null, // 添加离线时间字段
        game_settings: player.game_settings || {}
      };
      
      await wx.cloud.callFunction({
        name: 'databaseService',
        data: {
          action: 'update',
          tableName: 'players',
          conditions: {},
          data: playerUpdateData
        }
      });
      
      // 同步玩家资源
      if (player.resources) {
        const resourceUpdateData = {
          xianyu: player.resources.xianyu || 0,
          lingshi: player.resources.lingshi || 0,
          sword_intent: player.resources.swordIntent || 0,
          lianlidian: player.resources.lianlidian || 0,
          spirit_stone: player.resources.spiritStone || 0,
          tiangang_stone: player.resources.tiangangStone || 0,
          xiuwei_point: player.resources.xiuweiPoint || 0,
          arena_point: player.resources.arenaPoint || 0,
          guild_contribution: player.resources.guildContribution || 0,
          lingli: player.resources.lingli || 0  // 添加灵力字段
        };
        
        await wx.cloud.callFunction({
          name: 'databaseService',
          data: {
            action: 'update',
            tableName: 'player_res',
            conditions: {},
            data: resourceUpdateData
          }
        });
      }
      
      // 同步洞府数据
      if (player.dongfu || player.resources?.lingli !== undefined) {
        const dongfuUpdateData = {
          level: player.dongfu?.level || player.dongfu_level || 1,
          current_lingqi: player.resources?.lingli || player.dongfu?.current_lingqi || 0,
          max_lingqi: player.dongfu?.max_lingqi || 1000,
          lingqi_per_hour: player.dongfu?.lingqi_per_hour || 10,
          last_collect_time: player.dongfu?.last_collect_time || null,
          cultivation_start_time: player.dongfu?.cultivation_start_time || null,
          cultivation_character_id: player.dongfu?.cultivation_character_id || null,
          cultivation_speed_bonus: player.dongfu?.cultivation_speed_bonus || 1.0,
          buildings: player.dongfu?.buildings || {},
          upgrade_materials: player.dongfu?.upgrade_materials || {}
        };
        
        await wx.cloud.callFunction({
          name: 'databaseService',
          data: {
            action: 'update',
            tableName: 'player_dongf',
            conditions: {},
            data: dongfuUpdateData
          }
        });
      }
      
      console.log('数据同步到云端完成');
      return true;
      
    } catch (error) {
      console.error('同步数据到云端失败:', error);
      return false;
    }
  }
  
  /**
   * 手动触发数据同步
   */
  async manualSync() {
    try {
      wx.showLoading({
        title: '同步中...',
        mask: true
      });
      
      const success = await this.syncDataToCloud();
      
      wx.hideLoading();
      
      if (success) {
        wx.showToast({
          title: '同步成功',
          icon: 'success',
          duration: 2000
        });
      } else {
        wx.showToast({
          title: '同步失败',
          icon: 'error',
          duration: 2000
        });
      }
      
      return success;
    } catch (error) {
      wx.hideLoading();
      console.error('手动同步失败:', error);
      wx.showToast({
        title: '同步异常',
        icon: 'error',
        duration: 2000
      });
      return false;
    }
  }
  
  /**
   * 获取用户信息
   */
  getUserInfo() {
    const userInfo = wx.getStorageSync('userInfo') || {};
    const gameInstance = this.gameStateManager.game || game;
    return {
      nickname: userInfo.nickName || gameInstance.user?.nickName || '修仙者',
      avatarUrl: userInfo.avatarUrl || gameInstance.user?.avatarUrl || ''
    };
  }
  
  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  /**
   * 获取同步状态
   */
  getSyncStatus() {
    return {
      isInitialized: this.isInitialized,
      isSyncing: this.isSyncing,
      lastSyncTime: this.lastSyncTime,
      queueLength: this.syncQueue.length,
      autoSaveEnabled: !!this.autoSaveInterval,
      xianyouSyncEnabled: this.xianyouSyncConfig.enableSync
    };
  }

  // ========== 仙友系统数据同步方法 ==========

  /**
   * 绑定仙友系统数据变更监听
   */
  bindXianyouDataListeners() {
    if (!this.gameStateManager) return;

    try {
      // 监听仙友添加事件
      this.gameStateManager.on('xianyouAdded', (xianyou) => {
        this.queueDataSync('xianyouAdded', xianyou);
      });

      // 监听好感度更新事件
      this.gameStateManager.on('xianyouFavorabilityUpdated', (data) => {
        this.queueDataSync('xianyouFavorabilityUpdated', data);
      });

      // 监听好感度等级提升事件
      this.gameStateManager.on('xianyouFavorabilityLevelUp', (data) => {
        this.queueDataSync('xianyouFavorabilityLevelUp', data);
      });

      // 监听仙友升星事件
      this.gameStateManager.on('xianyouStarUpgraded', (data) => {
        this.queueDataSync('xianyouStarUpgraded', data);
      });

      // 监听洞府放置事件
      this.gameStateManager.on('xianyouPlacedInDongfu', (data) => {
        this.queueDataSync('xianyouPlacedInDongfu', data);
      });

      // 监听洞府移除事件
      this.gameStateManager.on('xianyouRemovedFromDongfu', (data) => {
        this.queueDataSync('xianyouRemovedFromDongfu', data);
      });

      console.log('仙友系统数据变更监听绑定完成');
    } catch (error) {
      console.error('绑定仙友数据监听失败:', error);
    }
  }

  /**
   * 从云端加载仙友数据
   */
  async loadXianyouDataFromCloud() {
    if (!this.xianyouSyncConfig.enableSync) {
      console.log('仙友数据同步已禁用');
      return false;
    }

    try {
      console.log('开始从云端加载仙友数据...');

      // 查询玩家仙友数据
      const xianyouResult = await wx.cloud.callFunction({
        name: 'databaseService',
        data: {
          action: 'get',
          tableName: 'player_xianyou',
          conditions: {},
          envType: 'prod'
        }
      });

      console.log('仙友数据查询结果:', xianyouResult);

      if (!xianyouResult.result || !xianyouResult.result.success) {
        console.log('云端无仙友数据或查询失败');
        return false;
      }

      const resultData = xianyouResult.result.data;
      let records = [];

      if (resultData && resultData.records) {
        if (Array.isArray(resultData.records)) {
          records = resultData.records;
        } else if (typeof resultData.records === 'object') {
          records = [resultData.records];
        }
      }

      if (records.length === 0) {
        console.log('云端无仙友数据记录');
        return false;
      }

      // 加载仙友数据到游戏状态
      const xianyouData = records[0];
      this.loadXianyouDataToGameState(xianyouData);

      console.log('仙友数据加载成功');
      return true;

    } catch (error) {
      console.error('加载仙友数据失败:', error);
      return false;
    }
  }

  /**
   * 将仙友数据加载到游戏状态
   */
  loadXianyouDataToGameState(xianyouData) {
    try {
      if (!this.gameStateManager || !this.gameStateManager.state) return;

      // 确保仙友数据结构存在
      if (!this.gameStateManager.state.xianyou) {
        this.gameStateManager.state.xianyou = {
          ownedXianyou: [],
          favorabilityLevels: {},
          favorabilityPoints: {},
          starLevels: {},
          placedInDongfu: {},
          gachaPity: 0,
          lastInteractionTime: {}
        };
      }

      // 加载各个字段数据
      const xianyouState = this.gameStateManager.state.xianyou;

      if (xianyouData.owned_xianyou) {
        xianyouState.ownedXianyou = JSON.parse(xianyouData.owned_xianyou) || [];
      }

      if (xianyouData.favorability_levels) {
        xianyouState.favorabilityLevels = JSON.parse(xianyouData.favorability_levels) || {};
      }

      if (xianyouData.favorability_points) {
        xianyouState.favorabilityPoints = JSON.parse(xianyouData.favorability_points) || {};
      }

      if (xianyouData.star_levels) {
        xianyouState.starLevels = JSON.parse(xianyouData.star_levels) || {};
      }

      if (xianyouData.placed_in_dongfu) {
        xianyouState.placedInDongfu = JSON.parse(xianyouData.placed_in_dongfu) || {};
      }

      if (xianyouData.gacha_pity !== undefined) {
        xianyouState.gachaPity = xianyouData.gacha_pity || 0;
      }

      if (xianyouData.last_interaction_time) {
        xianyouState.lastInteractionTime = JSON.parse(xianyouData.last_interaction_time) || {};
      }

      console.log('仙友数据已加载到游戏状态');
    } catch (error) {
      console.error('加载仙友数据到游戏状态失败:', error);
    }
  }

  /**
   * 同步仙友数据到云端
   */
  async syncXianyouDataToCloud() {
    if (!this.xianyouSyncConfig.enableSync) return true;

    try {
      console.log('开始同步仙友数据到云端...');

      const xianyouData = this.gameStateManager.state.xianyou;
      if (!xianyouData) {
        console.log('无仙友数据需要同步');
        return true;
      }

      // 准备同步数据
      const syncData = {
        owned_xianyou: JSON.stringify(xianyouData.ownedXianyou || []),
        favorability_levels: JSON.stringify(xianyouData.favorabilityLevels || {}),
        favorability_points: JSON.stringify(xianyouData.favorabilityPoints || {}),
        star_levels: JSON.stringify(xianyouData.starLevels || {}),
        placed_in_dongfu: JSON.stringify(xianyouData.placedInDongfu || {}),
        gacha_pity: xianyouData.gachaPity || 0,
        last_interaction_time: JSON.stringify(xianyouData.lastInteractionTime || {}),
        updated_at: Date.now()
      };

      // 同步到云端
      const result = await wx.cloud.callFunction({
        name: 'databaseService',
        data: {
          action: 'update',
          tableName: 'player_xianyou',
          conditions: {},
          updateData: syncData,
          envType: 'prod'
        }
      });

      if (result.result && result.result.success) {
        console.log('仙友数据同步成功');
        return true;
      } else {
        console.error('仙友数据同步失败:', result.result?.error);
        return false;
      }

    } catch (error) {
      console.error('同步仙友数据到云端失败:', error);
      return false;
    }
  }

  /**
   * 创建新玩家仙友数据
   */
  async createNewPlayerXianyouData() {
    try {
      console.log('创建新玩家仙友数据...');

      const defaultXianyouData = {
        owned_xianyou: JSON.stringify([]),
        favorability_levels: JSON.stringify({}),
        favorability_points: JSON.stringify({}),
        star_levels: JSON.stringify({}),
        placed_in_dongfu: JSON.stringify({}),
        gacha_pity: 0,
        last_interaction_time: JSON.stringify({}),
        created_at: Date.now(),
        updated_at: Date.now()
      };

      const result = await wx.cloud.callFunction({
        name: 'databaseService',
        data: {
          action: 'create',
          tableName: 'player_xianyou',
          createData: defaultXianyouData,
          envType: 'prod'
        }
      });

      if (result.result && result.result.success) {
        console.log('新玩家仙友数据创建成功');
        return true;
      } else {
        console.error('创建新玩家仙友数据失败:', result.result?.error);
        return false;
      }

    } catch (error) {
      console.error('创建新玩家仙友数据异常:', error);
      return false;
    }
  }
  
  /**
   * 销毁数据同步管理器
   */
  destroy() {
    this.stopAutoSave();
    
    if (this.syncTimeout) {
      clearTimeout(this.syncTimeout);
      this.syncTimeout = null;
    }
    
    this.syncQueue = [];
    this.isInitialized = false;
    
    console.log('数据同步管理器已销毁');
  }
  
  /**
   * 停止自动保存
   */
  stopAutoSave() {
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval);
      this.autoSaveInterval = null;
      console.log('自动保存已停止');
    }
  }
}

export default DataSyncManager; 