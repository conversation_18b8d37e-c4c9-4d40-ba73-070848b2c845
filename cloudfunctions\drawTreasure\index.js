// 云函数：古宝抽取
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 古宝抽取概率配置
const TREASURE_PROBABILITY = {
  legendary: 0.01,  // 1% 传说
  epic: 0.04,       // 4% 史诗  
  rare: 0.10,       // 10% 稀有
  fine: 0.25,       // 25% 精良
  common: 0.60      // 60% 普通
};

// 保底设置
const GUARANTEE_COUNT = 10; // 10次保底传说

// 古宝数据（简化版，使用数字稀有度）
const TREASURE_DATA = {
  5: [ // 传说
    { id: 'ancient_sword', name: '轩辕剑', category: 'weapon', rarity: 5 },
    { id: 'jade_seal', name: '传国玉玺', category: 'artifact', rarity: 5 },
    { id: 'chaos_bell', name: '混沌钟', category: 'artifact', rarity: 5 },
    { id: 'void_pendant', name: '虚空吊坠', category: 'talisman', rarity: 5 }
  ],
  4: [ // 史诗
    { id: 'dragon_blade', name: '青龙偃月刀', category: 'weapon', rarity: 4 },
    { id: 'phoenix_sword', name: '凤凰羽剑', category: 'weapon', rarity: 4 },
    { id: 'taiji_mirror', name: '太极八卦镜', category: 'artifact', rarity: 4 },
    { id: 'dragon_armor', name: '真龙鳞甲', category: 'talisman', rarity: 4 }
  ],
  3: [ // 稀有
    { id: 'spirit_sword', name: '灵光剑', category: 'weapon', rarity: 3 },
    { id: 'moon_blade', name: '弯月刀', category: 'weapon', rarity: 3 },
    { id: 'spirit_bell', name: '灵音铃', category: 'artifact', rarity: 3 },
    { id: 'wind_pendant', name: '御风佩', category: 'talisman', rarity: 3 }
  ],
  2: [ // 精良
    { id: 'iron_sword', name: '精钢剑', category: 'weapon', rarity: 2 },
    { id: 'wood_staff', name: '桃木杖', category: 'weapon', rarity: 2 },
    { id: 'bronze_mirror', name: '青铜镜', category: 'artifact', rarity: 2 },
    { id: 'jade_pendant', name: '玉佩', category: 'talisman', rarity: 2 }
  ],
  1: [ // 普通
    { id: 'common_sword', name: '凡铁剑', category: 'weapon', rarity: 1 },
    { id: 'wooden_shield', name: '木盾', category: 'artifact', rarity: 1 },
    { id: 'cloth_bag', name: '布袋', category: 'talisman', rarity: 1 },
    { id: 'straw_hat', name: '斗笠', category: 'talisman', rarity: 1 }
  ]
};

exports.main = async (event, context) => {
  try {
    const { count = 1 } = event;
    
    // 获取微信上下文，自动获取用户openid
    const wxContext = cloud.getWXContext();
    const openid = wxContext.OPENID;
    
    console.log('drawTreasure云函数调用:', {
      count,
      openid: openid ? '已获取' : '未获取',
      timestamp: new Date().toISOString()
    });
    
    if (!openid) {
      return {
        success: false,
        message: '用户身份验证失败'
      };
    }

    // 获取玩家数据
    const playerRes = await db.collection('players').where({
      openid: openid
    }).get();

    if (playerRes.data.length === 0) {
      return {
        success: false,
        message: '玩家数据不存在'
      };
    }

    const player = playerRes.data[0];
    const drawCount = player.drawCount || 0;

    // 执行抽取
    const treasures = [];
    let newDrawCount = drawCount;

    for (let i = 0; i < count; i++) {
      newDrawCount++;
      const treasure = performDraw(newDrawCount);
      treasures.push(treasure);
    }

    // 批量添加古宝到数据库
    const treasurePromises = treasures.map(async (treasure) => {
      return await db.collection('player_treasures').add({
        data: {
          player_id: player._id,
          treasure_id: treasure.id,
          level: 1,
          star: 0,
          exp: 0,
          obtained_time: new Date()
        }
      });
    });

    await Promise.all(treasurePromises);

    // 更新玩家抽取次数
    await db.collection('players').doc(player._id).update({
      data: {
        drawCount: newDrawCount
      }
    });

    return {
      success: true,
      treasures: treasures.map(t => ({
        treasure_id: t.id,
        level: 1,
        star: 0,
        exp: 0,
        obtained_time: new Date()
      })),
      newDrawCount: newDrawCount
    };

  } catch (error) {
    console.error('古宝抽取失败:', error);
    return {
      success: false,
      message: '抽取失败，请重试'
    };
  }
};

/**
 * 执行单次抽取
 * @param {number} drawCount 当前抽取次数
 * @returns {Object} 抽到的古宝
 */
function performDraw(drawCount) {
  // 检查保底
  const guaranteeLeft = GUARANTEE_COUNT - (drawCount % GUARANTEE_COUNT);
  
  let rarityNum;
  if (guaranteeLeft === 0) {
    // 保底传说
    rarityNum = 5;
  } else {
    // 正常概率
    const roll = Math.random();
    
    if (roll < 0.01) { // 1% 传说
      rarityNum = 5;
    } else if (roll < 0.05) { // 4% 史诗
      rarityNum = 4;
    } else if (roll < 0.15) { // 10% 稀有
      rarityNum = 3;
    } else if (roll < 0.40) { // 25% 精良
      rarityNum = 2;
    } else { // 60% 普通
      rarityNum = 1;
    }
  }

  // 从对应稀有度中随机选择古宝
  const rarityTreasures = TREASURE_DATA[rarityNum];
  if (!rarityTreasures || rarityTreasures.length === 0) {
    // 如果没有对应稀有度的古宝，返回普通稀有度
    rarityNum = 1;
  }

  const availableTreasures = TREASURE_DATA[rarityNum] || TREASURE_DATA[1];
  const selectedTreasure = availableTreasures[Math.floor(Math.random() * availableTreasures.length)];

  return selectedTreasure;
} 