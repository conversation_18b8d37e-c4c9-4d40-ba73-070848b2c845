/**
 * 仙友卡片组件
 * 用于显示仙友的基本信息、头像、好感度等
 * 版本: v1.0 - 基础仙友卡片功能
 */
class XianyouCard {
  constructor(config) {
    this.x = config.x || 0;
    this.y = config.y || 0;
    this.width = config.width || 200;
    this.height = config.height || 280;
    
    // 仙友数据
    this.xianyouData = config.xianyouData || {};
    this.id = this.xianyouData.id || 0;
    this.name = this.xianyouData.name || '未知仙友';
    this.avatar = this.xianyouData.avatar || null;
    this.star = this.xianyouData.star || 1;
    this.favorability = this.xianyouData.favorability || 0;
    this.maxFavorability = this.xianyouData.maxFavorability || 100;
    this.rarity = this.xianyouData.rarity || 'common'; // common, rare, epic, legendary
    
    // 回调函数
    this.onClick = config.onClick || null;
    this.onLongPress = config.onLongPress || null;
    
    // 状态
    this.isPressed = false;
    this.isSelected = config.isSelected || false;
    this.visible = config.visible !== false;
    
    // 动画相关
    this.animationScale = 1.0;
    this.targetScale = 1.0;
    this.animationSpeed = 0.12;
    
    // 长按检测
    this.pressStartTime = 0;
    this.longPressThreshold = 800; // 800ms
    
    // 稀有度颜色配置
    this.rarityColors = {
      common: '#8E8E93',
      rare: '#007AFF', 
      epic: '#AF52DE',
      legendary: '#FF9500'
    };
  }
  
  /**
   * 获取稀有度颜色
   */
  getRarityColor(rarity) {
    return this.rarityColors[rarity] || this.rarityColors.common;
  }
  
  /**
   * 更新组件状态
   */
  update(deltaTime) {
    // 缩放动画
    if (Math.abs(this.animationScale - this.targetScale) > 0.01) {
      this.animationScale += (this.targetScale - this.animationScale) * this.animationSpeed;
    } else {
      this.animationScale = this.targetScale;
    }
  }
  
  /**
   * 检测点击
   */
  isPointInside(x, y) {
    if (!this.visible) return false;
    
    return x >= this.x && x <= this.x + this.width &&
           y >= this.y && y <= this.y + this.height;
  }
  
  /**
   * 处理触摸开始
   */
  handleTouchStart(x, y) {
    if (!this.isPointInside(x, y)) return false;
    
    this.isPressed = true;
    this.targetScale = 0.95;
    this.pressStartTime = Date.now();
    return true;
  }
  
  /**
   * 处理触摸开始（兼容旧方法名）
   */
  onTouchStart(x, y) {
    return this.handleTouchStart(x, y);
  }
  
  /**
   * 处理触摸结束
   */
  onTouchEnd(x, y) {
    if (!this.isPressed) return false;
    
    const pressDuration = Date.now() - this.pressStartTime;
    this.isPressed = false;
    this.targetScale = 1.0;
    
    if (this.isPointInside(x, y)) {
      if (pressDuration >= this.longPressThreshold && this.onLongPress) {
        // 长按事件
        this.onLongPress(this.xianyouData);
      } else if (this.onClick) {
        // 普通点击事件
        this.onClick(this.xianyouData);
      }
    }
    
    return true;
  }
  
  /**
   * 渲染卡片
   */
  render(ctx) {
    if (!this.visible) return;
    
    ctx.save();
    
    // 应用缩放变换
    const centerX = this.x + this.width / 2;
    const centerY = this.y + this.height / 2;
    
    ctx.translate(centerX, centerY);
    ctx.scale(this.animationScale, this.animationScale);
    ctx.translate(-centerX, -centerY);
    
    // 绘制卡片背景和边框
    this.drawCardBackground(ctx);
    
    // 绘制头像
    this.drawAvatar(ctx);
    
    // 绘制星级
    this.drawStars(ctx);
    
    // 绘制名字
    this.drawName(ctx);
    
    // 绘制好感度条
    this.drawFavorabilityBar(ctx);
    
    // 绘制选中状态
    if (this.isSelected) {
      this.drawSelectedIndicator(ctx);
    }
    
    ctx.restore();
  }
  
  /**
   * 绘制卡片背景
   */
  drawCardBackground(ctx) {
    const borderRadius = 12;
    const rarityColor = this.rarityColors[this.rarity] || this.rarityColors.common;
    
    // 绘制阴影
    if (!this.isPressed) {
      ctx.save();
      ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
      ctx.shadowBlur = 8;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 4;
      
      this.drawRoundedRect(ctx, this.x, this.y, this.width, this.height, borderRadius);
      ctx.fillStyle = '#FFFFFF';
      ctx.fill();
      
      ctx.restore();
    }
    
    // 绘制主背景
    this.drawRoundedRect(ctx, this.x, this.y, this.width, this.height, borderRadius);
    
    // 渐变背景
    const gradient = ctx.createLinearGradient(this.x, this.y, this.x, this.y + this.height);
    gradient.addColorStop(0, '#FFFFFF');
    gradient.addColorStop(1, '#F8F9FA');
    ctx.fillStyle = gradient;
    ctx.fill();
    
    // 绘制稀有度边框
    ctx.strokeStyle = rarityColor;
    ctx.lineWidth = 3;
    ctx.stroke();
  }
  
  /**
   * 绘制头像
   */
  drawAvatar(ctx) {
    const avatarSize = 80;
    const avatarX = this.x + (this.width - avatarSize) / 2;
    const avatarY = this.y + 20;
    
    // 绘制头像背景圆圈
    ctx.beginPath();
    ctx.arc(avatarX + avatarSize / 2, avatarY + avatarSize / 2, avatarSize / 2, 0, Math.PI * 2);
    ctx.fillStyle = '#E5E5EA';
    ctx.fill();
    ctx.strokeStyle = '#C7C7CC';
    ctx.lineWidth = 2;
    ctx.stroke();
    
    if (this.avatar) {
      // 如果有头像图片，绘制头像
      try {
        ctx.save();
        ctx.beginPath();
        ctx.arc(avatarX + avatarSize / 2, avatarY + avatarSize / 2, avatarSize / 2 - 1, 0, Math.PI * 2);
        ctx.clip();
        ctx.drawImage(this.avatar, avatarX, avatarY, avatarSize, avatarSize);
        ctx.restore();
      } catch (error) {
        console.error('绘制仙友头像失败', error);
        this.drawDefaultAvatar(ctx, avatarX, avatarY, avatarSize);
      }
    } else {
      // 绘制默认头像
      this.drawDefaultAvatar(ctx, avatarX, avatarY, avatarSize);
    }
  }
  
  /**
   * 绘制默认头像
   */
  drawDefaultAvatar(ctx, x, y, size) {
    ctx.font = `${size * 0.4}px Arial`;
    ctx.fillStyle = '#8E8E93';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('👤', x + size / 2, y + size / 2);
  }
  
  /**
   * 绘制星级
   */
  drawStars(ctx) {
    const starSize = 16;
    const starSpacing = 20;
    const totalStarsWidth = this.star * starSpacing - (starSpacing - starSize);
    const startX = this.x + (this.width - totalStarsWidth) / 2;
    const startY = this.y + 120;
    
    ctx.font = `${starSize}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    for (let i = 0; i < 5; i++) {
      const starX = startX + i * starSpacing;
      ctx.fillStyle = i < this.star ? '#FFD700' : '#E5E5EA';
      ctx.fillText('⭐', starX + starSize / 2, startY + starSize / 2);
    }
  }
  
  /**
   * 绘制名字
   */
  drawName(ctx) {
    const nameY = this.y + 160;
    
    ctx.font = 'bold 16px Arial';
    ctx.fillStyle = '#1C1C1E';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    // 限制名字长度
    const maxWidth = this.width - 20;
    let displayName = this.name;
    if (ctx.measureText(displayName).width > maxWidth) {
      while (ctx.measureText(displayName + '...').width > maxWidth && displayName.length > 0) {
        displayName = displayName.slice(0, -1);
      }
      displayName += '...';
    }
    
    ctx.fillText(displayName, this.x + this.width / 2, nameY);
  }
  
  /**
   * 绘制好感度条
   */
  drawFavorabilityBar(ctx) {
    const barWidth = this.width - 40;
    const barHeight = 8;
    const barX = this.x + 20;
    const barY = this.y + 190;
    const borderRadius = 4;
    
    // 绘制背景条
    this.drawRoundedRect(ctx, barX, barY, barWidth, barHeight, borderRadius);
    ctx.fillStyle = '#E5E5EA';
    ctx.fill();
    
    // 绘制进度条
    const progress = Math.min(this.favorability / this.maxFavorability, 1);
    const progressWidth = barWidth * progress;
    
    if (progressWidth > 0) {
      this.drawRoundedRect(ctx, barX, barY, progressWidth, barHeight, borderRadius);
      
      // 好感度渐变色
      const gradient = ctx.createLinearGradient(barX, barY, barX + progressWidth, barY);
      gradient.addColorStop(0, '#FF6B6B');
      gradient.addColorStop(1, '#FF8E8E');
      ctx.fillStyle = gradient;
      ctx.fill();
    }
    
    // 绘制好感度数值
    ctx.font = '12px Arial';
    ctx.fillStyle = '#8E8E93';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'top';
    ctx.fillText(`${this.favorability}/${this.maxFavorability}`, this.x + this.width / 2, barY + barHeight + 8);
  }
  
  /**
   * 绘制选中指示器
   */
  drawSelectedIndicator(ctx) {
    const borderRadius = 12;
    
    // 绘制选中边框
    this.drawRoundedRect(ctx, this.x - 3, this.y - 3, this.width + 6, this.height + 6, borderRadius + 3);
    ctx.strokeStyle = '#007AFF';
    ctx.lineWidth = 4;
    ctx.stroke();
    
    // 绘制选中角标
    const checkSize = 24;
    const checkX = this.x + this.width - checkSize - 8;
    const checkY = this.y + 8;
    
    ctx.beginPath();
    ctx.arc(checkX + checkSize / 2, checkY + checkSize / 2, checkSize / 2, 0, Math.PI * 2);
    ctx.fillStyle = '#007AFF';
    ctx.fill();
    
    ctx.font = `${checkSize * 0.6}px Arial`;
    ctx.fillStyle = '#FFFFFF';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('✓', checkX + checkSize / 2, checkY + checkSize / 2);
  }
  
  /**
   * 绘制圆角矩形
   */
  drawRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();
  }
  
  /**
   * 更新仙友数据
   */
  updateXianyouData(xianyouData) {
    this.xianyouData = xianyouData;
    this.id = xianyouData.id || this.id;
    this.name = xianyouData.name || this.name;
    this.avatar = xianyouData.avatar || this.avatar;
    this.star = xianyouData.star || this.star;
    this.favorability = xianyouData.favorability || this.favorability;
    this.maxFavorability = xianyouData.maxFavorability || this.maxFavorability;
    this.rarity = xianyouData.rarity || this.rarity;
  }
  
  /**
   * 设置选中状态
   */
  setSelected(selected) {
    this.isSelected = selected;
  }
  
  /**
   * 设置位置
   */
  setPosition(x, y) {
    this.x = x;
    this.y = y;
  }
  
  /**
   * 设置可见性
   */
  setVisible(visible) {
    this.visible = visible;
  }
}

export default XianyouCard; 