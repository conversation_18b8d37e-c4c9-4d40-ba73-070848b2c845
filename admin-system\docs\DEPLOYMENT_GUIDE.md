# 修仙六道后台管理系统部署指南

**版本：** v2.1.0  
**更新时间：** 2025年1月28日  
**维护人员：** AI进化论-花生

## 📋 部署概览

本指南详细说明了如何部署修仙六道后台管理系统到生产环境，包括环境准备、配置设置、安全配置和维护指导。

## 🎯 系统要求

### 最低系统要求
- **操作系统**：Windows Server 2019+ / Ubuntu 18.04+ / CentOS 7+
- **Node.js**：18.x 或更高版本
- **内存**：最少 2GB RAM（推荐 4GB+）
- **存储**：最少 10GB 可用空间
- **网络**：稳定的互联网连接

### 推荐系统配置
- **CPU**：4核心或更多
- **内存**：8GB RAM 或更多
- **存储**：SSD 硬盘，50GB+ 可用空间
- **网络**：企业级网络环境
- **备份**：定期数据备份策略

## 🚀 快速部署（生产环境）

### 步骤1：环境准备

#### 1.1 安装Node.js
```bash
# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# CentOS/RHEL
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs

# 验证安装
node --version
npm --version
```

#### 1.2 安装PM2（进程管理器）
```bash
npm install -g pm2
pm2 --version
```

#### 1.3 创建部署目录
```bash
sudo mkdir -p /opt/xiuxian-admin
sudo chown -R $USER:$USER /opt/xiuxian-admin
cd /opt/xiuxian-admin
```

### 步骤2：代码部署

#### 2.1 获取代码
```bash
# 方式1：Git克隆（推荐）
git clone <your-repository-url> .

# 方式2：文件上传
# 将admin-system文件夹上传到 /opt/xiuxian-admin/
```

#### 2.2 安装依赖
```bash
cd /opt/xiuxian-admin/admin-system
npm install --production
```

### 步骤3：配置文件设置

#### 3.1 创建生产环境配置
```bash
cp config.example.js config.js
```

#### 3.2 编辑配置文件
```javascript
// config.js
module.exports = {
  // 服务器配置
  server: {
    port: process.env.PORT || 3000,
    host: '0.0.0.0',
    env: 'production'
  },

  // 数据库配置
  database: {
    // 微信云开发配置
    cloudFunction: {
      env: 'your-cloud-env-id',  // 你的云环境ID
      secretId: process.env.TENCENT_SECRET_ID,
      secretKey: process.env.TENCENT_SECRET_KEY
    }
  },

  // 会话配置
  session: {
    secret: process.env.SESSION_SECRET || 'your-super-secret-key-change-me',
    name: 'xiuxian-admin-session',
    maxAge: 24 * 60 * 60 * 1000, // 24小时
    secure: true, // HTTPS环境设为true
    httpOnly: true
  },

  // 安全配置
  security: {
    rateLimitWindowMs: 15 * 60 * 1000, // 15分钟
    rateLimitMax: 100, // 每个IP最多100次请求
    enableCors: false, // 生产环境建议关闭
    trustProxy: true // 如果使用反向代理，设为true
  },

  // 日志配置
  logging: {
    level: 'info',
    file: '/var/log/xiuxian-admin/app.log',
    maxSize: '20m',
    maxFiles: '14d'
  },

  // 数据表映射
  tables: {
    players: 'players',
    playerData: 'player_data', 
    playerResources: 'player_resources',
    playerTreasures: 'player_treasures',
    playerSkill: 'player_skill',
    skillTemp: 'skill_temp',
    sHeartTemp: 's_heart_temp',
    treasureTemp: 'treasure_tmp',
    mails: 'mails',
    mailTemplates: 'mail_temp',
    playerMails: 'mail_recipients',
    adminLogs: 'admin_logs',
    batchLogs: 'batch_operations'
  }
};
```

#### 3.3 创建环境变量文件
```bash
# 创建 .env 文件
cat > .env << 'EOF'
# 生产环境变量
NODE_ENV=production
PORT=3000

# 会话密钥（必须修改为强密码）
SESSION_SECRET=your-very-secure-session-secret-here

# 腾讯云配置
TENCENT_SECRET_ID=your-tencent-secret-id
TENCENT_SECRET_KEY=your-tencent-secret-key

# 管理员账户（可选，用于初始化）
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-admin-password

# 日志级别
LOG_LEVEL=info
EOF
```

### 步骤4：安全配置

#### 4.1 设置文件权限
```bash
# 设置配置文件权限
chmod 600 config.js .env

# 设置应用目录权限
chown -R $USER:$USER /opt/xiuxian-admin
chmod -R 755 /opt/xiuxian-admin
chmod -R 644 /opt/xiuxian-admin/admin-system/views
```

#### 4.2 创建日志目录
```bash
sudo mkdir -p /var/log/xiuxian-admin
sudo chown -R $USER:$USER /var/log/xiuxian-admin
```

### 步骤5：使用PM2启动应用

#### 5.1 创建PM2配置文件
```bash
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'xiuxian-admin',
    script: 'server.js',
    cwd: '/opt/xiuxian-admin/admin-system',
    instances: 'max', // 或者指定数量，如 2
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development'
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    log_file: '/var/log/xiuxian-admin/combined.log',
    out_file: '/var/log/xiuxian-admin/out.log',
    error_file: '/var/log/xiuxian-admin/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    time: true,
    autorestart: true,
    max_restarts: 10,
    min_uptime: '10s',
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
EOF
```

#### 5.2 启动应用
```bash
# 启动应用
pm2 start ecosystem.config.js --env production

# 保存PM2配置
pm2 save

# 设置PM2开机自启
pm2 startup
# 按照输出的命令执行

# 查看应用状态
pm2 status
pm2 logs xiuxian-admin
```

## 🌐 反向代理配置（Nginx）

### 安装Nginx
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install nginx

# CentOS/RHEL
sudo yum install nginx
```

### Nginx配置
```nginx
# /etc/nginx/sites-available/xiuxian-admin
server {
    listen 80;
    server_name your-domain.com;  # 替换为你的域名
    
    # 强制HTTPS重定向
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;  # 替换为你的域名
    
    # SSL配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' cdn.jsdelivr.net cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' cdn.jsdelivr.net; img-src 'self' data:; font-src 'self' cdn.jsdelivr.net;";
    
    # 反向代理到Node.js应用
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 60s;
        proxy_connect_timeout 60s;
    }
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 日志配置
    access_log /var/log/nginx/xiuxian-admin.access.log;
    error_log /var/log/nginx/xiuxian-admin.error.log;
}
```

### 启用配置
```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/xiuxian-admin /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

## 🔒 SSL证书配置

### 使用Let's Encrypt（免费SSL）
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 设置自动续期
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🔧 数据库连接配置

### 微信云开发配置
1. 登录微信开发者工具或微信开发者平台
2. 获取云环境ID
3. 创建云函数密钥
4. 在配置文件中设置相关参数

### CloudFunctionAdapter配置
确保CloudFunctionAdapter能正确连接到微信云开发环境：
```javascript
// 在config.js中配置
cloudFunction: {
  env: 'your-cloud-env-id',
  secretId: process.env.TENCENT_SECRET_ID,
  secretKey: process.env.TENCENT_SECRET_KEY,
  region: 'ap-shanghai', // 云函数地域
  timeout: 30000 // 超时时间
}
```

## 📊 监控和日志

### 应用监控
```bash
# PM2监控
pm2 monit

# 查看实时日志
pm2 logs xiuxian-admin --lines 100

# 重启应用
pm2 restart xiuxian-admin

# 重新加载配置（无停机）
pm2 reload xiuxian-admin
```

### 系统监控
```bash
# 安装系统监控工具
sudo apt install htop iotop

# 查看系统资源使用
htop
df -h
free -h
```

### 日志管理
```bash
# 设置日志轮转
sudo tee /etc/logrotate.d/xiuxian-admin << 'EOF'
/var/log/xiuxian-admin/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 0640 $USER $USER
    postrotate
        pm2 reloadLogs
    endscript
}
EOF
```

## 🔄 更新和维护

### 应用更新流程
```bash
# 1. 备份当前版本
cd /opt
sudo tar -czf xiuxian-admin-backup-$(date +%Y%m%d-%H%M%S).tar.gz xiuxian-admin/

# 2. 更新代码
cd /opt/xiuxian-admin
git pull origin main  # 或其他分支

# 3. 安装新依赖
cd admin-system
npm install --production

# 4. 重启应用
pm2 restart xiuxian-admin
```

### 备份策略
```bash
# 创建备份脚本
cat > /opt/xiuxian-admin/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/backups/xiuxian-admin"
DATE=$(date +%Y%m%d-%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份应用代码
tar -czf $BACKUP_DIR/app-$DATE.tar.gz -C /opt xiuxian-admin

# 备份配置文件
cp /opt/xiuxian-admin/admin-system/config.js $BACKUP_DIR/config-$DATE.js
cp /opt/xiuxian-admin/admin-system/.env $BACKUP_DIR/env-$DATE

# 清理超过30天的备份
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
find $BACKUP_DIR -name "config-*" -mtime +30 -delete
find $BACKUP_DIR -name "env-*" -mtime +30 -delete

echo "Backup completed: $DATE"
EOF

chmod +x /opt/xiuxian-admin/backup.sh

# 设置定时备份（每天凌晨2点）
echo "0 2 * * * /opt/xiuxian-admin/backup.sh" | sudo crontab -
```

## 🚨 故障排除

### 常见问题

#### 应用无法启动
```bash
# 检查端口占用
sudo netstat -tlnp | grep :3000

# 检查权限
ls -la /opt/xiuxian-admin/admin-system/

# 查看错误日志
pm2 logs xiuxian-admin --err
```

#### 数据库连接失败
1. 检查网络连接
2. 验证云函数密钥
3. 确认云环境ID正确
4. 查看CloudFunctionAdapter日志

#### 内存不足
```bash
# 增加swap空间
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# 永久生效
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
```

## 📞 支持和联系

- **技术支持**：AI进化论-花生
- **问题反馈**：通过项目仓库Issues
- **紧急联系**：请通过预留的紧急联系方式

---

**部署完成时间**：2025年1月28日  
**下次维护计划**：每月第一个周末进行系统维护 