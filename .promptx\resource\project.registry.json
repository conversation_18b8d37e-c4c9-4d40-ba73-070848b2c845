{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-06-30T15:29:29.525Z", "updatedAt": "2025-06-30T15:29:29.526Z", "resourceCount": 7}, "resources": [{"id": "wechat-minigame-expert", "source": "project", "protocol": "role", "name": "Wechat Minigame Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/wechat-minigame-expert/wechat-minigame-expert.role.md", "metadata": {"createdAt": "2025-06-30T15:29:29.525Z", "updatedAt": "2025-06-30T15:29:29.525Z", "scannedAt": "2025-06-30T15:29:29.525Z"}}, {"id": "wechat-minigame-thinking", "source": "project", "protocol": "thought", "name": "Wechat Minigame Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/wechat-minigame-expert/thought/wechat-minigame-thinking.thought.md", "metadata": {"createdAt": "2025-06-30T15:29:29.525Z", "updatedAt": "2025-06-30T15:29:29.525Z", "scannedAt": "2025-06-30T15:29:29.525Z"}}, {"id": "wechat-minigame-development", "source": "project", "protocol": "execution", "name": "Wechat Minigame Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/wechat-minigame-frontend-developer/execution/wechat-minigame-development.execution.md", "metadata": {"createdAt": "2025-06-30T15:29:29.526Z", "updatedAt": "2025-06-30T15:29:29.526Z", "scannedAt": "2025-06-30T15:29:29.526Z"}}, {"id": "wechat-minigame-tech-stack", "source": "project", "protocol": "knowledge", "name": "Wechat Minigame Tech Stack 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/wechat-minigame-expert/knowledge/wechat-minigame-tech-stack.knowledge.md", "metadata": {"createdAt": "2025-06-30T15:29:29.526Z", "updatedAt": "2025-06-30T15:29:29.526Z", "scannedAt": "2025-06-30T15:29:29.526Z"}}, {"id": "wechat-minigame-frontend-developer", "source": "project", "protocol": "role", "name": "Wechat Minigame Frontend Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/wechat-minigame-frontend-developer/wechat-minigame-frontend-developer.role.md", "metadata": {"createdAt": "2025-06-30T15:29:29.526Z", "updatedAt": "2025-06-30T15:29:29.526Z", "scannedAt": "2025-06-30T15:29:29.526Z"}}, {"id": "wechat-minigame-frontend", "source": "project", "protocol": "thought", "name": "Wechat Minigame Frontend 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/wechat-minigame-frontend-developer/thought/wechat-minigame-frontend.thought.md", "metadata": {"createdAt": "2025-06-30T15:29:29.526Z", "updatedAt": "2025-06-30T15:29:29.526Z", "scannedAt": "2025-06-30T15:29:29.526Z"}}, {"id": "ui-optimization", "source": "project", "protocol": "execution", "name": "Ui Optimization 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/wechat-minigame-frontend-developer/execution/ui-optimization.execution.md", "metadata": {"createdAt": "2025-06-30T15:29:29.526Z", "updatedAt": "2025-06-30T15:29:29.526Z", "scannedAt": "2025-06-30T15:29:29.526Z"}}], "stats": {"totalResources": 7, "byProtocol": {"role": 2, "thought": 2, "execution": 2, "knowledge": 1}, "bySource": {"project": 7}}}