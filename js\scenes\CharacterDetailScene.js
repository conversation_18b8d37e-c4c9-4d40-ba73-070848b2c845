/**
 * 角色详情页面场景类
 * 展示角色详细信息
 */
import BaseScene from './BaseScene';
import game from '../../game';

class CharacterDetailScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager) {
    super(ctx, screenWidth, screenHeight, sceneManager);

    // 场景资源
    this.resources = null;

    // 当前角色
    this.character = null;

    // 页面状态
    this.pageTitle = '详情';

    // 人设图对象
    this.characterPortraitImage = null;

    // 初始化UI
    this.initUI();
  }

  // 初始化UI
  initUI() {
    // 新的UI已经在绘制方法中实现，这里不需要创建旧的按钮
    // 人设图将使用已加载的资源
    this.loadCharacterPortrait();
  }

  // 加载人设图
  loadCharacterPortrait() {
    try {
      // 直接使用game.resourceLoader中已加载的character1资源
      if (game.resourceLoader && game.resourceLoader.resources && game.resourceLoader.resources.character1) {
        console.log('使用已加载的人设图资源');
        this.characterPortraitImage = game.resourceLoader.resources.character1;
        this.needsRedraw = true;
      } else {
        console.warn('人设图资源未找到，尝试从resources获取');
        // 尝试从this.resources获取（如果场景有资源引用）
        if (this.resources && this.resources.character1) {
          this.characterPortraitImage = this.resources.character1;
          this.needsRedraw = true;
        } else {
          console.error('无法获取人设图资源');
          this.characterPortraitImage = null;
        }
      }
    } catch (error) {
      console.error('加载人设图失败:', error);
      this.characterPortraitImage = null;
    }
  }

  // 场景显示时的回调
  onShow(params) {
    try {
      console.log("CharacterDetailScene onShow", params);
      this.visible = true;

      // 确保能获取到游戏资源
      this.resources = game.resourceLoader.resources;

      // 清空UI元素
      this.clearUIElements();

      // 可以接受character对象或characterId
      if (params && params.character) {
        // 直接使用传入的character对象
        this.character = params.character;
        this.characterId = params.character.id;
        console.log(`成功加载角色详情页，角色名称: ${this.character.name}`);
      } else if (params && params.characterId) {
        // 从characterId获取角色数据
        this.characterId = params.characterId;
        this.character = game.gameStateManager.getCharacterById(this.characterId);

        if (!this.character) {
          console.error(`找不到ID为${this.characterId}的角色`);
          return;
        }

        console.log(`成功加载角色详情页，角色ID: ${this.characterId}`);
      } else {
        console.error('角色详情页缺少必要参数：characterId或character');
        return;
      }

      // 初始化UI
      this.initUI();


    } catch (error) {
      console.error("CharacterDetailScene.onShow 出错:", error);
    }
  }









  // 处理触摸结束事件
  handleTouchEnd(x, y) {
    if (this.character) {
      // 检查是否点击了属性详情按钮
      if (this.attributeDetailButton && this.isPointInButton(x, y, this.attributeDetailButton)) {
        this.showAllAttributes();
        return true;
      }

      // 检查是否点击了返回按钮
      if (this.backButton && this.isPointInButton(x, y, this.backButton)) {
        this.sceneManager.showScene('main');
        return true;
      }

      // 检查是否点击了装备槽位
      const equipSlotIndex = this.getEquipSlotAtPosition(x, y);
      if (equipSlotIndex !== -1) {
        console.log(`点击了装备槽位: ${equipSlotIndex}`);
        // 这里可以添加装备槽位点击逻辑
        return true;
      }

      // 检查是否点击了技能槽位
      const skillSlotIndex = this.getSkillSlotAtPosition(x, y);
      if (skillSlotIndex !== -1) {
        console.log(`点击了技能槽位: ${skillSlotIndex}`);
        // 这里可以添加技能槽位点击逻辑
        return true;
      }
    }

    return false;
  }

  // 获取指定位置的装备槽位
  getEquipSlotAtPosition(x, y) {
    if (!this.equipmentRect) return -1;

    const slotSize = 35; // 更新为新的槽位尺寸
    const slotMargin = 8;
    const totalHeight = this.screenHeight / 5;
    const slotSpacing = (totalHeight - slotSize * 3) / 2;

    for (let i = 0; i < 6; i++) {
      const row = i % 3;
      const col = Math.floor(i / 3);
      const slotX = this.equipmentRect.x + col * (slotSize + slotMargin);
      const slotY = this.equipmentRect.y + row * (slotSize + slotSpacing);

      // 确保槽位在屏幕内
      if (slotX + slotSize > this.screenWidth - 10) {
        continue;
      }

      if (x >= slotX && x <= slotX + slotSize && y >= slotY && y <= slotY + slotSize) {
        return i;
      }
    }

    return -1;
  }

  // 获取指定位置的技能槽位
  getSkillSlotAtPosition(x, y) {
    if (!this.skillRect) return -1;

    const slotSize = 35; // 更新为新的槽位尺寸
    const slotMargin = 8;

    for (let i = 0; i < 6; i++) {
      const row = i % 3;
      const col = Math.floor(i / 3);
      const slotX = this.skillRect.x + col * (slotSize + slotMargin);
      const slotY = this.skillRect.y + row * (slotSize + slotMargin);

      // 确保槽位在屏幕内
      if (slotX + slotSize > this.screenWidth - 10) {
        continue;
      }

      if (x >= slotX && x <= slotX + slotSize && y >= slotY && y <= slotY + slotSize) {
        return i;
      }
    }

    return -1;
  }

  // 检查点是否在按钮内
  isPointInButton(x, y, button) {
    return x >= button.x && x <= button.x + button.width &&
           y >= button.y && y <= button.y + button.height;
  }

  // 显示所有属性弹窗
  showAllAttributes() {
    const attributes = this.character.getAttributes();

    // 创建属性详情弹窗内容
    const attributeList = [
      `生命值: ${Math.floor(attributes.hp)}`,
      `法力值: ${Math.floor(attributes.hp * 0.8)}`,
      `攻击力: ${Math.floor(attributes.attack)}`,
      `防御力: ${Math.floor(attributes.defense)}`,
      `暴击率: ${(attributes.critRate * 100).toFixed(1)}%`,
      `暴击伤害: ${(attributes.critDamage * 100).toFixed(1)}%`,
      `命中率: ${(attributes.hitRate * 100).toFixed(1)}%`,
      `闪避率: ${(attributes.dodgeRate * 100).toFixed(1)}%`,
      `攻击速度: ${attributes.attackSpeed.toFixed(1)}`,
      `移动速度: ${attributes.moveSpeed.toFixed(1)}`
    ];

    // 显示弹窗
    wx.showModal({
      title: '角色属性详情',
      content: attributeList.join('\n'),
      showCancel: false,
      confirmText: '确定'
    });
  }

  // 子类实现的绘制逻辑
  drawScene() {
    if (!this.character) {
      return;
    }

    // 绘制淡蓝色背景
    this.drawLightBlueBackground();

    // 绘制人设图（中间层）
    this.drawCharacterPortrait();

    // 绘制装备栏（上层）
    this.drawEquipmentSlots();

    // 绘制技能栏（上层）
    this.drawSkillSlots();

    // 绘制属性条（上层）
    this.drawAttributeBars();

    // 绘制属性区域（上层）
    this.drawAttributeSection();

    // 绘制返回按钮
    this.drawBackButton();
  }

  // 绘制淡蓝色背景
  drawLightBlueBackground() {
    // 淡蓝色背景
    this.ctx.fillStyle = '#E6F3FF'; // 淡蓝色
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);

    // TODO: 未来添加背景图时，取消注释下面的代码并设置正确的图片路径
    // if (this.backgroundImage) {
    //   this.ctx.drawImage(this.backgroundImage, 0, 0, this.screenWidth, this.screenHeight);
    // }
  }

  // 绘制人设图（中间层）
  drawCharacterPortrait() {
    // 计算人设图位置和尺寸
    const portraitWidth = this.screenWidth * 2 / 3;  // 2/3屏幕宽度
    const portraitHeight = this.screenHeight * 2 / 3; // 2/3屏幕高度
    const portraitX = (this.screenWidth - portraitWidth) / 2 - this.screenWidth * 0.1; // 居中靠左
    const portraitY = (this.screenHeight - portraitHeight) / 2;

    // 绘制人设图背景框
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
    this.ctx.fillRect(portraitX, portraitY, portraitWidth, portraitHeight);

    // 绘制边框
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(portraitX, portraitY, portraitWidth, portraitHeight);

    // 绘制人设图
    if (this.characterPortraitImage) {
      // 如果图片已加载，绘制实际的人设图
      this.ctx.drawImage(
        this.characterPortraitImage,
        portraitX, portraitY,
        portraitWidth, portraitHeight
      );
    } else {
      // 图片未加载时显示占位符
      this.ctx.fillStyle = '#666';
      this.ctx.font = 'bold 16px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText(
        '人设图加载中...',
        portraitX + portraitWidth / 2,
        portraitY + portraitHeight / 2
      );
    }

    // 保存人设图位置信息供其他方法使用
    this.portraitRect = {
      x: portraitX,
      y: portraitY,
      width: portraitWidth,
      height: portraitHeight
    };
  }

  // 绘制星空效果
  drawStarField() {
    // 使用固定的星星位置，避免每帧重新计算
    if (!this.stars) {
      this.stars = [];
      for (let i = 0; i < 30; i++) {
        this.stars.push({
          x: Math.random() * this.screenWidth,
          y: Math.random() * this.screenHeight,
          size: Math.random() * 1.5 + 0.5
        });
      }
    }

    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
    for (const star of this.stars) {
      this.ctx.beginPath();
      this.ctx.arc(star.x, star.y, star.size, 0, Math.PI * 2);
      this.ctx.fill();
    }
  }

  // 绘制顶部导航栏
  drawHeader() {
    const headerHeight = 80;

    // 绘制顶部导航栏背景（透明度更高的渐变）
    const headerGradient = this.ctx.createLinearGradient(0, 0, 0, headerHeight);
    headerGradient.addColorStop(0, 'rgba(0, 0, 0, 0.8)');
    headerGradient.addColorStop(1, 'rgba(0, 0, 0, 0.4)');
    this.ctx.fillStyle = headerGradient;
    this.ctx.fillRect(0, 0, this.screenWidth, headerHeight);

    // 绘制页面标题（更优雅的字体）
    this.ctx.font = 'bold 28px Arial';
    this.ctx.fillStyle = '#FFD700';
    this.ctx.textAlign = 'left';
    this.ctx.strokeStyle = 'rgba(0, 0, 0, 0.5)';
    this.ctx.lineWidth = 2;
    this.ctx.strokeText(this.pageTitle, 20, headerHeight / 2 + 10);
    this.ctx.fillText(this.pageTitle, 20, headerHeight / 2 + 10);

    // 绘制资源信息（更美观的布局）
    const player = game.gameStateManager.getPlayer();
    const resourceY = headerHeight / 2;
    const iconSize = 24;

    // 绘制仙玉（改进的图标和文字）
    const xianyuX = this.screenWidth / 2;
    this.drawResourceIcon(xianyuX, resourceY - iconSize / 2, iconSize, '#FFD700', '仙');
    this.ctx.font = 'bold 16px Arial';
    this.ctx.fillStyle = '#FFD700';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`${player.resources.xianyu}`, xianyuX + iconSize + 8, resourceY + 6);

    // 绘制灵石（改进的图标和文字）
    const lingshiX = this.screenWidth / 2 + 120;
    this.drawResourceIcon(lingshiX, resourceY - iconSize / 2, iconSize, '#C0C0C0', '灵');
    this.ctx.font = 'bold 16px Arial';
    this.ctx.fillStyle = '#C0C0C0';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`${player.resources.lingshi}`, lingshiX + iconSize + 8, resourceY + 6);

    // 绘制历练点（新增）
    const lianlidianX = this.screenWidth / 2 + 240;
    this.drawResourceIcon(lianlidianX, resourceY - iconSize / 2, iconSize, '#90EE90', '练');
    this.ctx.font = 'bold 16px Arial';
    this.ctx.fillStyle = '#90EE90';
    this.ctx.textAlign = 'left';
    this.ctx.fillText(`${player.resources.lianlidian || 0}`, lianlidianX + iconSize + 8, resourceY + 6);
  }

  // 绘制资源图标
  drawResourceIcon(x, y, size, color, text) {
    // 绘制圆形背景
    this.ctx.fillStyle = color;
    this.ctx.beginPath();
    this.ctx.arc(x + size / 2, y + size / 2, size / 2, 0, Math.PI * 2);
    this.ctx.fill();

    // 绘制边框
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
    this.ctx.lineWidth = 2;
    this.ctx.stroke();

    // 绘制文字
    this.ctx.font = 'bold 14px Arial';
    this.ctx.fillStyle = '#000000';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(text, x + size / 2, y + size / 2);
  }

  // 绘制角色信息卡片
  drawCharacterCard() {
    const headerHeight = 80;
    const cardY = headerHeight + 20;
    const cardHeight = 280;
    const cardWidth = this.screenWidth - 40;
    const cardX = 20;

    // 绘制卡片背景（带圆角和阴影效果）
    this.drawRoundedCard(cardX, cardY, cardWidth, cardHeight);

    // 绘制角色头像区域
    this.drawCharacterAvatar(cardX + 20, cardY + 20, 120, 120);

    // 绘制角色信息区域
    this.drawCharacterInfo(cardX + 160, cardY + 20, cardWidth - 180, 120);

    // 绘制角色属性预览
    this.drawCharacterPreview(cardX + 20, cardY + 160, cardWidth - 40, 100);
  }

  // 绘制圆角卡片
  drawRoundedCard(x, y, width, height) {
    const radius = 15;

    // 绘制阴影
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    this.drawRoundedRect(x + 3, y + 3, width, height, radius);
    this.ctx.fill();

    // 绘制卡片背景渐变
    const cardGradient = this.ctx.createLinearGradient(x, y, x, y + height);
    cardGradient.addColorStop(0, 'rgba(40, 40, 80, 0.9)');
    cardGradient.addColorStop(1, 'rgba(20, 20, 40, 0.9)');

    this.ctx.fillStyle = cardGradient;
    this.drawRoundedRect(x, y, width, height, radius);
    this.ctx.fill();

    // 绘制卡片边框
    this.ctx.strokeStyle = 'rgba(255, 215, 0, 0.6)';
    this.ctx.lineWidth = 2;
    this.ctx.stroke();
  }

  // 绘制圆角矩形的辅助方法
  drawRoundedRect(x, y, width, height, radius) {
    this.ctx.beginPath();
    this.ctx.moveTo(x + radius, y);
    this.ctx.lineTo(x + width - radius, y);
    this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    this.ctx.lineTo(x + width, y + height - radius);
    this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    this.ctx.lineTo(x + radius, y + height);
    this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    this.ctx.lineTo(x, y + radius);
    this.ctx.quadraticCurveTo(x, y, x + radius, y);
    this.ctx.closePath();
  }

  // 绘制角色头像
  drawCharacterAvatar(x, y, width, height) {
    const quality = this.character.quality || 0;
    const qualityColors = {
      0: 'rgba(255, 255, 255, 0.3)',   // 白色
      1: 'rgba(0, 255, 0, 0.3)',      // 绿色
      2: 'rgba(0, 0, 255, 0.3)',      // 蓝色
      3: 'rgba(255, 0, 255, 0.3)',    // 紫色
      4: 'rgba(255, 215, 0, 0.3)'     // 金色
    };
    const qualityColor = qualityColors[quality] || 'rgba(255, 255, 255, 0.3)';

    // 绘制头像背景光晕
    const glowGradient = this.ctx.createRadialGradient(
      x + width / 2, y + height / 2, 10,
      x + width / 2, y + height / 2, width / 2
    );
    glowGradient.addColorStop(0, qualityColor.replace('0.3', '0.6'));
    glowGradient.addColorStop(1, 'rgba(0, 0, 0, 0)');

    this.ctx.fillStyle = glowGradient;
    this.ctx.beginPath();
    this.ctx.arc(x + width / 2, y + height / 2, width / 2, 0, Math.PI * 2);
    this.ctx.fill();

    // 绘制头像边框
    this.ctx.strokeStyle = qualityColor.replace('0.3', '0.8');
    this.ctx.lineWidth = 3;
    this.ctx.beginPath();
    this.ctx.arc(x + width / 2, y + height / 2, width / 2 - 5, 0, Math.PI * 2);
    this.ctx.stroke();

    // 绘制角色名称（如果没有头像图片）
    this.ctx.font = 'bold 32px Arial';
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(this.character.name, x + width / 2, y + height / 2);
  }





  // 绘制装备栏（新设计）
  drawEquipmentSlots() {
    if (!this.portraitRect) return;

    // 装备栏位置：对齐人设图顶部，在人设图右边
    const equipStartX = this.portraitRect.x + this.portraitRect.width + 10;
    const equipStartY = this.portraitRect.y;
    const slotSize = 35; // 缩小装备槽尺寸
    const slotMargin = 8;
    const totalHeight = this.screenHeight / 5; // 总长度约五分之一屏幕长度
    const slotSpacing = (totalHeight - slotSize * 3) / 2; // 3行装备的间距

    // 装备槽位名称
    const slotNames = ['武器', '头盔', '护甲', '护腕', '鞋子', '饰品'];
    const equipTypeKeys = ['weapon', 'helmet', 'armor', 'bracers', 'boots', 'accessory'];

    for (let i = 0; i < 6; i++) {
      const row = i % 3; // 3行
      const col = Math.floor(i / 3); // 2列
      const x = equipStartX + col * (slotSize + slotMargin);
      const y = equipStartY + row * (slotSize + slotSpacing);

      // 确保不超出屏幕边界
      if (x + slotSize > this.screenWidth - 10) {
        continue; // 跳过超出屏幕的槽位
      }

      // 绘制装备槽位背景
      this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
      this.ctx.fillRect(x, y, slotSize, slotSize);

      // 绘制装备槽位边框
      this.ctx.strokeStyle = '#4A90E2';
      this.ctx.lineWidth = 2;
      this.ctx.strokeRect(x, y, slotSize, slotSize);

      // 获取当前槽位的装备
      const equipment = this.character.equipment ? this.character.equipment[equipTypeKeys[i]] : null;

      // 绘制装备图标或占位符
      if (equipment) {
        // 绘制装备品质背景
        const qualityColors = {
          0: 'rgba(255, 255, 255, 0.5)',
          1: 'rgba(0, 255, 0, 0.5)',
          2: 'rgba(0, 0, 255, 0.5)',
          3: 'rgba(255, 0, 255, 0.5)',
          4: 'rgba(255, 215, 0, 0.5)'
        };
        this.ctx.fillStyle = qualityColors[equipment.quality] || 'rgba(255, 255, 255, 0.5)';
        this.ctx.fillRect(x + 2, y + 2, slotSize - 4, slotSize - 4);

        // 绘制装备名称
        this.ctx.fillStyle = '#333';
        this.ctx.font = 'bold 8px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText(equipment.name.substring(0, 2), x + slotSize / 2, y + slotSize / 2);
      } else {
        // 绘制空槽位提示
        this.ctx.fillStyle = '#999';
        this.ctx.font = 'bold 12px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText('+', x + slotSize / 2, y + slotSize / 2);
      }

      // 绘制槽位名称（在装备槽下方小字显示）
      this.ctx.fillStyle = '#666';
      this.ctx.font = '8px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'top';
      this.ctx.fillText(slotNames[i], x + slotSize / 2, y + slotSize + 2);
    }

    // 保存装备栏位置信息
    this.equipmentRect = {
      x: equipStartX,
      y: equipStartY,
      width: slotSize * 2 + slotMargin,
      height: totalHeight
    };
  }

  // 绘制技能栏
  drawSkillSlots() {
    if (!this.equipmentRect) return;

    // 技能栏位置：在装备栏下方
    const skillStartX = this.equipmentRect.x;
    const skillStartY = this.equipmentRect.y + this.equipmentRect.height + 20;
    const slotSize = 35; // 与装备栏相同的尺寸
    const slotMargin = 8;

    // 技能槽位名称
    const skillNames = ['普攻', '技能1', '技能2', '技能3', '技能4', '技能5'];

    for (let i = 0; i < 6; i++) {
      const row = i % 3; // 3行
      const col = Math.floor(i / 3); // 2列
      const x = skillStartX + col * (slotSize + slotMargin);
      const y = skillStartY + row * (slotSize + slotMargin);

      // 确保不超出屏幕边界
      if (x + slotSize > this.screenWidth - 10) {
        continue; // 跳过超出屏幕的槽位
      }

      // 绘制技能槽位背景
      this.ctx.fillStyle = 'rgba(200, 220, 255, 0.8)'; // 淡蓝色背景
      this.ctx.fillRect(x, y, slotSize, slotSize);

      // 绘制技能槽位边框
      this.ctx.strokeStyle = '#6A5ACD'; // 紫色边框
      this.ctx.lineWidth = 2;
      this.ctx.strokeRect(x, y, slotSize, slotSize);

      // 获取角色技能（这里暂时用占位符）
      const skill = this.character.skills && this.character.skills[i];

      if (skill) {
        // 绘制技能图标或名称
        this.ctx.fillStyle = '#333';
        this.ctx.font = 'bold 8px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText(skill.name.substring(0, 2), x + slotSize / 2, y + slotSize / 2);

        // 绘制技能等级
        this.ctx.fillStyle = '#FFD700';
        this.ctx.font = 'bold 6px Arial';
        this.ctx.textAlign = 'right';
        this.ctx.textBaseline = 'bottom';
        this.ctx.fillText(`Lv${skill.level || 1}`, x + slotSize - 2, y + slotSize - 2);
      } else {
        // 绘制空槽位提示
        this.ctx.fillStyle = '#999';
        this.ctx.font = 'bold 12px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText(i === 0 ? '⚔' : '+', x + slotSize / 2, y + slotSize / 2);
      }

      // 绘制技能名称（在技能槽下方小字显示）
      this.ctx.fillStyle = '#666';
      this.ctx.font = '8px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'top';
      this.ctx.fillText(skillNames[i], x + slotSize / 2, y + slotSize + 2);
    }

    // 保存技能栏位置信息
    this.skillRect = {
      x: skillStartX,
      y: skillStartY,
      width: slotSize * 2 + slotMargin,
      height: slotSize * 3 + slotMargin * 2
    };
  }

  // 绘制属性条（气血和法力）
  drawAttributeBars() {
    if (!this.portraitRect) return;

    // 属性条位置：在人设图底部，距离屏幕左右各留十分之一的距离
    const barStartX = this.screenWidth * 0.1; // 左边留十分之一
    const barStartY = this.portraitRect.y + this.portraitRect.height - 80;
    const barWidth = this.screenWidth * 0.8; // 宽度为屏幕的80%
    const barHeight = 20;
    const barSpacing = 10;

    // 获取角色属性
    const attributes = this.character.getAttributes();
    const currentHp = this.character.currentHp || attributes.hp;
    const maxHp = attributes.hp;
    const currentMp = Math.floor(attributes.hp * 0.8); // 法力值为生命值的80%
    const maxMp = Math.floor(attributes.hp * 0.8);

    // 绘制气血条
    this.drawAttributeBar(
      barStartX, barStartY,
      barWidth, barHeight,
      currentHp, maxHp,
      '气血', '#FF6B6B'
    );

    // 绘制法力条
    this.drawAttributeBar(
      barStartX, barStartY + barHeight + barSpacing,
      barWidth, barHeight,
      currentMp, maxMp,
      '法力', '#4ECDC4'
    );
  }

  // 绘制单个属性条
  drawAttributeBar(x, y, width, height, current, max, label, color) {
    // 绘制属性名称（左上角）
    this.ctx.fillStyle = '#333';
    this.ctx.font = 'bold 12px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.textBaseline = 'bottom';
    this.ctx.fillText(label, x, y - 2);

    // 绘制属性数值（右上角）
    this.ctx.textAlign = 'right';
    this.ctx.fillText(`${current}/${max}`, x + width, y - 2);

    // 绘制属性条背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    this.ctx.fillRect(x, y, width, height);

    // 绘制属性条边框
    this.ctx.strokeStyle = '#333';
    this.ctx.lineWidth = 1;
    this.ctx.strokeRect(x, y, width, height);

    // 绘制属性条填充
    const progress = Math.min(1, current / max);
    this.ctx.fillStyle = color;
    this.ctx.fillRect(x + 1, y + 1, (width - 2) * progress, height - 2);

    // 绘制属性条光泽效果
    const gradient = this.ctx.createLinearGradient(x, y, x, y + height);
    gradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
    gradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.1)');
    gradient.addColorStop(1, 'rgba(0, 0, 0, 0.1)');
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(x + 1, y + 1, (width - 2) * progress, height - 2);
  }

  // 绘制属性区域
  drawAttributeSection() {
    if (!this.portraitRect) return;

    // 属性区域位置：在人设图下方靠左
    const attrStartX = this.portraitRect.x;
    const attrStartY = this.portraitRect.y + this.portraitRect.height + 20;

    // 绘制"属性"标题和按钮
    this.ctx.fillStyle = '#333';
    this.ctx.font = 'bold 18px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.textBaseline = 'top';
    this.ctx.fillText('属性', attrStartX, attrStartY);

    // 绘制属性详情按钮
    const buttonX = attrStartX + 60;
    const buttonY = attrStartY;
    const buttonWidth = 60;
    const buttonHeight = 25;

    this.ctx.fillStyle = 'rgba(74, 144, 226, 0.8)';
    this.ctx.fillRect(buttonX, buttonY, buttonWidth, buttonHeight);

    this.ctx.strokeStyle = '#4A90E2';
    this.ctx.lineWidth = 1;
    this.ctx.strokeRect(buttonX, buttonY, buttonWidth, buttonHeight);

    this.ctx.fillStyle = '#fff';
    this.ctx.font = '12px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText('详情', buttonX + buttonWidth / 2, buttonY + buttonHeight / 2);

    // 保存按钮位置信息
    this.attributeDetailButton = {
      x: buttonX,
      y: buttonY,
      width: buttonWidth,
      height: buttonHeight
    };

    // 绘制基础属性
    const attributes = this.character.getAttributes();
    const basicAttrs = [
      { name: '攻击', value: Math.floor(attributes.attack), color: '#FF6B6B' },
      { name: '防御', value: Math.floor(attributes.defense), color: '#4ECDC4' },
      { name: '暴击率', value: `${(attributes.critRate * 100).toFixed(1)}%`, color: '#FFE66D' },
      { name: '暴击伤害', value: `${(attributes.critDamage * 100).toFixed(1)}%`, color: '#95E1D3' }
    ];

    const attrItemY = attrStartY + 35;
    const attrItemHeight = 25;

    basicAttrs.forEach((attr, index) => {
      const y = attrItemY + index * attrItemHeight;

      // 绘制属性名称
      this.ctx.fillStyle = '#666';
      this.ctx.font = '14px Arial';
      this.ctx.textAlign = 'left';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText(attr.name + ':', attrStartX, y);

      // 绘制属性值
      this.ctx.fillStyle = attr.color;
      this.ctx.font = 'bold 14px Arial';
      this.ctx.textAlign = 'left';
      this.ctx.fillText(attr.value, attrStartX + 80, y);
    });
  }

  // 绘制返回按钮
  drawBackButton() {
    const buttonX = 20;
    const buttonY = 20;
    const buttonWidth = 80;
    const buttonHeight = 40;

    // 绘制按钮背景
    this.ctx.fillStyle = 'rgba(74, 144, 226, 0.9)';
    this.ctx.fillRect(buttonX, buttonY, buttonWidth, buttonHeight);

    // 绘制按钮边框
    this.ctx.strokeStyle = '#4A90E2';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(buttonX, buttonY, buttonWidth, buttonHeight);

    // 绘制按钮文字
    this.ctx.fillStyle = '#fff';
    this.ctx.font = 'bold 16px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText('返回', buttonX + buttonWidth / 2, buttonY + buttonHeight / 2);

    // 保存按钮位置信息
    this.backButton = {
      x: buttonX,
      y: buttonY,
      width: buttonWidth,
      height: buttonHeight
    };
  }







  // 显示所有属性弹窗
  showAllAttributes() {
    const attributes = this.character.getAttributes();

    // 创建属性详情弹窗内容
    const attributeList = [
      `生命值: ${Math.floor(attributes.hp)}`,
      `法力值: ${Math.floor(attributes.hp * 0.8)}`,
      `攻击力: ${Math.floor(attributes.attack)}`,
      `防御力: ${Math.floor(attributes.defense)}`,
      `暴击率: ${(attributes.critRate * 100).toFixed(1)}%`,
      `暴击伤害: ${(attributes.critDamage * 100).toFixed(1)}%`,
      `命中率: ${(attributes.hitRate * 100).toFixed(1)}%`,
      `闪避率: ${(attributes.dodgeRate * 100).toFixed(1)}%`,
      `攻击速度: ${attributes.attackSpeed.toFixed(1)}`,
      `移动速度: ${attributes.moveSpeed.toFixed(1)}`
    ];

    // 显示弹窗
    wx.showModal({
      title: '角色属性详情',
      content: attributeList.join('\n'),
      showCancel: false,
      confirmText: '确定'
    });
  }




}

export default CharacterDetailScene;