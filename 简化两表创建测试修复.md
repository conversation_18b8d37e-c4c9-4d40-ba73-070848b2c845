# 简化两表创建测试修复

## 问题背景

为了方便测试，用户希望新玩家暂时只创建`players`和`player_res`表的数据，而不是之前的5个表。

## 修改内容

### 1. 简化createPlayer函数

**修改前**：创建5个表
- `players` - 玩家基础信息
- `player_resources` - 玩家资源
- `player_dongfu` - 洞府系统
- `player_arena` - 竞技场数据
- `player_sword_bones` - 剑骨系统

**修改后**：只创建2个表
- ✅ `players` - 玩家基础信息
- ✅ `player_res` - 玩家资源（注意表名是player_res而不是player_resources）

### 2. 修正表名和字段

根据`database_design_optimized.md`文档：
- 表名：`player_res`（不是`player_resources`）
- 时间字段：`createdAt`和`updatedAt`（不是`created_at`和`updated_at`）

### 3. 增强日志输出

```javascript
console.log('开始创建新玩家，openid:', openid)
console.log('创建参数:', JSON.stringify(params))
console.log('玩家不存在，开始创建新玩家数据...')
console.log('创建players表数据:', JSON.stringify(playerData))
console.log('已创建玩家基础信息表(players)数据，ID:', playerResult._id)
console.log('已创建玩家资源表(player_res)数据')
console.log('玩家创建完成，playerId:', playerResult._id)
```

## 简化后的数据结构

### players表数据
```javascript
const playerData = {
  _openid: openid,
  nickname: nickname,          // 来自userInfo
  avatar_url: avatarUrl,       // 来自userInfo
  server_id: 1,
  level: 1,
  exp: 0,
  power: 0,
  cultivation_realm: '炼气期一层',
  dongfu_level: 1,
  vip_level: 0,
  total_recharge: 0,
  last_vip_reward_time: null,
  last_login_time: db.serverDate(),
  last_offline_time: null,
  registration_time: db.serverDate(),
  formation: [],
  game_settings: {},
  created_at: db.serverDate(),
  updated_at: db.serverDate()
}
```

### player_res表数据
```javascript
const resourcesData = {
  _openid: openid,
  xianyu: 1000,              // 仙玉
  lingshi: 1000,             // 灵石
  sword_intent: 0,           // 剑意
  lianlidian: 100,           // 历练点
  spirit_stone: 0,           // 强化石
  tiangang_stone: 0,         // 天罡石
  xiuwei_point: 0,           // 修为点
  arena_point: 0,            // 竞技场积分
  guild_contribution: 0,     // 公会贡献
  createdAt: db.serverDate(),
  updatedAt: db.serverDate()
}
```

## 测试验证

### 测试步骤

1. **重新启动游戏**（确保云函数更新生效）
2. **点击"保存数据"按钮**
3. **观察控制台详细日志**
4. **检查数据库后台只有两个表**

### 预期日志输出

**云函数日志**：
```
开始创建新玩家，openid: oc0c842...
创建参数: {"nickname":"开发者","avatarUrl":"...","serverName":"青云门"}
玩家不存在，开始创建新玩家数据...
创建players表数据: {"_openid":"oc0c842...","nickname":"开发者",...}
已创建玩家基础信息表(players)数据，ID: xxx
已创建玩家资源表(player_res)数据
玩家创建完成，playerId: xxx
```

**客户端反馈**：
```javascript
{
  synced: true,
  timestamp: 1749364887817,
  message: "玩家创建成功 (包含players和player_res表)",
  operation: "create",
  playerId: "xxx",
  tables_created: ["players", "player_res"]
}
```

### 数据库验证

应该在以下**2个表**中看到数据：

1. **players表**：
   - `nickname`: "开发者"
   - `level`: 1
   - `power`: 0
   - `cultivation_realm`: "炼气期一层"
   - 等其他基础字段

2. **player_res表**：
   - `xianyu`: 1000
   - `lingshi`: 1000  
   - `sword_intent`: 0
   - `lianlidian`: 100
   - 等其他资源字段

## 优势

1. **测试简化**：只需关注2个表，便于快速验证
2. **错误定位**：问题更容易定位和修复
3. **渐进开发**：验证基础功能后再扩展其他表
4. **事务安全**：仍使用数据库事务确保一致性

## 后续扩展

测试成功后，可以逐步添加其他表：
1. 先确保`players`和`player_res`表正常
2. 再添加`player_dongfu`洞府表
3. 然后添加`player_arena`竞技场表  
4. 最后添加`player_sword_bones`剑骨表

这种渐进式的方法有助于逐步验证每个功能模块的数据创建。 