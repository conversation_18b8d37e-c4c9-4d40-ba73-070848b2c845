# 修仙六道游戏数据库表设计 - 优化版

## 设计原则

1. **模块化设计**：每个功能模块对应相应的数据表
2. **性能优化**：合理使用索引，减少联表查询
3. **扩展性强**：预留字段便于功能扩展
4. **数据一致性**：确保关键数据的完整性约束
5. **云开发适配**：针对微信小游戏云开发特点设计

---

## 核心数据表

### 1. 玩家基础信息表 (players)

| 字段名 | 数据类型 | 默认值 | 描述 | 索引 |
|--------|----------|--------|------|------|
| _id | String | 自动生成 | 记录ID | 主键 |
| _openid | String | - | 微信用户标识 | 唯一索引 |
| nickname | String | '修仙者' | 玩家昵称 | - |
| avatar_url | String | '' | 头像URL | - |
| server_id | Number | 1 | 服务器ID | 索引 |
| level | Number | 1 | 玩家等级 | 索引 |
| exp | Number | 0 | 玩家经验值 | - |
| power | Number | 0 | 总战力 | 索引 |
| cultivation_realm | String | '炼气期一层' | 主修境界 | 索引 |
| dongfu_level | Number | 1 | 洞府等级 | - |
| vip_level | Number | 0 | VIP等级 | 索引 |
| total_recharge | Number | 0 | 累计充值(分) | - |
| last_vip_reward_time | Date | null | 上次VIP奖励时间 | - |
| last_login_time | Date | 当前时间 | 上次登录时间 | 索引 |
| last_offline_time | Date | null | 上次离线时间 | - |
| registration_time | Date | 当前时间 | 注册时间 | - |
| game_settings | Object | {} | 游戏设置 | - |
| createdAt | Date | 当前时间 | 创建时间 | 索引 |
| updatedAt | Date | 当前时间 | 更新时间 | 索引 |

### 2. 玩家资源表 (player_res)

| 字段名 | 数据类型 | 默认值 | 描述 | 索引 |
|--------|----------|--------|------|------|
| _id | String | 自动生成 | 记录ID | 主键 |
| _openid | String | - | 玩家openid | 唯一索引 |
| xianyu | Number | 1000 | 仙玉(高级货币) | - |
| lingshi | Number | 1000 | 灵石(基础货币) | - |
| sword_intent | Number | 0 | 剑意(剑心系统) | - |
| lianlidian | Number | 100 | 历练点(技能升级) | - |
| spirit_stone | Number | 0 | 强化石(古宝升级) | - |
| tiangang_stone | Number | 0 | 天罡石(高级材料) | - |
| xiuwei_point | Number | 0 | 修为点(境界突破) | - |
| arena_point | Number | 0 | 竞技场积分 | - |
| guild_contribution | Number | 0 | 公会贡献 | - |
| createdAt | Date | 当前时间 | 创建时间 | - |
| updatedAt | Date | 当前时间 | 更新时间 | - |

### 3. 玩家古宝表 (player_treasures)

| 字段名 | 数据类型 | 默认值 | 描述 | 索引 |
|--------|----------|--------|------|------|
| _id | String | 自动生成 | 记录ID | 主键 |
| _openid | String | - | 玩家openid | 索引 |
| treasure_id | String | - | 古宝模板ID | 索引 |
| name | String | - | 古宝名称 | - |
| category | String | - | 分类(weapon/artifact/talisman) | 索引 |
| rarity | Number | 1 | 稀有度(1-5) | 索引 |
| level | Number | 1 | 等级 | - |
| max_level | Number | 100 | 最大等级 | - |
| star | Number | 0 | 星级(0-5) | - |
| base_attributes | Object | {} | 基础属性 | - |
| current_attributes | Object | {} | 当前属性 | - |
| is_equipped | Boolean | false | 是否装备 | - |
| acquired_time | Date | 当前时间 | 获得时间 | - |
| createdAt | Date | 当前时间 | 创建时间 | - |
| updatedAt | Date | 当前时间 | 更新时间 | - |

### 4. 玩家技能表 (player_skill)

| 字段名 | 数据类型 | 默认值 | 描述 | 索引 |
|--------|----------|--------|------|------|
| _id | String | 自动生成 | 记录ID | 主键 |
| _openid | String | - | 玩家openid | 索引 |
| skill_id | String | - | 技能模板ID | 索引 |
| name | String | - | 技能名称 | - |
| type | String | 'passive' | 技能类型(passive/active) | 索引 |
| quality | String | 'common' | 技能品质 | 索引 |
| level | Number | 1 | 技能等级 | - |
| max_level | Number | 10 | 最大等级 | - |
| stars | Number | 1 | 技能星级 | - |
| power | Number | 0 | 技能强度 | - |
| current_attributes | Object | {} | 当前属性加成 | - |
| is_equipped | Boolean | false | 是否装备 | - |
| total_upgrade_cost | Number | 0 | 总升级消耗 | - |
| createdAt | Date | 当前时间 | 创建时间 | - |
| updatedAt | Date | 当前时间 | 更新时间 | - |

### 5. 背包物品表 (player_items)

| 字段名 | 数据类型 | 默认值 | 描述 | 索引 |
|--------|----------|--------|------|------|
| _id | String | 自动生成 | 记录ID | 主键 |
| _openid | String | - | 玩家openid | 索引 |
| item_id | String | - | 物品模板ID | 索引 |
| name | String | - | 物品名称 | - |
| type | String | - | 物品类型 | 索引 |
| subtype | String | - | 物品子类型 | - |
| quality | Number | 1 | 物品品质 | - |
| count | Number | 1 | 物品数量 | - |
| max_stack | Number | 999 | 最大堆叠数 | - |
| effects | Object | {} | 物品效果 | - |
| can_use | Boolean | true | 是否可使用 | - |
| can_sell | Boolean | true | 是否可出售 | - |
| sell_price | Number | 0 | 出售价格 | - |
| description | String | '' | 物品描述 | - |
| icon | String | '' | 图标路径 | - |
| acquired_time | Date | 当前时间 | 获得时间 | - |
| createdAt | Date | 当前时间 | 创建时间 | - |
| updatedAt | Date | 当前时间 | 更新时间 | - |

### 6. 剑心系统表 (sword_hearts)

| 字段名 | 数据类型 | 默认值 | 描述 | 索引 |
|--------|----------|--------|------|------|
| _id | String | 自动生成 | 记录ID | 主键 |
| _openid | String | - | 玩家openid | 索引 |
| sword_heart_id | String | - | 剑心ID | 索引 |
| name | String | - | 剑心名称 | - |
| level | Number | 0 | 等级 | - |
| max_level | Number | 10 | 最大等级 | - |
| advancement_level | Number | 0 | 进阶等级 | - |
| max_advancement_level | Number | 9 | 最大进阶等级 | - |
| sword_intent_invested | Number | 0 | 投入的剑意 | - |
| total_attributes | Object | {} | 总属性加成 | - |
| color | String | '#4299e1' | 剑心颜色 | - |
| description | String | '' | 描述 | - |
| createdAt | Date | 当前时间 | 创建时间 | - |
| updatedAt | Date | 当前时间 | 更新时间 | - |

### 7. 剑骨系统表 (sword_bones)

| 字段名 | 数据类型 | 默认值 | 描述 | 索引 |
|--------|----------|--------|------|------|
| _id | String | 自动生成 | 记录ID | 主键 |
| _openid | String | - | 玩家openid | 唯一索引 |
| level | Number | 0 | 剑骨等级 | - |
| rank | Number | 0 | 剑骨品阶 | - |
| total_attributes | Object | {} | 总属性加成 | - |
| upgrade_materials_used | Object | {} | 已使用材料 | - |
| createdAt | Date | 当前时间 | 创建时间 | - |
| updatedAt | Date | 当前时间 | 更新时间 | - |

---

## 系统功能表

### 8. 洞府系统表 (player_dongf)

| 字段名 | 数据类型 | 默认值 | 描述 | 索引 |
|--------|----------|--------|------|------|
| _id | String | 自动生成 | 记录ID | 主键 |
| _openid | String | - | 玩家openid | 唯一索引 |
| level | Number | 1 | 洞府等级 | - |
| current_lingqi | Number | 0 | 当前灵气 | - |
| max_lingqi | Number | 1000 | 最大灵气 | - |
| lingqi_per_hour | Number | 10 | 每小时灵气产出 | - |
| last_collect_time | Date | null | 上次收集时间 | - |
| cultivation_start_time | Date | null | 修炼开始时间 | - |
| cultivation_character_id | String | null | 修炼角色ID | - |
| cultivation_speed_bonus | Number | 1.0 | 修炼速度加成 | - |
| buildings | Object | {} | 建筑数据 | - |
| upgrade_materials | Object | {} | 升级材料需求 | - |
| createdAt | Date | 当前时间 | 创建时间 | - |
| updatedAt | Date | 当前时间 | 更新时间 | - |

### 9. 竞技场数据表 (player_arena)

| 字段名 | 数据类型 | 默认值 | 描述 | 索引 |
|--------|----------|--------|------|------|
| _id | String | 自动生成 | 记录ID | 主键 |
| _openid | String | - | 玩家openid | 唯一索引 |
| current_rank | Number | 10000 | 当前排名 | 索引 |
| highest_rank | Number | 10000 | 历史最高排名 | - |
| challenges_used | Number | 0 | 已使用挑战次数 | - |
| max_challenges | Number | 5 | 最大挑战次数 | - |
| last_challenge_time | Date | null | 上次挑战时间 | - |
| last_reset_time | Date | null | 上次重置时间 | - |
| last_reward_time | Date | null | 上次奖励时间 | - |
| season_wins | Number | 0 | 赛季胜场 | - |
| season_losses | Number | 0 | 赛季败场 | - |
| total_wins | Number | 0 | 总胜场 | - |
| total_losses | Number | 0 | 总败场 | - |
| arena_points | Number | 0 | 竞技场积分 | - |
| createdAt | Date | 当前时间 | 创建时间 | - |
| updatedAt | Date | 当前时间 | 更新时间 | - |

### 10. 挂机游历表 (player_idle)

| 字段名 | 数据类型 | 默认值 | 描述 | 索引 |
|--------|----------|--------|------|------|
| _id | String | 自动生成 | 记录ID | 主键 |
| _openid | String | - | 玩家openid | 唯一索引 |
| current_location | String | 'bamboo_forest' | 当前游历地点 | 索引 |
| start_time | Date | null | 开始时间 | - |
| last_collect_time | Date | null | 上次收集时间 | - |
| total_time | Number | 0 | 总游历时间(秒) | - |
| total_kills | Number | 0 | 总击杀数 | - |
| elite_kills | Number | 0 | 精英击杀数 | - |
| boss_kills | Number | 0 | Boss击杀数 | - |
| location_progress | Object | {} | 各地点进度 | - |
| offline_rewards | Object | {} | 离线奖励待领取 | - |
| auto_use_items | Object | {} | 自动使用物品设置 | - |
| createdAt | Date | 当前时间 | 创建时间 | - |
| updatedAt | Date | 当前时间 | 更新时间 | - |

### 11. 技能修炼表 (p_skill_cul)

| 字段名 | 数据类型 | 默认值 | 描述 | 索引 |
|--------|----------|--------|------|------|
| _id | String | 自动生成 | 记录ID | 主键 |
| _openid | String | - | 玩家openid | 索引 |
| cultivation_tree_id | String | - | 修炼树ID | 索引 |
| realm | String | - | 对应境界 | 索引 |
| current_level | Number | 0 | 当前等级 | - |
| current_layer | Number | 1 | 当前层级 | - |
| unlocked_nodes | Array | [] | 已解锁节点 | - |
| total_cost | Number | 0 | 总消耗修炼点 | - |
| attributes_bonus | Object | {} | 属性加成 | - |
| special_effects | Array | [] | 特殊效果 | - |
| createdAt | Date | 当前时间 | 创建时间 | - |
| updatedAt | Date | 当前时间 | 更新时间 | - |

---

## 交易和记录表

### 12. 充值记录表 (recharge_rec)

| 字段名 | 数据类型 | 默认值 | 描述 | 索引 |
|--------|----------|--------|------|------|
| _id | String | 自动生成 | 记录ID | 主键 |
| _openid | String | - | 玩家openid | 索引 |
| order_id | String | - | 订单ID | 唯一索引 |
| amount | Number | - | 充值金额(分) | - |
| xianyu_amount | Number | - | 获得仙玉 | - |
| bonus_xianyu | Number | 0 | 首充/活动赠送 | - |
| package_id | String | - | 充值套餐ID | - |
| payment_method | String | 'wechat' | 支付方式 | - |
| status | String | 'pending' | 状态(pending/success/failed) | 索引 |
| transaction_id | String | null | 微信交易号 | - |
| recharge_time | Date | 当前时间 | 充值时间 | 索引 |
| callback_time | Date | null | 回调时间 | - |
| createdAt | Date | 当前时间 | 创建时间 | - |

### 13. 抽卡记录表 (gacha_record)

| 字段名 | 数据类型 | 默认值 | 描述 | 索引 |
|--------|----------|--------|------|------|
| _id | String | 自动生成 | 记录ID | 主键 |
| _openid | String | - | 玩家openid | 索引 |
| gacha_type | String | - | 抽卡类型(treasure/character/skill) | 索引 |
| draw_count | Number | 1 | 抽取数量 | - |
| cost_type | String | 'xianyu' | 消耗类型 | - |
| cost_amount | Number | 0 | 消耗数量 | - |
| results | Array | [] | 抽取结果 | - |
| is_pity | Boolean | false | 是否保底 | - |
| pity_count | Number | 0 | 保底计数 | - |
| draw_time | Date | 当前时间 | 抽取时间 | 索引 |
| createdAt | Date | 当前时间 | 创建时间 | - |

### 14. 战斗记录表 (battle_recor)

| 字段名 | 数据类型 | 默认值 | 描述 | 索引 |
|--------|----------|--------|------|------|
| _id | String | 自动生成 | 记录ID | 主键 |
| _openid | String | - | 玩家openid | 索引 |
| battle_type | String | - | 战斗类型(arena/trial/idle/boss) | 索引 |
| opponent_id | String | null | 对手ID | - |
| battle_result | String | - | 战斗结果(win/lose/draw) | 索引 |
| battle_rounds | Number | 0 | 战斗回合数 | - |
| damage_dealt | Number | 0 | 造成伤害 | - |
| damage_taken | Number | 0 | 承受伤害 | - |
| rewards | Object | {} | 战斗奖励 | - |
| battle_duration | Number | 0 | 战斗时长(秒) | - |
| battle_data | Object | {} | 详细战斗数据 | - |
| battle_time | Date | 当前时间 | 战斗时间 | 索引 |
| createdAt | Date | 当前时间 | 创建时间 | - |

---

## 邮件和通知表

### 15. 邮件模板表 (mail_temp)

| 字段名 | 数据类型 | 默认值 | 描述 | 索引 |
|--------|----------|--------|------|------|
| _id | String | 自动生成 | 记录ID | 主键 |
| template_id | String | - | 模板ID | 唯一索引 |
| title | String | - | 邮件标题 | - |
| content | String | - | 邮件内容 | - |
| mail_type | String | 'system' | 邮件类型(system/activity/compensation) | 索引 |
| sender | String | '系统' | 发送者 | - |
| rewards | Object | null | 奖励内容 | - |
| has_rewards | Boolean | false | 是否有奖励 | - |
| expire_days | Number | 7 | 过期天数 | - |
| target_conditions | Object | {} | 目标条件 | - |
| is_active | Boolean | true | 是否激活 | - |
| createdAt | Date | 当前时间 | 创建时间 | 索引 |
| updatedAt | Date | 当前时间 | 更新时间 | - |

### 16. 玩家邮件表 (player_mails)

| 字段名 | 数据类型 | 默认值 | 描述 | 索引 |
|--------|----------|--------|------|------|
| _id | String | 自动生成 | 记录ID | 主键 |
| _openid | String | - | 接收者openid | 索引 |
| mail_id | String | - | 邮件模板ID | - |
| title | String | - | 邮件标题 | - |
| content | String | - | 邮件内容 | - |
| sender | String | '系统' | 发送者 | - |
| mail_type | String | 'system' | 邮件类型 | 索引 |
| rewards | Object | null | 奖励内容 | - |
| has_rewards | Boolean | false | 是否有奖励 | - |
| is_read | Boolean | false | 是否已读 | 索引 |
| is_claimed | Boolean | false | 是否已领取 | 索引 |
| is_deleted | Boolean | false | 是否已删除 | 索引 |
| expire_time | Date | - | 过期时间 | 索引 |
| read_time | Date | null | 阅读时间 | - |
| claim_time | Date | null | 领取时间 | - |
| delete_time | Date | null | 删除时间 | - |
| createdAt | Date | 当前时间 | 创建时间 | 索引 |

---

## 活动和任务表

### 17. 每日任务表 (daily_tasks)

| 字段名 | 数据类型 | 默认值 | 描述 | 索引 |
|--------|----------|--------|------|------|
| _id | String | 自动生成 | 记录ID | 主键 |
| _openid | String | - | 玩家openid | 索引 |
| task_date | String | - | 任务日期(YYYY-MM-DD) | 索引 |
| tasks | Object | {} | 任务进度数据 | - |
| total_activity | Number | 0 | 总活跃度 | - |
| claimed_rewards | Array | [] | 已领取奖励 | - |
| is_completed | Boolean | false | 是否全部完成 | - |
| createdAt | Date | 当前时间 | 创建时间 | - |
| updatedAt | Date | 当前时间 | 更新时间 | - |

### 18. 活动参与表 (activity_par)

| 字段名 | 数据类型 | 默认值 | 描述 | 索引 |
|--------|----------|--------|------|------|
| _id | String | 自动生成 | 记录ID | 主键 |
| _openid | String | - | 玩家openid | 索引 |
| activity_id | String | - | 活动ID | 索引 |
| activity_type | String | - | 活动类型 | 索引 |
| participation_data | Object | {} | 参与数据 | - |
| rewards_claimed | Array | [] | 已领取奖励 | - |
| total_score | Number | 0 | 总分数 | - |
| is_active | Boolean | true | 是否活跃 | - |
| first_join_time | Date | 当前时间 | 首次参与时间 | - |
| last_action_time | Date | 当前时间 | 最后操作时间 | - |
| createdAt | Date | 当前时间 | 创建时间 | - |
| updatedAt | Date | 当前时间 | 更新时间 | - |

---

## 系统配置表

### 19. 游戏配置表 (game_configs)

| 字段名 | 数据类型 | 默认值 | 描述 | 索引 |
|--------|----------|--------|------|------|
| _id | String | 自动生成 | 记录ID | 主键 |
| config_key | String | - | 配置键 | 唯一索引 |
| config_value | Any | - | 配置值 | - |
| config_type | String | 'json' | 配置类型(string/number/boolean/json) | - |
| description | String | '' | 配置描述 | - |
| is_client_visible | Boolean | false | 客户端是否可见 | - |
| version | Number | 1 | 配置版本 | - |
| createdAt | Date | 当前时间 | 创建时间 | - |
| updatedAt | Date | 当前时间 | 更新时间 | - |

---

## 性能优化建议

### 索引策略
1. **复合索引**：`_openid + createdAt + 状态字段`
2. **查询优化**：根据实际查询模式建立最优索引
3. **分页查询**：使用limit和skip进行分页

### 数据分离策略
1. **热数据**：玩家基础信息、资源、角色数据
2. **温数据**：装备、技能、邮件数据
3. **冷数据**：历史记录、日志数据

### 缓存策略
1. **本地缓存**：静态配置数据
2. **云端缓存**：玩家基础数据
3. **实时同步**：关键操作数据

---

## 数据一致性保证

1. **事务处理**：关键操作使用云函数事务
2. **乐观锁**：并发操作使用版本号控制
3. **数据校验**：客户端和服务端双重校验
4. **备份恢复**：定期数据备份和恢复机制

这个设计充分考虑了微信小游戏的特点，既保证了功能完整性，又优化了性能和扩展性。 