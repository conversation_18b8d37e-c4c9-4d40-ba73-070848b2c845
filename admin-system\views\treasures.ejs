<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #2c3e50;
        }
        .sidebar .nav-link {
            color: #ecf0f1;
            border-radius: 5px;
            margin: 2px 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: #34495e;
            color: #fff;
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: none;
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
        }
        .rarity-badge {
            font-size: 0.8em;
        }
        .rarity-1 { background-color: #6c757d; }
        .rarity-2 { background-color: #28a745; }
        .rarity-3 { background-color: #007bff; }
        .rarity-4 { background-color: #6f42c1; }
        .rarity-5 { background-color: #ffc107; color: #212529; }
        .attribute-input {
            margin-bottom: 10px;
        }
        .attribute-group {
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f8f9fa;
        }
        .star-upgrade-item {
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
            background-color: #fff;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">修仙游戏管理后台</h5>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/">
                                <i class="bi bi-house-door"></i> 首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/players">
                                <i class="bi bi-people"></i> 玩家管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/mails">
                                <i class="bi bi-envelope"></i> 邮件管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/skills">
                                <i class="bi bi-book"></i> 功法模板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/sword-hearts">
                                <i class="bi bi-heart"></i> 剑心模板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/treasures">
                                <i class="bi bi-gem"></i> 古宝模板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/gacha-pools">
                                <i class="bi bi-dice-3"></i> 抽取池
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <div>
                        <h1 class="h2">古宝模板管理</h1>
                        <p class="text-muted">管理游戏中的所有古宝模板，包括武器、法宝、护符等</p>
                    </div>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#treasureModal" onclick="openCreateModal()">
                            <i class="bi bi-plus-circle"></i> 创建古宝
                        </button>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <input type="text" class="form-control" id="searchInput" placeholder="搜索古宝名称...">
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="typeFilter">
                                    <option value="">所有类型</option>
                                    <option value="attack">攻击</option>
                                    <option value="defense">防御</option>
                                    <option value="special">特殊</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="categoryFilter">
                                    <option value="">所有分类</option>
                                    <option value="weapon">武器</option>
                                    <option value="artifact">法宝</option>
                                    <option value="talisman">护符</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="rarityFilter">
                                    <option value="">所有稀有度</option>
                                    <option value="1">1星</option>
                                    <option value="2">2星</option>
                                    <option value="3">3星</option>
                                    <option value="4">4星</option>
                                    <option value="5">5星</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-primary me-2" onclick="searchTreasures()">
                                    <i class="bi bi-search"></i> 搜索
                                </button>
                                <button class="btn btn-outline-secondary" onclick="resetFilters()">
                                    <i class="bi bi-arrow-clockwise"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 古宝列表 -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>古宝ID</th>
                                        <th>名称</th>
                                        <th>类型</th>
                                        <th>分类</th>
                                        <th>稀有度</th>
                                        <th>最大等级</th>
                                        <th>最大星级</th>
                                        <th>套装</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="treasuresTableBody">
                                    <!-- 数据将通过JavaScript加载 -->
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页 -->
                        <nav aria-label="古宝列表分页">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页将通过JavaScript生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 古宝编辑模态框 -->
    <div class="modal fade" id="treasureModal" tabindex="-1" aria-labelledby="treasureModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="treasureModalLabel">创建古宝模板</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="treasureForm">
                        <div class="row">
                            <!-- 基本信息 -->
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">基本信息</h6>
                                <div class="mb-3">
                                    <label for="treasureId" class="form-label">古宝ID <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="treasureId" required>
                                    <div class="form-text">唯一标识符，不可重复</div>
                                </div>
                                <div class="mb-3">
                                    <label for="treasureName" class="form-label">古宝名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="treasureName" required>
                                </div>
                                <div class="mb-3">
                                    <label for="treasureType" class="form-label">古宝类型 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="treasureType" required>
                                        <option value="">请选择类型</option>
                                        <option value="attack">攻击</option>
                                        <option value="defense">防御</option>
                                        <option value="special">特殊</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="treasureCategory" class="form-label">古宝分类 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="treasureCategory" required>
                                        <option value="">请选择分类</option>
                                        <option value="weapon">武器</option>
                                        <option value="artifact">法宝</option>
                                        <option value="talisman">护符</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="treasureRarity" class="form-label">稀有度</label>
                                    <select class="form-select" id="treasureRarity">
                                        <option value="1">1星</option>
                                        <option value="2">2星</option>
                                        <option value="3">3星</option>
                                        <option value="4">4星</option>
                                        <option value="5">5星</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="treasureMaxLevel" class="form-label">最大等级</label>
                                    <input type="number" class="form-control" id="treasureMaxLevel" value="100" min="1" max="200">
                                </div>
                                <div class="mb-3">
                                    <label for="treasureMaxStar" class="form-label">最大星级</label>
                                    <input type="number" class="form-control" id="treasureMaxStar" value="5" min="1" max="10">
                                </div>
                                <div class="mb-3">
                                    <label for="treasureSetId" class="form-label">套装ID</label>
                                    <input type="text" class="form-control" id="treasureSetId" placeholder="可选，如：dragon_set">
                                    <div class="form-text">留空表示不属于任何套装</div>
                                </div>
                            </div>

                            <!-- 属性配置 -->
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">属性配置</h6>
                                
                                <!-- 基础属性 -->
                                <div class="attribute-group">
                                    <h6>基础属性</h6>
                                    <div id="baseAttributesContainer">
                                        <!-- 动态生成 -->
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="addAttribute('base')">
                                        <i class="bi bi-plus"></i> 添加基础属性
                                    </button>
                                </div>

                                <!-- 升级成长 -->
                                <div class="attribute-group">
                                    <h6>升级成长</h6>
                                    <div id="upgradeGrowthContainer">
                                        <!-- 动态生成 -->
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="addAttribute('growth')">
                                        <i class="bi bi-plus"></i> 添加成长属性
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 升星配置 -->
                        <div class="row">
                            <div class="col-12">
                                <h6 class="text-primary mb-3">升星配置</h6>
                                <div id="starUpgradeContainer">
                                    <!-- 动态生成 -->
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="addStarUpgrade()">
                                    <i class="bi bi-plus"></i> 添加升星配置
                                </button>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="treasureDescription" class="form-label">古宝描述</label>
                                    <textarea class="form-control" id="treasureDescription" rows="3"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="treasureIcon" class="form-label">图标路径</label>
                                    <input type="text" class="form-control" id="treasureIcon" placeholder="如：icons/treasures/treasure_name.png">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveTreasure()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentPage = 1;
        let currentTreasureId = null;
        let isEditMode = false;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadTreasures();
            
            // 绑定搜索事件
            document.getElementById('searchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchTreasures();
                }
            });
        });

        // 加载古宝列表
        async function loadTreasures(page = 1) {
            try {
                const search = document.getElementById('searchInput').value;
                const type = document.getElementById('typeFilter').value;
                const category = document.getElementById('categoryFilter').value;
                const rarity = document.getElementById('rarityFilter').value;
                
                const params = new URLSearchParams({
                    page: page,
                    limit: 20
                });
                
                if (search) params.append('search', search);
                if (type) params.append('type', type);
                if (category) params.append('category', category);
                if (rarity) params.append('rarity', rarity);
                
                const response = await fetch(`/api/treasure-templates?${params}`);
                const result = await response.json();
                
                if (result.success) {
                    displayTreasures(result.data);
                    displayPagination(result.page, Math.ceil(result.total / result.limit), result.total);
                    currentPage = result.page;
                } else {
                    console.error('加载古宝列表失败:', result.error);
                    alert('加载古宝列表失败: ' + result.error);
                }
            } catch (error) {
                console.error('加载古宝列表出错:', error);
                alert('加载古宝列表出错: ' + error.message);
            }
        }

        // 显示古宝列表
        function displayTreasures(treasures) {
            const tbody = document.getElementById('treasuresTableBody');
            tbody.innerHTML = '';
            
            treasures.forEach(treasure => {
                const typeMap = { attack: '攻击', defense: '防御', special: '特殊' };
                const categoryMap = { weapon: '武器', artifact: '法宝', talisman: '护符' };
                
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><code>${treasure.treasure_id}</code></td>
                    <td><strong>${treasure.name}</strong></td>
                    <td><span class="badge bg-${treasure.type === 'attack' ? 'danger' : treasure.type === 'defense' ? 'success' : 'warning'}">${typeMap[treasure.type]}</span></td>
                    <td><span class="badge bg-info">${categoryMap[treasure.category]}</span></td>
                    <td><span class="badge rarity-${treasure.rarity} rarity-badge">${treasure.rarity}星</span></td>
                    <td>${treasure.max_level}</td>
                    <td>${treasure.max_star}</td>
                    <td>${treasure.set_id ? `<span class="badge bg-secondary">${treasure.set_id}</span>` : '-'}</td>
                    <td>${new Date(treasure.created_at).toLocaleDateString()}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary me-1" onclick="editTreasure('${treasure._id}')">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteTreasure('${treasure._id}', '${treasure.name}')">
                            <i class="bi bi-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 显示分页
        function displayPagination(current, total, count) {
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';
            
            // 显示总数
            const info = document.createElement('li');
            info.className = 'page-item disabled';
            info.innerHTML = `<span class="page-link">共 ${count} 条记录</span>`;
            pagination.appendChild(info);
            
            // 上一页
            if (current > 1) {
                const prev = document.createElement('li');
                prev.className = 'page-item';
                prev.innerHTML = `<a class="page-link" href="#" onclick="loadTreasures(${current - 1})">上一页</a>`;
                pagination.appendChild(prev);
            }
            
            // 页码
            const start = Math.max(1, current - 2);
            const end = Math.min(total, current + 2);
            
            for (let i = start; i <= end; i++) {
                const item = document.createElement('li');
                item.className = `page-item ${i === current ? 'active' : ''}`;
                item.innerHTML = `<a class="page-link" href="#" onclick="loadTreasures(${i})">${i}</a>`;
                pagination.appendChild(item);
            }
            
            // 下一页
            if (current < total) {
                const next = document.createElement('li');
                next.className = 'page-item';
                next.innerHTML = `<a class="page-link" href="#" onclick="loadTreasures(${current + 1})">下一页</a>`;
                pagination.appendChild(next);
            }
        }

        // 搜索古宝
        function searchTreasures() {
            currentPage = 1;
            loadTreasures(1);
        }

        // 重置筛选
        function resetFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('typeFilter').value = '';
            document.getElementById('categoryFilter').value = '';
            document.getElementById('rarityFilter').value = '';
            currentPage = 1;
            loadTreasures(1);
        }

        // 打开创建模态框
        function openCreateModal() {
            isEditMode = false;
            currentTreasureId = null;
            document.getElementById('treasureModalLabel').textContent = '创建古宝模板';
            document.getElementById('treasureForm').reset();
            clearAll();
            addAttribute('base');
            addAttribute('growth');
            addStarUpgrade();
        }

        // 编辑古宝
        async function editTreasure(treasureId) {
            try {
                const response = await fetch(`/api/treasure-templates?search=${treasureId}`);
                const result = await response.json();
                
                if (result.success && result.data.length > 0) {
                    const treasure = result.data[0];
                    isEditMode = true;
                    currentTreasureId = treasureId;
                    
                    document.getElementById('treasureModalLabel').textContent = '编辑古宝模板';
                    document.getElementById('treasureId').value = treasure.treasure_id;
                    document.getElementById('treasureName').value = treasure.name;
                    document.getElementById('treasureType').value = treasure.type;
                    document.getElementById('treasureCategory').value = treasure.category;
                    document.getElementById('treasureRarity').value = treasure.rarity;
                    document.getElementById('treasureMaxLevel').value = treasure.max_level;
                    document.getElementById('treasureMaxStar').value = treasure.max_star;
                    document.getElementById('treasureSetId').value = treasure.set_id || '';
                    document.getElementById('treasureDescription').value = treasure.description || '';
                    document.getElementById('treasureIcon').value = treasure.icon || '';
                    
                    // 加载属性
                    loadAttributes(treasure.base_attributes, 'base');
                    loadAttributes(treasure.upgrade_growth, 'growth');
                    loadStarUpgrades(treasure.star_upgrade);
                    
                    // 显示模态框
                    const modal = new bootstrap.Modal(document.getElementById('treasureModal'));
                    modal.show();
                } else {
                    alert('获取古宝信息失败');
                }
            } catch (error) {
                console.error('编辑古宝出错:', error);
                alert('编辑古宝出错: ' + error.message);
            }
        }

        // 删除古宝
        async function deleteTreasure(treasureId, treasureName) {
            if (!confirm(`确定要删除古宝 "${treasureName}" 吗？此操作不可撤销。`)) {
                return;
            }
            
            try {
                const response = await fetch(`/api/treasure-templates/${treasureId}`, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('古宝删除成功');
                    loadTreasures(currentPage);
                } else {
                    alert('删除失败: ' + result.error);
                }
            } catch (error) {
                console.error('删除古宝出错:', error);
                alert('删除古宝出错: ' + error.message);
            }
        }

        // 保存古宝
        async function saveTreasure() {
            try {
                const formData = {
                    treasure_id: document.getElementById('treasureId').value,
                    name: document.getElementById('treasureName').value,
                    type: document.getElementById('treasureType').value,
                    category: document.getElementById('treasureCategory').value,
                    rarity: parseInt(document.getElementById('treasureRarity').value),
                    max_level: parseInt(document.getElementById('treasureMaxLevel').value),
                    max_star: parseInt(document.getElementById('treasureMaxStar').value),
                    set_id: document.getElementById('treasureSetId').value || null,
                    description: document.getElementById('treasureDescription').value,
                    icon: document.getElementById('treasureIcon').value,
                    base_attributes: getAttributes('base'),
                    upgrade_growth: getAttributes('growth'),
                    star_upgrade: getStarUpgrades()
                };
                
                const url = isEditMode ? `/api/treasure-templates/${currentTreasureId}` : '/api/treasure-templates';
                const method = isEditMode ? 'PUT' : 'POST';
                
                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert(isEditMode ? '古宝更新成功' : '古宝创建成功');
                    const modal = bootstrap.Modal.getInstance(document.getElementById('treasureModal'));
                    modal.hide();
                    loadTreasures(currentPage);
                } else {
                    alert('保存失败: ' + result.error);
                }
            } catch (error) {
                console.error('保存古宝出错:', error);
                alert('保存古宝出错: ' + error.message);
            }
        }

        // 添加属性输入框
        function addAttribute(type) {
            const container = document.getElementById(type === 'base' ? 'baseAttributesContainer' : 'upgradeGrowthContainer');
            const div = document.createElement('div');
            div.className = 'attribute-input';
            div.innerHTML = `
                <div class="row">
                    <div class="col-5">
                        <select class="form-select attribute-type">
                            <option value="hp">生命值</option>
                            <option value="attack">攻击力</option>
                            <option value="defense">防御力</option>
                            <option value="speed">速度</option>
                            <option value="crit">暴击率</option>
                            <option value="crit_damage">暴击伤害</option>
                            <option value="dao_rule">大道法则</option>
                            <option value="penetration">破防</option>
                        </select>
                    </div>
                    <div class="col-5">
                        <input type="number" class="form-control attribute-value" placeholder="${type === 'base' ? '数值' : '每级成长'}">
                    </div>
                    <div class="col-2">
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeAttribute(this)">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            `;
            container.appendChild(div);
        }

        // 移除属性输入框
        function removeAttribute(button) {
            button.closest('.attribute-input').remove();
        }

        // 添加升星配置
        function addStarUpgrade() {
            const container = document.getElementById('starUpgradeContainer');
            const div = document.createElement('div');
            div.className = 'star-upgrade-item';
            div.innerHTML = `
                <div class="row mb-2">
                    <div class="col-2">
                        <label class="form-label">星级</label>
                        <input type="number" class="form-control star-level" min="1" max="10" value="1">
                    </div>
                    <div class="col-3">
                        <label class="form-label">灵石消耗</label>
                        <input type="number" class="form-control cost-lingshi" min="0">
                    </div>
                    <div class="col-3">
                        <label class="form-label">强化石消耗</label>
                        <input type="number" class="form-control cost-spirit-stone" min="0">
                    </div>
                    <div class="col-3">
                        <label class="form-label">属性加成</label>
                        <div class="star-attributes-container">
                            <!-- 动态生成属性加成 -->
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="addStarAttribute(this)">
                            <i class="bi bi-plus"></i> 添加属性
                        </button>
                    </div>
                    <div class="col-1">
                        <label class="form-label">&nbsp;</label>
                        <button type="button" class="btn btn-sm btn-outline-danger d-block" onclick="removeStarUpgrade(this)">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            `;
            container.appendChild(div);
            
            // 添加一个默认属性
            addStarAttribute(div.querySelector('button'));
        }

        // 移除升星配置
        function removeStarUpgrade(button) {
            button.closest('.star-upgrade-item').remove();
        }

        // 添加升星属性
        function addStarAttribute(button) {
            const container = button.previousElementSibling;
            const div = document.createElement('div');
            div.className = 'row mb-1';
            div.innerHTML = `
                <div class="col-6">
                    <select class="form-select star-attr-type">
                        <option value="hp">生命值</option>
                        <option value="attack">攻击力</option>
                        <option value="defense">防御力</option>
                        <option value="speed">速度</option>
                        <option value="crit">暴击率</option>
                        <option value="crit_damage">暴击伤害</option>
                        <option value="dao_rule">大道法则</option>
                        <option value="penetration">破防</option>
                    </select>
                </div>
                <div class="col-4">
                    <input type="number" class="form-control star-attr-value" placeholder="数值">
                </div>
                <div class="col-2">
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeStarAttribute(this)">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            `;
            container.appendChild(div);
        }

        // 移除升星属性
        function removeStarAttribute(button) {
            button.closest('.row').remove();
        }

        // 清空所有内容
        function clearAll() {
            document.getElementById('baseAttributesContainer').innerHTML = '';
            document.getElementById('upgradeGrowthContainer').innerHTML = '';
            document.getElementById('starUpgradeContainer').innerHTML = '';
        }

        // 获取属性数据
        function getAttributes(type) {
            const container = document.getElementById(type === 'base' ? 'baseAttributesContainer' : 'upgradeGrowthContainer');
            const attributes = {};
            
            container.querySelectorAll('.attribute-input').forEach(input => {
                const typeSelect = input.querySelector('.attribute-type');
                const valueInput = input.querySelector('.attribute-value');
                
                if (typeSelect.value && valueInput.value) {
                    attributes[typeSelect.value] = parseFloat(valueInput.value);
                }
            });
            
            return attributes;
        }

        // 获取升星配置
        function getStarUpgrades() {
            const container = document.getElementById('starUpgradeContainer');
            const starUpgrades = [];
            
            container.querySelectorAll('.star-upgrade-item').forEach(item => {
                const star = parseInt(item.querySelector('.star-level').value);
                const lingshi = parseInt(item.querySelector('.cost-lingshi').value) || 0;
                const spiritStone = parseInt(item.querySelector('.cost-spirit-stone').value) || 0;
                
                const attributeBonus = {};
                item.querySelectorAll('.star-attributes-container .row').forEach(attrRow => {
                    const type = attrRow.querySelector('.star-attr-type').value;
                    const value = parseFloat(attrRow.querySelector('.star-attr-value').value);
                    if (type && !isNaN(value)) {
                        attributeBonus[type] = value;
                    }
                });
                
                if (star && (lingshi > 0 || spiritStone > 0)) {
                    starUpgrades.push({
                        star: star,
                        cost: { lingshi: lingshi, spirit_stone: spiritStone },
                        attribute_bonus: attributeBonus
                    });
                }
            });
            
            return starUpgrades;
        }

        // 加载属性数据到界面
        function loadAttributes(attributes, type) {
            const container = document.getElementById(type === 'base' ? 'baseAttributesContainer' : 'upgradeGrowthContainer');
            container.innerHTML = '';
            
            Object.entries(attributes || {}).forEach(([key, value]) => {
                addAttribute(type);
                const lastInput = container.lastElementChild;
                lastInput.querySelector('.attribute-type').value = key;
                lastInput.querySelector('.attribute-value').value = value;
            });
            
            // 如果没有属性，至少添加一个空的输入框
            if (Object.keys(attributes || {}).length === 0) {
                addAttribute(type);
            }
        }

        // 加载升星配置
        function loadStarUpgrades(starUpgrades) {
            const container = document.getElementById('starUpgradeContainer');
            container.innerHTML = '';
            
            (starUpgrades || []).forEach(upgrade => {
                addStarUpgrade();
                const lastItem = container.lastElementChild;
                
                lastItem.querySelector('.star-level').value = upgrade.star;
                lastItem.querySelector('.cost-lingshi').value = upgrade.cost.lingshi || 0;
                lastItem.querySelector('.cost-spirit-stone').value = upgrade.cost.spirit_stone || 0;
                
                // 清空默认属性，加载实际属性
                const attrContainer = lastItem.querySelector('.star-attributes-container');
                attrContainer.innerHTML = '';
                
                Object.entries(upgrade.attribute_bonus || {}).forEach(([key, value]) => {
                    addStarAttribute(lastItem.querySelector('button'));
                    const lastAttr = attrContainer.lastElementChild;
                    lastAttr.querySelector('.star-attr-type').value = key;
                    lastAttr.querySelector('.star-attr-value').value = value;
                });
            });
            
            // 如果没有升星配置，至少添加一个空的
            if ((starUpgrades || []).length === 0) {
                addStarUpgrade();
            }
        }
    </script>
</body>
</html> 