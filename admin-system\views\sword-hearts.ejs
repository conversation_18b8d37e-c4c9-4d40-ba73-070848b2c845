<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #2c3e50;
        }
        .sidebar .nav-link {
            color: #ecf0f1;
            border-radius: 5px;
            margin: 2px 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: #34495e;
            color: #fff;
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: none;
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
        }
        .rarity-badge {
            font-size: 0.8em;
        }
        .rarity-1 { background-color: #6c757d; }
        .rarity-2 { background-color: #28a745; }
        .rarity-3 { background-color: #007bff; }
        .rarity-4 { background-color: #6f42c1; }
        .rarity-5 { background-color: #ffc107; color: #212529; }
        .attribute-input {
            margin-bottom: 10px;
        }
        .attribute-group {
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f8f9fa;
        }
        .color-preview {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: 2px solid #dee2e6;
            display: inline-block;
            vertical-align: middle;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">修仙游戏管理后台</h5>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/">
                                <i class="bi bi-house-door"></i> 首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/players">
                                <i class="bi bi-people"></i> 玩家管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/mails">
                                <i class="bi bi-envelope"></i> 邮件管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/skills">
                                <i class="bi bi-book"></i> 功法模板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/sword-hearts">
                                <i class="bi bi-heart"></i> 剑心模板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/treasures">
                                <i class="bi bi-gem"></i> 古宝模板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/gacha-pools">
                                <i class="bi bi-dice-3"></i> 抽取池
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <div>
                        <h1 class="h2">剑心模板管理</h1>
                        <p class="text-muted">管理游戏中的所有剑心模板，包括属性配置和进阶设置</p>
                    </div>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#swordHeartModal" onclick="openCreateModal()">
                            <i class="bi bi-plus-circle"></i> 创建剑心
                        </button>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <input type="text" class="form-control" id="searchInput" placeholder="搜索剑心名称...">
                            </div>
                            <div class="col-md-3">
                                <input type="text" class="form-control" id="categoryFilter" placeholder="剑心分类...">
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="rarityFilter">
                                    <option value="">所有稀有度</option>
                                    <option value="1">1星</option>
                                    <option value="2">2星</option>
                                    <option value="3">3星</option>
                                    <option value="4">4星</option>
                                    <option value="5">5星</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-outline-primary me-2" onclick="searchSwordHearts()">
                                    <i class="bi bi-search"></i> 搜索
                                </button>
                                <button class="btn btn-outline-secondary" onclick="resetFilters()">
                                    <i class="bi bi-arrow-clockwise"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 剑心列表 -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>剑心ID</th>
                                        <th>名称</th>
                                        <th>分类</th>
                                        <th>稀有度</th>
                                        <th>最大等级</th>
                                        <th>最大进阶</th>
                                        <th>颜色</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="swordHeartsTableBody">
                                    <!-- 数据将通过JavaScript加载 -->
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页 -->
                        <nav aria-label="剑心列表分页">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页将通过JavaScript生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 剑心编辑模态框 -->
    <div class="modal fade" id="swordHeartModal" tabindex="-1" aria-labelledby="swordHeartModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="swordHeartModalLabel">创建剑心模板</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="swordHeartForm">
                        <div class="row">
                            <!-- 基本信息 -->
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">基本信息</h6>
                                <div class="mb-3">
                                    <label for="swordHeartId" class="form-label">剑心ID <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="swordHeartId" required>
                                    <div class="form-text">唯一标识符，不可重复</div>
                                </div>
                                <div class="mb-3">
                                    <label for="swordHeartName" class="form-label">剑心名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="swordHeartName" required>
                                </div>
                                <div class="mb-3">
                                    <label for="swordHeartCategory" class="form-label">剑心分类 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="swordHeartCategory" required>
                                    <div class="form-text">如：攻击类、防御类、辅助类等</div>
                                </div>
                                <div class="mb-3">
                                    <label for="swordHeartRarity" class="form-label">稀有度</label>
                                    <select class="form-select" id="swordHeartRarity">
                                        <option value="1">1星</option>
                                        <option value="2">2星</option>
                                        <option value="3">3星</option>
                                        <option value="4">4星</option>
                                        <option value="5">5星</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="swordHeartMaxLevel" class="form-label">最大等级</label>
                                    <input type="number" class="form-control" id="swordHeartMaxLevel" value="10" min="1" max="50">
                                </div>
                                <div class="mb-3">
                                    <label for="swordHeartMaxAdvancement" class="form-label">最大进阶等级</label>
                                    <input type="number" class="form-control" id="swordHeartMaxAdvancement" value="9" min="0" max="20">
                                </div>
                                <div class="mb-3">
                                    <label for="swordHeartColor" class="form-label">剑心颜色</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="swordHeartColor" value="#4299e1">
                                        <input type="text" class="form-control" id="swordHeartColorText" value="#4299e1">
                                    </div>
                                </div>
                            </div>

                            <!-- 属性配置 -->
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">属性配置</h6>
                                
                                <!-- 基础属性 -->
                                <div class="attribute-group">
                                    <h6>基础属性</h6>
                                    <div id="baseAttributesContainer">
                                        <!-- 动态生成 -->
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="addAttribute('base')">
                                        <i class="bi bi-plus"></i> 添加基础属性
                                    </button>
                                </div>

                                <!-- 升级属性 -->
                                <div class="attribute-group">
                                    <h6>升级属性</h6>
                                    <div id="levelAttributesContainer">
                                        <!-- 动态生成 -->
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="addAttribute('level')">
                                        <i class="bi bi-plus"></i> 添加升级属性
                                    </button>
                                </div>

                                <!-- 进阶属性 -->
                                <div class="attribute-group">
                                    <h6>进阶属性</h6>
                                    <div id="advancementAttributesContainer">
                                        <!-- 动态生成 -->
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="addAttribute('advancement')">
                                        <i class="bi bi-plus"></i> 添加进阶属性
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="swordHeartDescription" class="form-label">剑心描述</label>
                                    <textarea class="form-control" id="swordHeartDescription" rows="3"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="swordHeartIcon" class="form-label">图标路径</label>
                                    <input type="text" class="form-control" id="swordHeartIcon" placeholder="如：icons/sword_hearts/heart_name.png">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveSwordHeart()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentPage = 1;
        let currentSwordHeartId = null;
        let isEditMode = false;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadSwordHearts();
            
            // 绑定颜色选择器事件
            document.getElementById('swordHeartColor').addEventListener('input', function(e) {
                document.getElementById('swordHeartColorText').value = e.target.value;
            });
            
            document.getElementById('swordHeartColorText').addEventListener('input', function(e) {
                document.getElementById('swordHeartColor').value = e.target.value;
            });
            
            // 绑定搜索事件
            document.getElementById('searchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchSwordHearts();
                }
            });
        });

        // 加载剑心列表
        async function loadSwordHearts(page = 1) {
            try {
                const search = document.getElementById('searchInput').value;
                const category = document.getElementById('categoryFilter').value;
                const rarity = document.getElementById('rarityFilter').value;
                
                const params = new URLSearchParams({
                    page: page,
                    limit: 20
                });
                
                if (search) params.append('search', search);
                if (category) params.append('category', category);
                if (rarity) params.append('rarity', rarity);
                
                const response = await fetch(`/api/sword-heart-templates?${params}`);
                const result = await response.json();
                
                if (result.success) {
                    displaySwordHearts(result.data);
                    displayPagination(result.page, Math.ceil(result.total / result.limit), result.total);
                    currentPage = result.page;
                } else {
                    console.error('加载剑心列表失败:', result.error);
                    alert('加载剑心列表失败: ' + result.error);
                }
            } catch (error) {
                console.error('加载剑心列表出错:', error);
                alert('加载剑心列表出错: ' + error.message);
            }
        }

        // 显示剑心列表
        function displaySwordHearts(swordHearts) {
            const tbody = document.getElementById('swordHeartsTableBody');
            tbody.innerHTML = '';
            
            swordHearts.forEach(swordHeart => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><code>${swordHeart.sword_heart_id}</code></td>
                    <td><strong>${swordHeart.name}</strong></td>
                    <td><span class="badge bg-info">${swordHeart.category}</span></td>
                    <td><span class="badge rarity-${swordHeart.rarity} rarity-badge">${swordHeart.rarity}星</span></td>
                    <td>${swordHeart.max_level}</td>
                    <td>${swordHeart.max_advancement}</td>
                    <td>
                        <span class="color-preview" style="background-color: ${swordHeart.color}"></span>
                        <small class="text-muted">${swordHeart.color}</small>
                    </td>
                    <td>${new Date(swordHeart.created_at).toLocaleDateString()}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary me-1" onclick="editSwordHeart('${swordHeart._id}')">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteSwordHeart('${swordHeart._id}', '${swordHeart.name}')">
                            <i class="bi bi-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 显示分页
        function displayPagination(current, total, count) {
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';
            
            // 显示总数
            const info = document.createElement('li');
            info.className = 'page-item disabled';
            info.innerHTML = `<span class="page-link">共 ${count} 条记录</span>`;
            pagination.appendChild(info);
            
            // 上一页
            if (current > 1) {
                const prev = document.createElement('li');
                prev.className = 'page-item';
                prev.innerHTML = `<a class="page-link" href="#" onclick="loadSwordHearts(${current - 1})">上一页</a>`;
                pagination.appendChild(prev);
            }
            
            // 页码
            const start = Math.max(1, current - 2);
            const end = Math.min(total, current + 2);
            
            for (let i = start; i <= end; i++) {
                const item = document.createElement('li');
                item.className = `page-item ${i === current ? 'active' : ''}`;
                item.innerHTML = `<a class="page-link" href="#" onclick="loadSwordHearts(${i})">${i}</a>`;
                pagination.appendChild(item);
            }
            
            // 下一页
            if (current < total) {
                const next = document.createElement('li');
                next.className = 'page-item';
                next.innerHTML = `<a class="page-link" href="#" onclick="loadSwordHearts(${current + 1})">下一页</a>`;
                pagination.appendChild(next);
            }
        }

        // 搜索剑心
        function searchSwordHearts() {
            currentPage = 1;
            loadSwordHearts(1);
        }

        // 重置筛选
        function resetFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('categoryFilter').value = '';
            document.getElementById('rarityFilter').value = '';
            currentPage = 1;
            loadSwordHearts(1);
        }

        // 打开创建模态框
        function openCreateModal() {
            isEditMode = false;
            currentSwordHeartId = null;
            document.getElementById('swordHeartModalLabel').textContent = '创建剑心模板';
            document.getElementById('swordHeartForm').reset();
            document.getElementById('swordHeartColor').value = '#4299e1';
            document.getElementById('swordHeartColorText').value = '#4299e1';
            clearAttributes();
            addAttribute('base');
            addAttribute('level');
            addAttribute('advancement');
        }

        // 编辑剑心
        async function editSwordHeart(swordHeartId) {
            try {
                const response = await fetch(`/api/sword-heart-templates?search=${swordHeartId}`);
                const result = await response.json();
                
                if (result.success && result.data.length > 0) {
                    const swordHeart = result.data[0];
                    isEditMode = true;
                    currentSwordHeartId = swordHeartId;
                    
                    document.getElementById('swordHeartModalLabel').textContent = '编辑剑心模板';
                    document.getElementById('swordHeartId').value = swordHeart.sword_heart_id;
                    document.getElementById('swordHeartName').value = swordHeart.name;
                    document.getElementById('swordHeartCategory').value = swordHeart.category;
                    document.getElementById('swordHeartRarity').value = swordHeart.rarity;
                    document.getElementById('swordHeartMaxLevel').value = swordHeart.max_level;
                    document.getElementById('swordHeartMaxAdvancement').value = swordHeart.max_advancement;
                    document.getElementById('swordHeartColor').value = swordHeart.color;
                    document.getElementById('swordHeartColorText').value = swordHeart.color;
                    document.getElementById('swordHeartDescription').value = swordHeart.description || '';
                    document.getElementById('swordHeartIcon').value = swordHeart.icon || '';
                    
                    // 加载属性
                    loadAttributes(swordHeart.base_attributes, 'base');
                    loadAttributes(swordHeart.level_attributes, 'level');
                    loadAttributes(swordHeart.advancement_attributes, 'advancement');
                    
                    // 显示模态框
                    const modal = new bootstrap.Modal(document.getElementById('swordHeartModal'));
                    modal.show();
                } else {
                    alert('获取剑心信息失败');
                }
            } catch (error) {
                console.error('编辑剑心出错:', error);
                alert('编辑剑心出错: ' + error.message);
            }
        }

        // 删除剑心
        async function deleteSwordHeart(swordHeartId, swordHeartName) {
            if (!confirm(`确定要删除剑心 "${swordHeartName}" 吗？此操作不可撤销。`)) {
                return;
            }
            
            try {
                const response = await fetch(`/api/sword-heart-templates/${swordHeartId}`, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('剑心删除成功');
                    loadSwordHearts(currentPage);
                } else {
                    alert('删除失败: ' + result.error);
                }
            } catch (error) {
                console.error('删除剑心出错:', error);
                alert('删除剑心出错: ' + error.message);
            }
        }

        // 保存剑心
        async function saveSwordHeart() {
            try {
                const formData = {
                    sword_heart_id: document.getElementById('swordHeartId').value,
                    name: document.getElementById('swordHeartName').value,
                    category: document.getElementById('swordHeartCategory').value,
                    rarity: parseInt(document.getElementById('swordHeartRarity').value),
                    max_level: parseInt(document.getElementById('swordHeartMaxLevel').value),
                    max_advancement: parseInt(document.getElementById('swordHeartMaxAdvancement').value),
                    color: document.getElementById('swordHeartColor').value,
                    description: document.getElementById('swordHeartDescription').value,
                    icon: document.getElementById('swordHeartIcon').value,
                    base_attributes: getAttributes('base'),
                    level_attributes: getAttributes('level'),
                    advancement_attributes: getAttributes('advancement')
                };
                
                const url = isEditMode ? `/api/sword-heart-templates/${currentSwordHeartId}` : '/api/sword-heart-templates';
                const method = isEditMode ? 'PUT' : 'POST';
                
                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert(isEditMode ? '剑心更新成功' : '剑心创建成功');
                    const modal = bootstrap.Modal.getInstance(document.getElementById('swordHeartModal'));
                    modal.hide();
                    loadSwordHearts(currentPage);
                } else {
                    alert('保存失败: ' + result.error);
                }
            } catch (error) {
                console.error('保存剑心出错:', error);
                alert('保存剑心出错: ' + error.message);
            }
        }

        // 添加属性输入框
        function addAttribute(type) {
            const containerMap = {
                'base': 'baseAttributesContainer',
                'level': 'levelAttributesContainer',
                'advancement': 'advancementAttributesContainer'
            };
            
            const container = document.getElementById(containerMap[type]);
            const div = document.createElement('div');
            div.className = 'attribute-input';
            div.innerHTML = `
                <div class="row">
                    <div class="col-5">
                        <select class="form-select attribute-type">
                            <option value="hp">生命值</option>
                            <option value="attack">攻击力</option>
                            <option value="defense">防御力</option>
                            <option value="speed">速度</option>
                            <option value="crit">暴击率</option>
                            <option value="crit_damage">暴击伤害</option>
                            <option value="dao_rule">大道法则</option>
                            <option value="penetration">破防</option>
                        </select>
                    </div>
                    <div class="col-5">
                        <input type="number" class="form-control attribute-value" placeholder="${getPlaceholder(type)}">
                    </div>
                    <div class="col-2">
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeAttribute(this)">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            `;
            container.appendChild(div);
        }

        // 获取占位符文本
        function getPlaceholder(type) {
            switch(type) {
                case 'base': return '基础数值';
                case 'level': return '每级成长';
                case 'advancement': return '每次进阶';
                default: return '数值';
            }
        }

        // 移除属性输入框
        function removeAttribute(button) {
            button.closest('.attribute-input').remove();
        }

        // 清空属性
        function clearAttributes() {
            document.getElementById('baseAttributesContainer').innerHTML = '';
            document.getElementById('levelAttributesContainer').innerHTML = '';
            document.getElementById('advancementAttributesContainer').innerHTML = '';
        }

        // 获取属性数据
        function getAttributes(type) {
            const containerMap = {
                'base': 'baseAttributesContainer',
                'level': 'levelAttributesContainer',
                'advancement': 'advancementAttributesContainer'
            };
            
            const container = document.getElementById(containerMap[type]);
            const attributes = {};
            
            container.querySelectorAll('.attribute-input').forEach(input => {
                const typeSelect = input.querySelector('.attribute-type');
                const valueInput = input.querySelector('.attribute-value');
                
                if (typeSelect.value && valueInput.value) {
                    attributes[typeSelect.value] = parseFloat(valueInput.value);
                }
            });
            
            return attributes;
        }

        // 加载属性数据到界面
        function loadAttributes(attributes, type) {
            const containerMap = {
                'base': 'baseAttributesContainer',
                'level': 'levelAttributesContainer',
                'advancement': 'advancementAttributesContainer'
            };
            
            const container = document.getElementById(containerMap[type]);
            container.innerHTML = '';
            
            Object.entries(attributes || {}).forEach(([key, value]) => {
                addAttribute(type);
                const lastInput = container.lastElementChild;
                lastInput.querySelector('.attribute-type').value = key;
                lastInput.querySelector('.attribute-value').value = value;
            });
            
            // 如果没有属性，至少添加一个空的输入框
            if (Object.keys(attributes || {}).length === 0) {
                addAttribute(type);
            }
        }
    </script>
</body>
</html> 