/**
 * CloudFunctionAdapter - 云函数数据连接适配器
 * 封装对游戏端databaseService云函数的调用
 * 
 * 功能特性:
 * - 支持8种操作类型 (create/read/update/delete/list/count/saveOfflineTime/getServerTime)
 * - 自动重试机制 (最多3次)
 * - 请求日志和性能监控
 * - 统一错误处理
 * - 环境配置管理
 */

const https = require('https');
const config = require('../config.example.js');

class CloudFunctionAdapter {
  constructor(options = {}) {
    // 云开发环境配置
    this.envId = options.envId || config.cloud.envId;
    this.functionName = options.functionName || 'databaseService';
    
    // 重试配置
    this.maxRetries = options.maxRetries || 3;
    this.retryDelay = options.retryDelay || 1000; // 1秒
    
    // 日志配置
    this.enableLogging = options.enableLogging !== false;
    this.enablePerformanceMonitoring = options.enablePerformanceMonitoring !== false;
    
    // 超时配置
    this.timeout = options.timeout || 30000; // 30秒
    
    console.log(`🔧 CloudFunctionAdapter初始化完成`);
    console.log(`📊 云环境: ${this.envId}`);
    console.log(`⚡ 函数名: ${this.functionName}`);
    console.log(`🔄 重试次数: ${this.maxRetries}`);
  }

  /**
   * 调用云函数的核心方法
   * @param {Object} params 请求参数
   * @param {number} retryCount 当前重试次数
   * @returns {Promise<Object>} 云函数返回结果
   */
  async callCloudFunction(params, retryCount = 0) {
    const startTime = Date.now();
    const requestId = this.generateRequestId();
    
    try {
      if (this.enableLogging) {
        console.log(`📤 [${requestId}] 云函数调用开始:`, {
          action: params.action,
          tableName: params.tableName,
          envId: this.envId,
          attempt: retryCount + 1
        });
      }

      const result = await this.makeHttpRequest(params, requestId);
      
      const duration = Date.now() - startTime;
      
      if (this.enablePerformanceMonitoring) {
        console.log(`⏱️ [${requestId}] 性能监控: ${duration}ms`);
      }
      
      if (this.enableLogging) {
        console.log(`📥 [${requestId}] 云函数调用成功:`, {
          success: result.success,
          duration: `${duration}ms`,
          dataLength: result.data ? JSON.stringify(result.data).length : 0
        });
      }

      // 验证返回结果
      if (!this.validateResponse(result)) {
        throw new Error('云函数返回数据格式无效');
      }

      return result;

    } catch (error) {
      const duration = Date.now() - startTime;
      
      console.error(`❌ [${requestId}] 云函数调用失败 (尝试 ${retryCount + 1}/${this.maxRetries + 1}):`, {
        error: error.message,
        duration: `${duration}ms`,
        params: params
      });

      // 判断是否需要重试
      if (retryCount < this.maxRetries && this.shouldRetry(error)) {
        console.log(`🔄 [${requestId}] ${this.retryDelay}ms后进行第${retryCount + 2}次尝试...`);
        
        await this.delay(this.retryDelay);
        return this.callCloudFunction(params, retryCount + 1);
      }

      // 重试失败或不可重试，抛出错误
      throw this.createDetailedError(error, params, requestId);
    }
  }

  /**
   * 发送HTTP请求到云函数
   * @param {Object} params 请求参数
   * @param {string} requestId 请求ID
   * @returns {Promise<Object>} 解析后的响应数据
   */
  async makeHttpRequest(params, requestId) {
    return new Promise((resolve, reject) => {
      const postData = JSON.stringify(params);
      
      const options = {
        hostname: `${this.envId}.tcb-api.tencentcloudapi.com`,
        port: 443,
        path: `/tcb-api/v1/environments/${this.envId}/functions/${this.functionName}/invoke`,
        method: 'POST',
        timeout: this.timeout,
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(postData),
          'User-Agent': 'AdminSystem/1.0',
          'X-Request-ID': requestId
        }
      };

      const req = https.request(options, (res) => {
        let body = '';
        
        res.on('data', (chunk) => {
          body += chunk;
        });
        
        res.on('end', () => {
          try {
            const result = JSON.parse(body);
            
            // 检查HTTP状态码
            if (res.statusCode !== 200) {
              reject(new Error(`HTTP ${res.statusCode}: ${result.message || body}`));
              return;
            }
            
            resolve(result);
          } catch (parseError) {
            reject(new Error(`JSON解析失败: ${parseError.message}, 响应: ${body.substring(0, 200)}`));
          }
        });
      });

      req.on('error', (error) => {
        reject(new Error(`网络请求失败: ${error.message}`));
      });

      req.on('timeout', () => {
        req.destroy();
        reject(new Error(`请求超时 (${this.timeout}ms)`));
      });

      req.write(postData);
      req.end();
    });
  }

  /**
   * 验证云函数响应格式
   * @param {Object} response 响应对象
   * @returns {boolean} 是否有效
   */
  validateResponse(response) {
    if (!response || typeof response !== 'object') {
      return false;
    }
    
    // 检查必要字段
    if (!response.hasOwnProperty('success')) {
      return false;
    }
    
    // 如果操作失败，检查错误信息
    if (!response.success && !response.error) {
      return false;
    }
    
    return true;
  }

  /**
   * 判断错误是否应该重试
   * @param {Error} error 错误对象
   * @returns {boolean} 是否应该重试
   */
  shouldRetry(error) {
    const retryableErrors = [
      'ECONNRESET',
      'ENOTFOUND', 
      'ECONNREFUSED',
      'ETIMEDOUT',
      '请求超时',
      '网络请求失败',
      'socket hang up'
    ];
    
    return retryableErrors.some(errorType => 
      error.message.includes(errorType)
    );
  }

  /**
   * 创建详细的错误对象
   * @param {Error} originalError 原始错误
   * @param {Object} params 请求参数
   * @param {string} requestId 请求ID
   * @returns {Error} 详细错误
   */
  createDetailedError(originalError, params, requestId) {
    const error = new Error(`云函数调用失败: ${originalError.message}`);
    error.originalError = originalError;
    error.requestId = requestId;
    error.params = params;
    error.envId = this.envId;
    error.functionName = this.functionName;
    error.timestamp = new Date().toISOString();
    
    return error;
  }

  /**
   * 延迟函数
   * @param {number} ms 延迟毫秒数
   * @returns {Promise<void>}
   */
  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 生成请求ID
   * @returns {string} 唯一请求ID
   */
  generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // ==================== 数据操作方法 ====================

  /**
   * 创建记录
   * @param {string} tableName 表名
   * @param {Object} data 创建的数据
   * @param {Object} options 选项
   * @returns {Promise<Object>} 创建结果
   */
  async create(tableName, data, options = {}) {
    const params = {
      action: 'create',
      tableName,
      data,
      envType: options.envType || 'prod'
    };
    
    return this.callCloudFunction(params);
  }

  /**
   * 读取记录
   * @param {string} tableName 表名
   * @param {Object} conditions 查询条件
   * @param {Object} options 选项
   * @returns {Promise<Object>} 查询结果
   */
  async read(tableName, conditions = {}, options = {}) {
    const params = {
      action: 'read',
      tableName,
      conditions,
      _id: options._id,
      limit: options.limit || 10,
      skip: options.skip || 0,
      envType: options.envType || 'prod'
    };
    
    return this.callCloudFunction(params);
  }

  /**
   * 获取记录 (read的别名)
   * @param {string} tableName 表名
   * @param {Object} conditions 查询条件
   * @param {Object} options 选项
   * @returns {Promise<Object>} 查询结果
   */
  async get(tableName, conditions = {}, options = {}) {
    return this.read(tableName, conditions, options);
  }

  /**
   * 更新记录
   * @param {string} tableName 表名
   * @param {Object} data 更新的数据
   * @param {Object} conditions 更新条件
   * @param {Object} options 选项
   * @returns {Promise<Object>} 更新结果
   */
  async update(tableName, data, conditions = {}, options = {}) {
    const params = {
      action: 'update',
      tableName,
      data,
      conditions,
      _id: options._id,
      envType: options.envType || 'prod'
    };
    
    return this.callCloudFunction(params);
  }

  /**
   * 删除记录
   * @param {string} tableName 表名
   * @param {Object} conditions 删除条件
   * @param {Object} options 选项
   * @returns {Promise<Object>} 删除结果
   */
  async delete(tableName, conditions = {}, options = {}) {
    const params = {
      action: 'delete',
      tableName,
      conditions,
      _id: options._id,
      envType: options.envType || 'prod'
    };
    
    return this.callCloudFunction(params);
  }

  /**
   * 列表查询
   * @param {string} tableName 表名
   * @param {Object} options 选项
   * @returns {Promise<Object>} 列表结果
   */
  async list(tableName, options = {}) {
    const params = {
      action: 'list',
      tableName,
      limit: options.limit || 10,
      skip: options.skip || 0,
      envType: options.envType || 'prod'
    };
    
    return this.callCloudFunction(params);
  }

  /**
   * 计数查询
   * @param {string} tableName 表名
   * @param {Object} conditions 查询条件
   * @param {Object} options 选项
   * @returns {Promise<Object>} 计数结果
   */
  async count(tableName, conditions = {}, options = {}) {
    const params = {
      action: 'count',
      tableName,
      conditions,
      envType: options.envType || 'prod'
    };
    
    return this.callCloudFunction(params);
  }

  /**
   * 保存离线时间 (特殊操作)
   * @param {Object} options 选项
   * @returns {Promise<Object>} 操作结果
   */
  async saveOfflineTime(options = {}) {
    const params = {
      action: 'saveOfflineTime',
      envType: options.envType || 'prod'
    };
    
    return this.callCloudFunction(params);
  }

  /**
   * 获取服务器时间 (特殊操作)
   * @param {Object} options 选项
   * @returns {Promise<Object>} 服务器时间
   */
  async getServerTime(options = {}) {
    const params = {
      action: 'getServerTime'
    };
    
    return this.callCloudFunction(params);
  }

  // ==================== 便捷方法 ====================

  /**
   * 获取玩家列表 (管理员专用)
   * @param {Object} filters 过滤条件
   * @param {Object} pagination 分页参数
   * @returns {Promise<Object>} 玩家列表
   */
  async getPlayerList(filters = {}, pagination = {}) {
    const options = {
      limit: pagination.limit || 20,
      skip: pagination.skip || 0
    };
    
    // 注意：这里需要特殊处理，因为普通用户只能看到自己的数据
    // 管理员需要特殊的权限或openid来访问所有用户数据
    return this.list('players', options);
  }

  /**
   * 获取统计数据
   * @returns {Promise<Object>} 统计数据
   */
  async getStats() {
    try {
      const [playersCount, resourcesCount, treasuresCount] = await Promise.all([
        this.count('players'),
        this.count('player_res'), 
        this.count('player_treasures')
      ]);
      
      return {
        success: true,
        data: {
          players: playersCount.data?.count || 0,
          resources: resourcesCount.data?.count || 0,
          treasures: treasuresCount.data?.count || 0,
          timestamp: Date.now()
        }
      };
    } catch (error) {
      console.error('获取统计数据失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 健康检查
   * @returns {Promise<Object>} 健康状态
   */
  async healthCheck() {
    try {
      const startTime = Date.now();
      const result = await this.getServerTime();
      const responseTime = Date.now() - startTime;
      
      return {
        success: true,
        data: {
          status: 'healthy',
          responseTime: `${responseTime}ms`,
          envId: this.envId,
          functionName: this.functionName,
          serverTime: result.data?.serverTime,
          timestamp: Date.now()
        }
      };
    } catch (error) {
      return {
        success: false,
        data: {
          status: 'unhealthy',
          error: error.message,
          envId: this.envId,
          functionName: this.functionName,
          timestamp: Date.now()
        }
      };
    }
  }
}

module.exports = CloudFunctionAdapter; 