/**
 * 古宝抽取场景类
 * 负责古宝的抽取功能，包含抽取动画和结果展示
 */

import BaseScene from './BaseScene.js';
import Button from '../ui/Button.js';
import AppContext from '../utils/AppContext.js';

class TreasureDrawScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager) {
    super(ctx, screenWidth, screenHeight, sceneManager);
    
    // 抽取状态
    this.isDrawing = false;
    this.drawResult = null;
    this.showResult = false;
    
    // 抽取次数和保底
    this.drawCount = 0;
    this.guaranteeCount = 10; // 10次保底
    
    // 动画相关
    this.particles = [];
    this.animationFrame = 0;
    this.resultAnimation = {
      scale: 0,
      alpha: 0,
      active: false
    };
    
    // 消息显示
    this.messageText = '';
    this.messageDisplayTime = 0;
    
    // 按钮区域
    this.drawButtonArea = {
      x: this.screenWidth / 2 - 100,
      y: this.screenHeight - 200,
      width: 200,
      height: 80
    };
    
    this.initUI();
  }

  initUI() {
    this.clearUIElements();
    
    // 创建返回按钮
    this.backButton = new Button(
      this.ctx,
      20,
      20,
      80,
      40,
      '返回',
      null,
      null,
      () => this.sceneManager.showScene('main')
    );
    this.addUIElement(this.backButton);
    
    // 创建抽取按钮
    this.drawButton = new Button(
      this.ctx,
      this.drawButtonArea.x,
      this.drawButtonArea.y,
      this.drawButtonArea.width,
      this.drawButtonArea.height,
      '抽取古宝',
      null,
      null,
      () => this.startDraw()
    );
    this.addUIElement(this.drawButton);
    
    // 创建十连抽按钮
    this.draw10Button = new Button(
      this.ctx,
      this.drawButtonArea.x,
      this.drawButtonArea.y + 100,
      this.drawButtonArea.width,
      this.drawButtonArea.height,
      '十连抽',
      null,
      null,
      () => this.startDraw(10)
    );
    this.addUIElement(this.draw10Button);
    
    // 加载保底计数
    this.loadDrawCount();
  }

  /**
   * 加载抽取次数
   */
  loadDrawCount() {
    const gameState = AppContext.game.gameStateManager;
    if (gameState && gameState.state.player) {
      this.drawCount = gameState.state.player.drawCount || 0;
    }
  }

  /**
   * 保存抽取次数
   */
  async saveDrawCount() {
    const gameState = AppContext.game.gameStateManager;
    if (gameState && gameState.state.player) {
      gameState.state.player.drawCount = this.drawCount;
      // 使用正确的保存方法（异步，但不等待完成）
      try {
        await gameState.saveGameState();
        console.log('抽取次数已保存');
      } catch (error) {
        console.error('保存抽取次数失败:', error);
      }
    }
  }

  /**
   * 开始抽取
   */
  async startDraw(count = 1) {
    if (this.isDrawing) return;
    
    // 检查资源
    const gameState = AppContext.game.gameStateManager;
    const playerResources = gameState.state.player.resources;
    const cost = count === 1 ? 100 : 900; // 单抽100仙玉，十连抽900仙玉（打9折）
    
    console.log('检查仙玉资源:', {
      playerResources: playerResources,
      xianyu: playerResources ? playerResources.xianyu : 'undefined',
      cost: cost
    });
    
    if ((playerResources.xianyu || 0) < cost) {
      this.showMessage(`仙玉不足！当前: ${playerResources.xianyu || 0}, 需要: ${cost}`);
      return;
    }
    
    this.isDrawing = true;
    this.showResult = false;
    
    // 播放抽取动画
    this.startDrawAnimation();
    
    try {
      // 临时使用本地模拟抽取进行测试
      console.log('使用本地模拟抽取进行测试');
      const result = this.simulateDrawFallback(count);
      
      if (result.success) {
        // 扣除资源
        playerResources.xianyu = (playerResources.xianyu || 0) - cost;
        
        // 更新抽取次数
        this.drawCount += count;
        await this.saveDrawCount();
        
        // 显示结果
        setTimeout(() => {
          this.showDrawResult(result.treasures);
          this.showMessage(`成功获得 ${result.treasures.length} 件古宝！`);
        }, 2000);
      } else {
        this.isDrawing = false;
        this.showMessage(result.message || '抽取失败');
      }
    } catch (error) {
      console.error('抽取古宝失败:', error);
      this.isDrawing = false;
      this.showMessage('网络错误，请重试');
    }
  }

  /**
   * 调用抽取云函数
   */
  async callDrawFunction(count) {
    try {
      console.log('开始调用drawTreasure云函数:', { count });
      
      // 调用云函数 (不需要传递openid，云函数会自动获取)
      const result = await wx.cloud.callFunction({
        name: 'drawTreasure',
        data: {
          count: count
        }
      });
      
      console.log('drawTreasure云函数调用结果:', result);
      return result.result;
    } catch (error) {
      console.error('调用抽取云函数失败:', error);
      // 如果云函数调用失败，使用本地模拟
      return this.simulateDrawFallback(count);
    }
  }

  /**
   * 备用本地模拟抽取（当云函数不可用时）
   */
  simulateDrawFallback(count) {
    const treasures = this.simulateDraw(count);
    return {
      success: true,
      treasures: treasures
    };
  }

  /**
   * 模拟抽取逻辑
   */
  simulateDraw(count) {
    const treasures = [];
    const treasureManager = AppContext.game.treasureManager;
    
    console.log('开始模拟抽取, treasureManager:', treasureManager);
    console.log('treasureManager.treasureData:', treasureManager ? treasureManager.treasureData : 'undefined');
    
    if (!treasureManager || !treasureManager.treasureData) {
      console.error('TreasureManager或古宝数据未初始化');
      // 返回一个测试古宝，确保界面能正常显示
      return [{
        treasure_id: 'ancient_sword',
        level: 1,
        star: 0,
        exp: 0,
        obtained_time: new Date()
      }];
    }
    
    const allTreasures = treasureManager.treasureData;
    
    console.log('古宝数据总数:', allTreasures.length);
    console.log('前3个古宝:', allTreasures.slice(0, 3));
    
    if (!allTreasures || allTreasures.length === 0) {
      console.error('古宝数据为空');
      return [];
    }
    
    for (let i = 0; i < count; i++) {
      // 计算稀有度概率
      let rarityNum;
      const roll = Math.random();
      const guaranteeLeft = this.guaranteeCount - (this.drawCount % this.guaranteeCount);
      
      if (guaranteeLeft === 1 || roll < 0.01) { // 1%传说概率或保底
        rarityNum = 5; // 传说 = 5星
      } else if (roll < 0.05) { // 4%史诗概率
        rarityNum = 4; // 史诗 = 4星
      } else if (roll < 0.15) { // 10%稀有概率
        rarityNum = 3; // 稀有 = 3星
      } else if (roll < 0.40) { // 25%精良概率
        rarityNum = 2; // 精良 = 2星
      } else { // 60%普通概率
        rarityNum = 1; // 普通 = 1星
      }
      
      console.log(`第${i+1}次抽取，稀有度: ${rarityNum}`);
      
      // 从对应稀有度中随机选择一个古宝
      const rarityTreasures = allTreasures.filter(t => t.rarity === rarityNum);
      console.log(`稀有度${rarityNum}的古宝数量:`, rarityTreasures.length);
      
      if (rarityTreasures.length === 0) {
        // 如果没有对应稀有度的古宝，从所有古宝中随机选择
        console.warn(`没有找到稀有度${rarityNum}的古宝，从所有古宝中随机选择`);
        const selectedTreasure = allTreasures[Math.floor(Math.random() * allTreasures.length)];
        
        treasures.push({
          treasure_id: selectedTreasure.id,
          level: 1,
          star: 0,
          exp: 0,
          obtained_time: new Date()
        });
      } else {
        const selectedTreasure = rarityTreasures[Math.floor(Math.random() * rarityTreasures.length)];
        console.log('选中的古宝:', selectedTreasure);
        
        treasures.push({
          treasure_id: selectedTreasure.id,
          level: 1,
          star: 0,
          exp: 0,
          obtained_time: new Date()
        });
      }
    }
    
    console.log('模拟抽取完成，获得古宝:', treasures);
    return treasures;
  }

  /**
   * 开始抽取动画
   */
  startDrawAnimation() {
    this.particles = [];
    this.animationFrame = 0;
    
    // 创建粒子效果
    const centerX = this.screenWidth / 2;
    const centerY = this.screenHeight / 2 - 100;
    
    for (let i = 0; i < 50; i++) {
      this.particles.push({
        x: centerX,
        y: centerY,
        vx: (Math.random() - 0.5) * 10,
        vy: (Math.random() - 0.5) * 10,
        size: Math.random() * 8 + 2,
        color: this.getRandomColor(),
        life: 1.0,
        decay: Math.random() * 0.02 + 0.01
      });
    }
  }

  /**
   * 获取随机颜色
   */
  getRandomColor() {
    const colors = ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57'];
    return colors[Math.floor(Math.random() * colors.length)];
  }

  /**
   * 显示抽取结果
   */
  showDrawResult(treasures) {
    this.isDrawing = false;
    this.drawResult = treasures;
    this.showResult = true;
    
    // 启动结果显示动画
    this.resultAnimation.active = true;
    this.resultAnimation.scale = 0;
    this.resultAnimation.alpha = 0;
    
    // 添加古宝到玩家背包
    this.addTreasuresToPlayer(treasures);
  }

  /**
   * 添加古宝到玩家数据
   */
  addTreasuresToPlayer(treasures) {
    const treasureManager = AppContext.game.treasureManager;
    const gameState = AppContext.game.gameStateManager;
    
    treasures.forEach(treasure => {
      // 生成唯一ID
      treasure._id = this.generateTreasureId();
      // 使用openid作为玩家标识
      treasure.player_id = gameState.getPlayerOpenID() || 'local_player';
      
      // 添加到管理器
      treasureManager.playerTreasures.push(treasure);
    });
    
    // 重新计算属性
    treasureManager.recalculatePlayerAttributes();
    
    // 保存游戏状态（异步，不阻塞界面）
    gameState.saveGameState().catch(error => {
      console.error('保存古宝数据失败:', error);
    });
    
    console.log('添加古宝到玩家背包:', treasures);
  }

  /**
   * 生成古宝ID
   */
  generateTreasureId() {
    return 'treasure_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 显示消息
   */
  showMessage(message) {
    console.log('消息:', message);
    
    // 显示消息到界面上
    this.messageText = message;
    this.messageDisplayTime = Date.now();
    
    // 3秒后清除消息
    setTimeout(() => {
      if (this.messageText === message) {
        this.messageText = '';
      }
    }, 3000);
  }

  /**
   * 更新场景
   */
  updateScene() {
    super.updateScene();
    
    // 更新粒子动画
    if (this.isDrawing) {
      this.updateParticles();
    }
    
    // 更新结果动画
    if (this.resultAnimation.active) {
      this.updateResultAnimation();
    }
  }

  /**
   * 更新粒子
   */
  updateParticles() {
    this.animationFrame++;
    
    for (let i = this.particles.length - 1; i >= 0; i--) {
      const particle = this.particles[i];
      
      particle.x += particle.vx;
      particle.y += particle.vy;
      particle.vy += 0.2; // 重力
      particle.life -= particle.decay;
      
      if (particle.life <= 0) {
        this.particles.splice(i, 1);
      }
    }
  }

  /**
   * 更新结果动画
   */
  updateResultAnimation() {
    const animation = this.resultAnimation;
    
    if (animation.scale < 1) {
      animation.scale += 0.05;
    }
    
    if (animation.alpha < 1) {
      animation.alpha += 0.03;
    }
    
    if (animation.scale >= 1 && animation.alpha >= 1) {
      animation.active = false;
    }
  }

  /**
   * 绘制场景
   */
  drawScene() {
    super.drawScene();
    
    // 绘制背景
    this.drawBackground();
    
    // 绘制标题
    this.drawTitle();
    
    // 绘制保底信息
    this.drawGuaranteeInfo();
    
    // 绘制消耗信息
    this.drawCostInfo();
    
    // 绘制粒子动画
    if (this.isDrawing) {
      this.drawParticles();
    }
    
    // 绘制抽取结果
    if (this.showResult && this.drawResult) {
      this.drawResultPanel();
    }
    
    // 绘制消息
    if (this.messageText) {
      this.drawMessage();
    }
  }
  
  /**
   * 绘制消息
   */
  drawMessage() {
    if (!this.messageText) return;
    
    // 消息背景
    const messageWidth = this.screenWidth * 0.8;
    const messageHeight = 60;
    const messageX = (this.screenWidth - messageWidth) / 2;
    const messageY = this.screenHeight / 2 - 100;
    
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    this.ctx.fillRect(messageX, messageY, messageWidth, messageHeight);
    this.ctx.strokeStyle = '#FFD700';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(messageX, messageY, messageWidth, messageHeight);
    
    // 消息文本
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.font = '16px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(this.messageText, this.screenWidth / 2, messageY + 35);
  }

  /**
   * 绘制背景
   */
  drawBackground() {
    // 渐变背景
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, '#2C1810');
    gradient.addColorStop(0.5, '#4A2C17');
    gradient.addColorStop(1, '#2C1810');
    
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }

  /**
   * 绘制标题
   */
  drawTitle() {
    this.ctx.fillStyle = '#FFD700';
    this.ctx.font = 'bold 28px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('古宝抽取', this.screenWidth / 2, 80);
  }

  /**
   * 绘制保底信息
   */
  drawGuaranteeInfo() {
    const guaranteeLeft = this.guaranteeCount - (this.drawCount % this.guaranteeCount);
    
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.font = '16px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(`再抽 ${guaranteeLeft} 次必得传说古宝`, this.screenWidth / 2, 130);
  }

  /**
   * 绘制消耗信息
   */
  drawCostInfo() {
    this.ctx.fillStyle = '#FFA500';
    this.ctx.font = '14px Arial';
    this.ctx.textAlign = 'center';
    
    const y = this.drawButtonArea.y - 20;
    this.ctx.fillText('单抽: 100仙玉', this.screenWidth / 2, y);
    this.ctx.fillText('十连抽: 900仙玉', this.screenWidth / 2, y + 80);
  }

  /**
   * 绘制粒子
   */
  drawParticles() {
    this.particles.forEach(particle => {
      this.ctx.save();
      this.ctx.globalAlpha = particle.life;
      this.ctx.fillStyle = particle.color;
      this.ctx.beginPath();
      this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
      this.ctx.fill();
      this.ctx.restore();
    });
  }

  /**
   * 绘制抽取结果面板
   */
  drawResultPanel() {
    if (!this.drawResult || !this.resultAnimation) return;
    
    // 绘制遮罩
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
    
    // 绘制结果面板
    const panelWidth = this.screenWidth * 0.8;
    const panelHeight = this.screenHeight * 0.6;
    const panelX = (this.screenWidth - panelWidth) / 2;
    const panelY = (this.screenHeight - panelHeight) / 2;
    
    this.ctx.save();
    this.ctx.globalAlpha = this.resultAnimation.alpha;
    
    // 面板背景
    this.ctx.fillStyle = '#2A2A3E';
    this.ctx.fillRect(panelX, panelY, panelWidth, panelHeight);
    this.ctx.strokeStyle = '#FFD700';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(panelX, panelY, panelWidth, panelHeight);
    
    // 标题
    this.ctx.fillStyle = '#FFD700';
    this.ctx.font = 'bold 20px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('获得古宝', this.screenWidth / 2, panelY + 40);
    
    // 绘制获得的古宝
    this.drawResult.forEach((treasure, index) => {
      const config = AppContext.game.treasureManager.getTreasureConfig(treasure.treasure_id);
      if (!config) return;
      
      const itemsPerRow = Math.min(this.drawResult.length, 5);
      const itemWidth = panelWidth / itemsPerRow;
      const col = index % itemsPerRow;
      const row = Math.floor(index / itemsPerRow);
      
      const x = panelX + col * itemWidth + itemWidth / 2;
      const y = panelY + 80 + row * 100;
      
      // 绘制稀有度背景
      const rarityConfig = AppContext.game.treasureManager.rarityConfig?.[config.rarity];
      const bgColor = rarityConfig?.color || '#666666';
      this.ctx.fillStyle = bgColor;
      this.ctx.fillRect(x - 30, y - 30, 60, 60);
      
      // 绘制古宝名称
      this.ctx.fillStyle = '#FFFFFF';
      this.ctx.font = '12px Arial';
      this.ctx.fillText(config.name, x, y + 50);
    });
    
    this.ctx.restore();
    
    // 关闭按钮
    this.ctx.fillStyle = '#FF4444';
    this.ctx.fillRect(panelX + panelWidth - 30, panelY + 5, 25, 25);
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.font = 'bold 16px Arial';
    this.ctx.fillText('×', panelX + panelWidth - 17, panelY + 22);
  }

  /**
   * 处理触摸事件
   */
  handleTouchEnd(x, y) {
    super.handleTouchEnd(x, y);
    
    // 检查是否点击了结果面板的关闭按钮
    if (this.showResult) {
      const panelWidth = this.screenWidth * 0.8;
      const panelX = (this.screenWidth - panelWidth) / 2;
      const panelY = (this.screenHeight - this.screenHeight * 0.6) / 2;
      
      if (x >= panelX + panelWidth - 30 && x <= panelX + panelWidth - 5 &&
          y >= panelY + 5 && y <= panelY + 30) {
        this.showResult = false;
        this.drawResult = null;
      }
    }
  }

  /**
   * 场景显示时调用
   */
  onShow(params) {
    super.onShow(params);
    this.loadDrawCount();
    this.initUI();
  }

  /**
   * 场景隐藏时调用
   */
  onHide() {
    super.onHide();
    this.isDrawing = false;
    this.showResult = false;
    this.particles = [];
  }
}

export default TreasureDrawScene; 