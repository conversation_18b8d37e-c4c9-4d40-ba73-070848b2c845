/**
 * 仙友系统场景
 * 显示仙友列表，支持查看仙友详情、好感度培养等功能
 * 版本: v1.0 - 基础仙友系统场景
 */

import BaseScene from './BaseScene.js';
import XianyouCard from '../ui/XianyouCard.js';
import FavorabilityBar from '../ui/FavorabilityBar.js';
import EnhancedButton from '../ui/EnhancedButton.js';

class XianyouScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager, resources) {
    super(ctx, screenWidth, screenHeight, sceneManager, resources);
    
    this.sceneName = 'xianyou';
    this.sceneTitle = '仙友系统';
    
    // 仙友卡片列表
    this.xianyouCards = [];
    this.selectedXianyou = null;
    
    // 滚动相关
    this.scrollY = 0;
    this.maxScrollY = 0;
    this.isDragging = false;
    this.lastY = 0;
    
    // 布局配置
    this.cardWidth = 180;
    this.cardHeight = 280;
    this.cardSpacing = 20;
    this.cardsPerRow = Math.floor((this.screenWidth - 40) / (this.cardWidth + this.cardSpacing));
    
    // 按钮
    this.buttons = [];
    
    // 测试数据
    this.testXianyouData = this.createTestData();
    
    this.initUI();
  }
  
  /**
   * 创建测试数据
   */
  createTestData() {
    return [
      {
        id: 1,
        name: '林清音',
        avatar: null,
        star: 3,
        favorability: 65,
        maxFavorability: 100,
        rarity: 'rare',
        description: '来自青云门的天才弟子，精通剑法和阵法。',
        attributes: {
          attack: 120,
          defense: 80,
          speed: 95,
          health: 200
        },
        skills: ['剑气纵横', '青云剑诀'],
        story: '自幼在青云门修炼，天赋异禀，深受师父器重。'
      },
      {
        id: 2,
        name: '萧炎',
        avatar: null,
        star: 5,
        favorability: 85,
        maxFavorability: 100,
        rarity: 'legendary',
        description: '拥有异火的炼药师，实力强悍。',
        attributes: {
          attack: 180,
          defense: 120,
          speed: 110,
          health: 300
        },
        skills: ['异火焚天', '炼药术', '斗技：焚决'],
        story: '曾经的废材少年，因获得异火而逆天改命。'
      },
      {
        id: 3,
        name: '紫嫣',
        avatar: null,
        star: 2,
        favorability: 30,
        maxFavorability: 100,
        rarity: 'common',
        description: '温柔善良的医修，擅长治疗术法。',
        attributes: {
          attack: 60,
          defense: 90,
          speed: 75,
          health: 180
        },
        skills: ['回春术', '护体灵光'],
        story: '出身医修世家，立志救死扶伤。'
      },
      {
        id: 4,
        name: '雷动',
        avatar: null,
        star: 4,
        favorability: 50,
        maxFavorability: 100,
        rarity: 'epic',
        description: '掌控雷电之力的修士，攻击力极强。',
        attributes: {
          attack: 160,
          defense: 100,
          speed: 130,
          health: 250
        },
        skills: ['雷霆万钧', '闪电突袭', '雷神之怒'],
        story: '天生雷灵根，修炼雷系功法事半功倍。'
      },
      {
        id: 5,
        name: '花无缺',
        avatar: null,
        star: 3,
        favorability: 40,
        maxFavorability: 100,
        rarity: 'rare',
        description: '移花宫少主，容貌绝世，武功高强。',
        attributes: {
          attack: 140,
          defense: 110,
          speed: 120,
          health: 220
        },
        skills: ['移花接玉', '明玉功', '嫁衣神功'],
        story: '移花宫培养的完美男子，武功绝顶。'
      },
      {
        id: 6,
        name: '小龙女',
        avatar: null,
        star: 4,
        favorability: 75,
        maxFavorability: 100,
        rarity: 'epic',
        description: '古墓派传人，清冷如仙，武功超凡。',
        attributes: {
          attack: 150,
          defense: 130,
          speed: 105,
          health: 280
        },
        skills: ['玉女心经', '古墓派轻功', '玉蜂针'],
        story: '古墓中长大的仙子，不谙世事却武功高强。'
      }
    ];
  }
  
  /**
   * 初始化UI
   */
  initUI() {
    this.clearUIElements();
    
    // 创建返回按钮
    const backButton = new EnhancedButton({
      x: 20,
      y: 50,
      width: 80,
      height: 40,
      text: '返回',
      type: 'secondary',
      onClick: () => this.sceneManager.showScene('main')
    });
    this.addUIElement(backButton);
    this.buttons.push(backButton);
    
    // 创建仙友抽取按钮
    const gachaButton = new EnhancedButton({
      x: this.screenWidth - 120,
      y: 50,
      width: 100,
      height: 40,
      text: '仙友召唤',
      type: 'primary',
      onClick: () => this.openGachaDialog()
    });
    this.addUIElement(gachaButton);
    this.buttons.push(gachaButton);
    
    // 创建仙友卡片
    this.createXianyouCards();
    
    // 计算最大滚动距离
    this.calculateMaxScroll();
  }
  
  /**
   * 创建仙友卡片
   */
  createXianyouCards() {
    this.xianyouCards = [];
    
    const startX = 20;
    const startY = 120;
    
    this.testXianyouData.forEach((xianyou, index) => {
      const row = Math.floor(index / this.cardsPerRow);
      const col = index % this.cardsPerRow;
      
      const x = startX + col * (this.cardWidth + this.cardSpacing);
      const y = startY + row * (this.cardHeight + this.cardSpacing);
      
      const card = new XianyouCard({
        x: x,
        y: y,
        width: this.cardWidth,
        height: this.cardHeight,
        xianyouData: xianyou,
        onClick: (data) => this.onXianyouCardClick(data),
        onLongPress: (data) => this.onXianyouCardLongPress(data)
      });
      
      this.xianyouCards.push(card);
      this.addUIElement(card);
    });
  }
  
  /**
   * 计算最大滚动距离
   */
  calculateMaxScroll() {
    const rows = Math.ceil(this.testXianyouData.length / this.cardsPerRow);
    const totalHeight = 120 + rows * (this.cardHeight + this.cardSpacing);
    const visibleHeight = this.screenHeight - 160; // 减去顶部和底部空间
    this.maxScrollY = Math.max(0, totalHeight - visibleHeight);
  }
  
  /**
   * 仙友卡片点击事件
   */
  onXianyouCardClick(xianyouData) {
    console.log('点击仙友:', xianyouData.name);
    this.selectedXianyou = xianyouData;
    this.showXianyouDetail(xianyouData);
  }
  
  /**
   * 仙友卡片长按事件
   */
  onXianyouCardLongPress(xianyouData) {
    console.log('长按仙友:', xianyouData.name);
    this.showXianyouMenu(xianyouData);
  }
  
  /**
   * 显示仙友详情
   */
  showXianyouDetail(xianyouData) {
    wx.showModal({
      title: xianyouData.name,
      content: `${xianyouData.description}\n\n星级: ${xianyouData.star}星\n好感度: ${xianyouData.favorability}/${xianyouData.maxFavorability}\n稀有度: ${this.getRarityName(xianyouData.rarity)}\n\n属性:\n攻击: ${xianyouData.attributes.attack}\n防御: ${xianyouData.attributes.defense}\n速度: ${xianyouData.attributes.speed}\n生命: ${xianyouData.attributes.health}\n\n技能: ${xianyouData.skills.join(', ')}\n\n背景: ${xianyouData.story}`,
      showCancel: true,
      cancelText: '关闭',
      confirmText: '培养好感度',
      success: (res) => {
        if (res.confirm) {
          this.improveFavorability(xianyouData);
        }
      }
    });
  }
  
  /**
   * 显示仙友菜单
   */
  showXianyouMenu(xianyouData) {
    wx.showActionSheet({
      itemList: ['查看详情', '培养好感度', '升星', '设置为主仙友'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.showXianyouDetail(xianyouData);
            break;
          case 1:
            this.improveFavorability(xianyouData);
            break;
          case 2:
            this.upgradeXianyouStar(xianyouData);
            break;
          case 3:
            this.setMainXianyou(xianyouData);
            break;
        }
      }
    });
  }
  
  /**
   * 提升好感度
   */
  improveFavorability(xianyouData) {
    const improvement = Math.floor(Math.random() * 10) + 5; // 随机提升5-14点
    const oldValue = xianyouData.favorability;
    xianyouData.favorability = Math.min(xianyouData.favorability + improvement, xianyouData.maxFavorability);
    
    // 更新对应卡片
    const card = this.xianyouCards.find(c => c.xianyouData && c.xianyouData.id === xianyouData.id);
    if (card) {
      card.updateXianyouData(xianyouData);
    }
    
    wx.showToast({
      title: `${xianyouData.name}好感度+${xianyouData.favorability - oldValue}`,
      icon: 'success',
      duration: 2000
    });
  }
  
  /**
   * 升星
   */
  upgradeXianyouStar(xianyouData) {
    if (xianyouData.star >= 5) {
      wx.showToast({
        title: '已达最高星级',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    // 简单的升星逻辑
    const success = Math.random() > 0.3; // 70%成功率
    
    if (success) {
      xianyouData.star++;
      
      // 升星后属性提升
      xianyouData.attributes.attack = Math.floor(xianyouData.attributes.attack * 1.2);
      xianyouData.attributes.defense = Math.floor(xianyouData.attributes.defense * 1.2);
      xianyouData.attributes.speed = Math.floor(xianyouData.attributes.speed * 1.1);
      xianyouData.attributes.health = Math.floor(xianyouData.attributes.health * 1.2);
      
      // 更新对应卡片
      const card = this.xianyouCards.find(c => c.xianyouData && c.xianyouData.id === xianyouData.id);
      if (card) {
        card.updateXianyouData(xianyouData);
      }
      
      wx.showToast({
        title: `${xianyouData.name}升星成功！`,
        icon: 'success',
        duration: 2000
      });
    } else {
      wx.showToast({
        title: '升星失败',
        icon: 'none',
        duration: 2000
      });
    }
  }
  
  /**
   * 设置为主仙友
   */
  setMainXianyou(xianyouData) {
    // 清除其他仙友的选中状态
    this.xianyouCards.forEach(card => card.setSelected(false));
    
    // 设置当前仙友为选中状态
    const card = this.xianyouCards.find(c => c.xianyouData && c.xianyouData.id === xianyouData.id);
    if (card) {
      card.setSelected(true);
    }
    
    this.selectedXianyou = xianyouData;
    
    wx.showToast({
      title: `${xianyouData.name}已设为主仙友`,
      icon: 'success',
      duration: 2000
    });
  }
  
  /**
   * 打开抽取对话框
   */
  openGachaDialog() {
    wx.showModal({
      title: '仙友召唤',
      content: '是否消耗100仙玉进行仙友召唤？',
      success: (res) => {
        if (res.confirm) {
          this.performGacha();
        }
      }
    });
  }
  
  /**
   * 执行抽取
   */
  performGacha() {
    wx.showLoading({
      title: '召唤中...',
      mask: true
    });
    
    // 模拟抽取延迟
    setTimeout(() => {
      wx.hideLoading();
      
      // 简单的抽取逻辑
      const rarities = ['common', 'rare', 'epic', 'legendary'];
      const chances = [0.5, 0.3, 0.15, 0.05]; // 50%, 30%, 15%, 5%
      
      let rarity = 'common';
      const random = Math.random();
      let cumulative = 0;
      
      for (let i = 0; i < chances.length; i++) {
        cumulative += chances[i];
        if (random <= cumulative) {
          rarity = rarities[i];
          break;
        }
      }
      
      const newXianyou = this.generateRandomXianyou(rarity);
      this.testXianyouData.push(newXianyou);
      
      // 重新创建卡片
      this.createXianyouCards();
      this.calculateMaxScroll();
      
      wx.showModal({
        title: '召唤成功！',
        content: `恭喜获得${this.getRarityName(rarity)}仙友：${newXianyou.name}`,
        showCancel: false,
        confirmText: '确定'
      });
    }, 2000);
  }
  
  /**
   * 生成随机仙友
   */
  generateRandomXianyou(rarity) {
    const names = ['李逍遥', '赵灵儿', '林月如', '阿奴', '酒剑仙', '拜月教主', '景天', '雪见', '龙葵', '紫萱'];
    const randomName = names[Math.floor(Math.random() * names.length)];
    const id = this.testXianyouData.length + 1;
    
    const baseStats = {
      common: { attack: 60, defense: 60, speed: 60, health: 150 },
      rare: { attack: 90, defense: 90, speed: 90, health: 200 },
      epic: { attack: 130, defense: 130, speed: 130, health: 270 },
      legendary: { attack: 170, defense: 170, speed: 170, health: 350 }
    };
    
    const stats = baseStats[rarity];
    const variation = 0.2; // 20%属性浮动
    
    return {
      id: id,
      name: randomName,
      avatar: null,
      star: rarity === 'legendary' ? 4 : (rarity === 'epic' ? 3 : (rarity === 'rare' ? 2 : 1)),
      favorability: 0,
      maxFavorability: 100,
      rarity: rarity,
      description: `神秘的${this.getRarityName(rarity)}仙友`,
      attributes: {
        attack: Math.floor(stats.attack * (1 + (Math.random() - 0.5) * variation)),
        defense: Math.floor(stats.defense * (1 + (Math.random() - 0.5) * variation)),
        speed: Math.floor(stats.speed * (1 + (Math.random() - 0.5) * variation)),
        health: Math.floor(stats.health * (1 + (Math.random() - 0.5) * variation))
      },
      skills: ['基础技能'],
      story: '来历神秘的仙友。'
    };
  }
  
  /**
   * 获取稀有度名称
   */
  getRarityName(rarity) {
    const names = {
      common: '普通',
      rare: '稀有',
      epic: '史诗',
      legendary: '传说'
    };
    return names[rarity] || '未知';
  }
  
  /**
   * 处理触摸开始
   */
  handleTouchStart(x, y) {
    // 先处理UI元素
    if (super.handleTouchStart(x, y)) {
      return true;
    }
    
    // 检查是否在可滚动区域
    if (y > 100 && y < this.screenHeight - 80) {
      this.isDragging = true;
      this.lastY = y;
      return true;
    }
    
    return false;
  }
  
  /**
   * 处理触摸移动
   */
  handleTouchMove(x, y) {
    if (super.handleTouchMove(x, y)) {
      return true;
    }
    
    if (this.isDragging) {
      const deltaY = y - this.lastY;
      this.scrollY = Math.max(0, Math.min(this.maxScrollY, this.scrollY - deltaY));
      this.lastY = y;
      
      // 更新卡片位置
      this.updateCardPositions();
      return true;
    }
    
    return false;
  }
  
  /**
   * 处理触摸结束
   */
  handleTouchEnd(x, y) {
    if (super.handleTouchEnd(x, y)) {
      return true;
    }
    
    if (this.isDragging) {
      this.isDragging = false;
      return true;
    }
    
    return false;
  }
  
  /**
   * 更新卡片位置
   */
  updateCardPositions() {
    const startX = 20;
    const startY = 120 - this.scrollY;
    
    this.xianyouCards.forEach((card, index) => {
      const row = Math.floor(index / this.cardsPerRow);
      const col = index % this.cardsPerRow;
      
      const x = startX + col * (this.cardWidth + this.cardSpacing);
      const y = startY + row * (this.cardHeight + this.cardSpacing);
      
      card.setPosition(x, y);
    });
  }
  
  /**
   * 更新场景
   */
  updateScene(deltaTime) {
    super.updateScene(deltaTime);
    
    // 更新仙友卡片
    this.xianyouCards.forEach(card => {
      if (card.update) {
        card.update(deltaTime);
      }
    });
  }
  
  /**
   * 绘制场景
   */
  drawScene() {
    // 绘制背景
    this.drawBackground();
    
    // 绘制标题
    this.drawTitle();
    
    // 绘制仙友信息摘要
    this.drawSummary();
    
    // 绘制仙友卡片
    this.drawXianyouCards();
  }
  
  /**
   * 绘制仙友卡片
   */
  drawXianyouCards() {
    // 设置裁剪区域，防止卡片绘制到标题和导航栏区域
    this.ctx.save();
    this.ctx.beginPath();
    this.ctx.rect(0, 120, this.screenWidth, this.screenHeight - 200);
    this.ctx.clip();
    
    // 绘制所有仙友卡片
    this.xianyouCards.forEach(card => {
      if (card.render) {
        card.render(this.ctx);
      }
    });
    
    this.ctx.restore();
  }
  
  /**
   * 绘制背景
   */
  drawBackground() {
    // 使用渐变背景
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, '#4A148C');
    gradient.addColorStop(0.5, '#7B1FA2');
    gradient.addColorStop(1, '#9C27B0');
    
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }
  
  /**
   * 绘制标题
   */
  drawTitle() {
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText('仙友系统', this.screenWidth / 2, 70);
  }
  
  /**
   * 绘制摘要信息
   */
  drawSummary() {
    const totalXianyou = this.testXianyouData.length;
    const selectedName = this.selectedXianyou ? this.selectedXianyou.name : '未选择';
    
    this.ctx.font = '14px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.textBaseline = 'top';
    this.ctx.fillText(`拥有仙友: ${totalXianyou}个`, 20, 100);
    
    this.ctx.textAlign = 'right';
    this.ctx.fillText(`主仙友: ${selectedName}`, this.screenWidth - 20, 100);
  }
  
  /**
   * 场景显示时调用
   */
  onShow(params) {
    super.onShow(params);
    console.log('仙友系统场景显示');
    
    // 确保测试数据和卡片已创建
    if (this.testXianyouData.length === 0) {
      console.log('创建测试数据...');
      this.createTestData();
      this.createXianyouCards();
      this.calculateMaxScroll();
      this.updateCardPositions();
    }
  }
  
  /**
   * 场景隐藏时调用
   */
  onHide() {
    super.onHide();
    console.log('仙友系统场景隐藏');
  }
}

export default XianyouScene; 