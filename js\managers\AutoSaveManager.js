/**
 * 自动保存管理器
 * 负责定时自动保存游戏数据到云数据库
 */

class AutoSaveManager {
  constructor(game) {
    this.game = game;
    this.autoSaveInterval = null;
    this.autoSaveIntervalTime = 5 * 60 * 1000; // 5分钟
    this.isAutoSaveEnabled = false;
    this.lastSaveTime = 0;
    this.saveInProgress = false;
    
    console.log('自动保存管理器初始化完成');
  }

  /**
   * 启动自动保存
   */
  startAutoSave() {
    if (this.isAutoSaveEnabled) {
      console.log('自动保存已经启动');
      return;
    }

    // 检查用户是否已登录
    if (!this.game.loginManager || !this.game.loginManager.isLoggedIn) {
      console.log('用户未登录，无法启动自动保存');
      return;
    }

    this.isAutoSaveEnabled = true;
    this.lastSaveTime = Date.now();

    // 设置定时器
    this.autoSaveInterval = setInterval(() => {
      this.performAutoSave();
    }, this.autoSaveIntervalTime);

    console.log(`自动保存已启动，间隔时间: ${this.autoSaveIntervalTime / 1000}秒`);
  }

  /**
   * 停止自动保存
   */
  stopAutoSave() {
    if (!this.isAutoSaveEnabled) {
      return;
    }

    this.isAutoSaveEnabled = false;

    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval);
      this.autoSaveInterval = null;
    }

    console.log('自动保存已停止');
  }

  /**
   * 执行自动保存
   */
  async performAutoSave() {
    // 检查是否正在保存中
    if (this.saveInProgress) {
      console.log('保存正在进行中，跳过本次自动保存');
      return;
    }

    // 检查用户是否仍然登录
    if (!this.game.loginManager || !this.game.loginManager.isLoggedIn) {
      console.log('用户未登录，停止自动保存');
      this.stopAutoSave();
      return;
    }

    try {
      this.saveInProgress = true;
      const now = Date.now();
      
      console.log('开始执行自动保存...');
      
      // 执行保存
      const success = await this.game.gameStateManager.saveGameState();
      
      if (success) {
        this.lastSaveTime = now;
        console.log('自动保存成功');
        
        // 显示保存成功提示（可选）
        if (typeof wx !== 'undefined') {
          wx.showToast({
            title: '数据已自动保存',
            icon: 'success',
            duration: 1000
          });
        }
      } else {
        console.error('自动保存失败');
      }
    } catch (error) {
      console.error('自动保存过程中出错:', error);
    } finally {
      this.saveInProgress = false;
    }
  }

  /**
   * 手动触发保存
   */
  async manualSave() {
    if (this.saveInProgress) {
      console.log('保存正在进行中，请稍后再试');
      return false;
    }

    try {
      this.saveInProgress = true;
      console.log('开始手动保存...');
      
      const success = await this.game.gameStateManager.saveGameState();
      
      if (success) {
        this.lastSaveTime = Date.now();
        console.log('手动保存成功');
        
        if (typeof wx !== 'undefined') {
          wx.showToast({
            title: '保存成功',
            icon: 'success',
            duration: 2000
          });
        }
        return true;
      } else {
        console.error('手动保存失败');
        
        if (typeof wx !== 'undefined') {
          wx.showToast({
            title: '保存失败',
            icon: 'error',
            duration: 2000
          });
        }
        return false;
      }
    } catch (error) {
      console.error('手动保存过程中出错:', error);
      
      if (typeof wx !== 'undefined') {
        wx.showToast({
          title: '保存出错',
          icon: 'error',
          duration: 2000
        });
      }
      return false;
    } finally {
      this.saveInProgress = false;
    }
  }

  /**
   * 游戏退出时保存
   */
  async saveOnExit() {
    if (this.saveInProgress) {
      console.log('保存正在进行中，等待完成...');
      // 等待当前保存完成
      while (this.saveInProgress) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    try {
      this.saveInProgress = true;
      console.log('游戏退出，执行最终保存...');
      
      const success = await this.game.gameStateManager.saveGameState();
      
      if (success) {
        console.log('退出保存成功');
      } else {
        console.error('退出保存失败');
      }
      
      return success;
    } catch (error) {
      console.error('退出保存过程中出错:', error);
      return false;
    } finally {
      this.saveInProgress = false;
    }
  }

  /**
   * 设置自动保存间隔时间
   * @param {number} intervalMinutes 间隔时间（分钟）
   */
  setAutoSaveInterval(intervalMinutes) {
    if (intervalMinutes < 1) {
      console.warn('自动保存间隔时间不能少于1分钟');
      return;
    }

    this.autoSaveIntervalTime = intervalMinutes * 60 * 1000;
    
    // 如果自动保存正在运行，重新启动以应用新的间隔时间
    if (this.isAutoSaveEnabled) {
      this.stopAutoSave();
      this.startAutoSave();
    }
    
    console.log(`自动保存间隔时间已设置为: ${intervalMinutes}分钟`);
  }

  /**
   * 获取自动保存状态
   */
  getAutoSaveStatus() {
    return {
      isEnabled: this.isAutoSaveEnabled,
      intervalTime: this.autoSaveIntervalTime,
      lastSaveTime: this.lastSaveTime,
      saveInProgress: this.saveInProgress,
      nextSaveTime: this.isAutoSaveEnabled ? this.lastSaveTime + this.autoSaveIntervalTime : null
    };
  }

  /**
   * 获取距离下次自动保存的剩余时间
   * @returns {number} 剩余时间（毫秒）
   */
  getTimeUntilNextSave() {
    if (!this.isAutoSaveEnabled) {
      return -1;
    }

    const now = Date.now();
    const nextSaveTime = this.lastSaveTime + this.autoSaveIntervalTime;
    return Math.max(0, nextSaveTime - now);
  }

  /**
   * 获取距离下次自动保存的剩余时间（格式化字符串）
   * @returns {string} 格式化的剩余时间
   */
  getFormattedTimeUntilNextSave() {
    const remainingTime = this.getTimeUntilNextSave();
    
    if (remainingTime === -1) {
      return '自动保存未启动';
    }

    if (remainingTime === 0) {
      return '即将保存';
    }

    const minutes = Math.floor(remainingTime / 60000);
    const seconds = Math.floor((remainingTime % 60000) / 1000);
    
    return `${minutes}分${seconds}秒`;
  }

  /**
   * 销毁自动保存管理器
   */
  destroy() {
    this.stopAutoSave();
    this.game = null;
    console.log('自动保存管理器已销毁');
  }
}

export default AutoSaveManager;
