/**
 * CloudFunctionTest - 云函数适配器测试类
 * 用于测试CloudFunctionAdapter的各种功能
 */

const CloudFunctionAdapter = require('./CloudFunctionAdapter');

class CloudFunctionTest {
  constructor() {
    this.adapter = new CloudFunctionAdapter({
      enableLogging: true,
      enablePerformanceMonitoring: true,
      maxRetries: 2,
      timeout: 20000
    });
    
    console.log('🧪 CloudFunctionTest初始化完成');
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('\n🚀 开始运行云函数适配器测试...\n');
    
    const tests = [
      { name: '健康检查测试', method: 'testHealthCheck' },
      { name: '获取服务器时间测试', method: 'testGetServerTime' },
      { name: '统计数据测试', method: 'testGetStats' },
      { name: '玩家数据读取测试', method: 'testReadPlayers' },
      { name: '计数测试', method: 'testCount' },
      { name: '列表查询测试', method: 'testList' }
    ];
    
    const results = [];
    
    for (const test of tests) {
      try {
        console.log(`📋 执行测试: ${test.name}`);
        const startTime = Date.now();
        
        const result = await this[test.method]();
        const duration = Date.now() - startTime;
        
        results.push({
          name: test.name,
          success: true,
          duration: `${duration}ms`,
          result: result
        });
        
        console.log(`✅ ${test.name} - 通过 (${duration}ms)\n`);
        
      } catch (error) {
        console.error(`❌ ${test.name} - 失败:`, error.message);
        
        results.push({
          name: test.name,
          success: false,
          error: error.message
        });
        
        console.log(''); // 空行分隔
      }
    }
    
    // 输出测试总结
    this.printTestSummary(results);
    return results;
  }

  /**
   * 健康检查测试
   */
  async testHealthCheck() {
    const result = await this.adapter.healthCheck();
    
    if (!result.success) {
      throw new Error(`健康检查失败: ${result.data?.error || '未知错误'}`);
    }
    
    console.log('  💚 系统状态:', result.data.status);
    console.log('  ⏱️ 响应时间:', result.data.responseTime);
    console.log('  🕒 服务器时间:', result.data.serverTime);
    
    return result;
  }

  /**
   * 获取服务器时间测试
   */
  async testGetServerTime() {
    const result = await this.adapter.getServerTime();
    
    if (!result.success) {
      throw new Error(`获取服务器时间失败: ${result.error || '未知错误'}`);
    }
    
    console.log('  🕒 服务器时间:', result.data?.serverTime);
    
    return result;
  }

  /**
   * 统计数据测试
   */
  async testGetStats() {
    const result = await this.adapter.getStats();
    
    if (!result.success) {
      throw new Error(`获取统计数据失败: ${result.error || '未知错误'}`);
    }
    
    console.log('  👥 玩家数量:', result.data.players);
    console.log('  💎 资源记录:', result.data.resources);
    console.log('  🏺 古宝记录:', result.data.treasures);
    
    return result;
  }

  /**
   * 玩家数据读取测试
   */
  async testReadPlayers() {
    const result = await this.adapter.read('players', {}, { limit: 5 });
    
    if (!result.success) {
      throw new Error(`读取玩家数据失败: ${result.error || '未知错误'}`);
    }
    
    const players = result.data?.records || [];
    console.log(`  📊 获取到 ${players.length} 条玩家记录`);
    
    if (players.length > 0) {
      const firstPlayer = players[0];
      console.log('  👤 示例玩家:', {
        nickname: firstPlayer.nickname || '未知',
        level: firstPlayer.level || 0,
        cultivation_realm: firstPlayer.cultivation_realm || '未知'
      });
    }
    
    return result;
  }

  /**
   * 计数测试
   */
  async testCount() {
    const result = await this.adapter.count('players');
    
    if (!result.success) {
      throw new Error(`计数测试失败: ${result.error || '未知错误'}`);
    }
    
    console.log('  🔢 玩家总数:', result.data?.count || 0);
    
    return result;
  }

  /**
   * 列表查询测试
   */
  async testList() {
    const result = await this.adapter.list('skill_temp', { limit: 3 });
    
    if (!result.success) {
      throw new Error(`列表查询失败: ${result.error || '未知错误'}`);
    }
    
    const skills = result.data?.records || [];
    console.log(`  📜 技能模板数量: ${skills.length}`);
    
    if (skills.length > 0) {
      const firstSkill = skills[0];
      console.log('  ⚔️ 示例技能:', {
        name: firstSkill.name || '未知',
        type: firstSkill.type || '未知',
        level: firstSkill.level || 0
      });
    }
    
    return result;
  }

  /**
   * 快速测试 (仅健康检查)
   */
  async quickTest() {
    console.log('\n⚡ 运行快速连接测试...\n');
    
    try {
      const result = await this.testHealthCheck();
      console.log('✅ 快速测试通过 - 系统正常运行\n');
      return result;
    } catch (error) {
      console.error('❌ 快速测试失败:', error.message);
      throw error;
    }
  }

  /**
   * 打印测试总结
   */
  printTestSummary(results) {
    console.log('\n' + '='.repeat(50));
    console.log('📊 测试总结');
    console.log('='.repeat(50));
    
    const passed = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    const total = results.length;
    
    console.log(`总测试数: ${total}`);
    console.log(`✅ 通过: ${passed}`);
    console.log(`❌ 失败: ${failed}`);
    console.log(`📈 成功率: ${((passed/total) * 100).toFixed(1)}%`);
    
    console.log('\n📋 详细结果:');
    results.forEach((result, index) => {
      const status = result.success ? '✅' : '❌';
      const time = result.duration ? ` (${result.duration})` : '';
      console.log(`${index + 1}. ${status} ${result.name}${time}`);
      
      if (!result.success) {
        console.log(`   错误: ${result.error}`);
      }
    });
    
    console.log('\n' + '='.repeat(50) + '\n');
  }

  /**
   * 性能测试
   */
  async performanceTest(iterations = 10) {
    console.log(`\n🏃‍♂️ 开始性能测试 (${iterations} 次迭代)...\n`);
    
    const times = [];
    
    for (let i = 1; i <= iterations; i++) {
      const startTime = Date.now();
      
      try {
        await this.adapter.getServerTime();
        const duration = Date.now() - startTime;
        times.push(duration);
        
        console.log(`第${i}次调用: ${duration}ms`);
        
      } catch (error) {
        console.error(`第${i}次调用失败:`, error.message);
      }
    }
    
    if (times.length > 0) {
      const avgTime = Math.round(times.reduce((a, b) => a + b, 0) / times.length);
      const minTime = Math.min(...times);
      const maxTime = Math.max(...times);
      
      console.log('\n📊 性能统计:');
      console.log(`⏱️ 平均响应时间: ${avgTime}ms`);
      console.log(`🚀 最快响应时间: ${minTime}ms`);
      console.log(`🐌 最慢响应时间: ${maxTime}ms`);
      console.log(`📈 成功率: ${((times.length/iterations) * 100).toFixed(1)}%`);
    }
    
    return {
      iterations,
      successCount: times.length,
      avgTime: times.length > 0 ? Math.round(times.reduce((a, b) => a + b, 0) / times.length) : 0,
      minTime: times.length > 0 ? Math.min(...times) : 0,
      maxTime: times.length > 0 ? Math.max(...times) : 0
    };
  }
}

module.exports = CloudFunctionTest; 