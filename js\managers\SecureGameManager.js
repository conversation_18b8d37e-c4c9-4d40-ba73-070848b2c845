/**
 * 安全游戏管理器
 * 所有玩家数据变更操作都通过安全云函数执行
 * 客户端只负责展示数据，不直接修改数据
 */

class SecureGameManager {
  constructor(gameStateManager) {
    this.gameStateManager = gameStateManager;
    this.isInitialized = false;
    this.pendingOperations = new Map(); // 追踪进行中的操作
    
    console.log('安全游戏管理器初始化完成');
  }
  
  /**
   * 初始化安全管理器
   */
  async initialize() {
    try {
      console.log('开始初始化安全游戏管理器...');
      
      // 从云端加载最新的玩家数据
      await this.loadPlayerDataFromServer();
      
      this.isInitialized = true;
      console.log('安全游戏管理器初始化完成');
      
      return true;
    } catch (error) {
      console.error('安全游戏管理器初始化失败:', error);
      return false;
    }
  }
  
  /**
   * 从服务器加载玩家数据
   */
  async loadPlayerDataFromServer() {
    try {
      console.log('从服务器加载玩家数据...');
      
      const result = await this.callSecureFunction('getPlayerData', {});
      
      if (result.success) {
        const { playerData, resources } = result.data;
        
        // 更新游戏状态
        this.updateGameStateFromServer(playerData, resources);
        
        console.log('玩家数据加载成功');
        return true;
      } else {
        throw new Error(result.error || '加载玩家数据失败');
      }
    } catch (error) {
      console.error('从服务器加载玩家数据失败:', error);
      throw error;
    }
  }
  
  /**
   * 更新游戏状态（基于服务器数据）
   */
  updateGameStateFromServer(playerData, resources) {
    try {
      if (!this.gameStateManager.state.player) {
        this.gameStateManager.state.player = {};
      }
      
      // 更新玩家基础信息
      Object.assign(this.gameStateManager.state.player, {
        nickname: playerData.nickname,
        avatarUrl: playerData.avatar_url,
        level: playerData.level,
        exp: playerData.exp,
        power: playerData.power,
        cultivation_realm: playerData.cultivation_realm,
        dongfu_level: playerData.dongfu_level,
        vip_level: playerData.vip_level,
        total_recharge: playerData.total_recharge
      });
      
      // 更新资源信息
      this.gameStateManager.state.player.resources = {
        xianyu: resources.xianyu,
        lingshi: resources.lingshi,
        swordIntent: resources.sword_intent,
        lianlidian: resources.lianlidian,
        spiritStone: resources.spirit_stone || 0,
        tiangangStone: resources.tiangang_stone || 0,
        xiuweiPoint: resources.xiuwei_point || 0,
        arenaPoint: resources.arena_point || 0,
        guildContribution: resources.guild_contribution || 0
      };
      
      console.log('游戏状态已更新:', {
        level: playerData.level,
        power: playerData.power,
        resources: this.gameStateManager.state.player.resources
      });
      
      // 触发数据更新事件
      this.gameStateManager.emit('serverDataUpdated', {
        playerData: this.gameStateManager.state.player,
        resources: this.gameStateManager.state.player.resources
      });
      
    } catch (error) {
      console.error('更新游戏状态失败:', error);
    }
  }
  
  /**
   * 安全地添加资源
   */
  async addResources(resources, reason = 'game_reward') {
    try {
      const operationId = this.generateOperationId();
      console.log(`安全添加资源 [${operationId}]:`, resources, '原因:', reason);
      
      // 检查是否有重复操作
      if (this.pendingOperations.has('addResources')) {
        console.warn('有正在进行的添加资源操作，跳过');
        return false;
      }
      
      this.pendingOperations.set('addResources', operationId);
      
      try {
        const result = await this.callSecureFunction('addResources', {
          resources: resources,
          reason: reason
        });
        
        if (result.success) {
          // 更新本地状态
          this.updateLocalResources(result.data.newResources);
          
          console.log(`资源添加成功 [${operationId}]:`, result.data.changes);
          return result.data;
        } else {
          throw new Error(result.error);
        }
      } finally {
        this.pendingOperations.delete('addResources');
      }
      
    } catch (error) {
      console.error('安全添加资源失败:', error);
      
      // 显示错误提示
      if (typeof wx !== 'undefined') {
        wx.showToast({
          title: '操作失败: ' + error.message,
          icon: 'none',
          duration: 3000
        });
      }
      
      throw error;
    }
  }
  
  /**
   * 安全地消耗资源
   */
  async consumeResources(resources, reason = 'game_action') {
    try {
      const operationId = this.generateOperationId();
      console.log(`安全消耗资源 [${operationId}]:`, resources, '原因:', reason);
      
      // 检查是否有重复操作
      if (this.pendingOperations.has('consumeResources')) {
        console.warn('有正在进行的消耗资源操作，跳过');
        return false;
      }
      
      this.pendingOperations.set('consumeResources', operationId);
      
      try {
        const result = await this.callSecureFunction('consumeResources', {
          resources: resources,
          reason: reason
        });
        
        if (result.success) {
          // 更新本地状态
          this.updateLocalResources(result.data.newResources);
          
          console.log(`资源消耗成功 [${operationId}]:`, result.data.consumed);
          return result.data;
        } else {
          throw new Error(result.error);
        }
      } finally {
        this.pendingOperations.delete('consumeResources');
      }
      
    } catch (error) {
      console.error('安全消耗资源失败:', error);
      
      // 显示错误提示
      if (typeof wx !== 'undefined') {
        wx.showToast({
          title: '操作失败: ' + error.message,
          icon: 'none',
          duration: 3000
        });
      }
      
      throw error;
    }
  }
  
  /**
   * 安全地升级
   */
  async levelUp() {
    try {
      const operationId = this.generateOperationId();
      console.log(`安全升级 [${operationId}]`);
      
      // 检查是否有重复操作
      if (this.pendingOperations.has('levelUp')) {
        console.warn('有正在进行的升级操作，跳过');
        return false;
      }
      
      this.pendingOperations.set('levelUp', operationId);
      
      try {
        const result = await this.callSecureFunction('levelUp', {});
        
        if (result.success) {
          // 更新本地状态
          this.updateLocalPlayerData({
            level: result.data.newLevel,
            exp: result.data.remainingExp,
            power: result.data.newPower
          });
          
          console.log(`升级成功 [${operationId}]:`, {
            oldLevel: result.data.oldLevel,
            newLevel: result.data.newLevel,
            newPower: result.data.newPower
          });
          
          // 触发升级事件
          this.gameStateManager.emit('playerLevelUpSecure', result.data);
          
          return result.data;
        } else {
          throw new Error(result.error);
        }
      } finally {
        this.pendingOperations.delete('levelUp');
      }
      
    } catch (error) {
      console.error('安全升级失败:', error);
      
      // 显示错误提示
      if (typeof wx !== 'undefined') {
        wx.showToast({
          title: '升级失败: ' + error.message,
          icon: 'none',
          duration: 3000
        });
      }
      
      throw error;
    }
  }
  
  /**
   * 安全地境界突破
   */
  async realmBreakthrough() {
    try {
      const operationId = this.generateOperationId();
      console.log(`安全境界突破 [${operationId}]`);
      
      // 检查是否有重复操作
      if (this.pendingOperations.has('realmBreakthrough')) {
        console.warn('有正在进行的境界突破操作，跳过');
        return false;
      }
      
      this.pendingOperations.set('realmBreakthrough', operationId);
      
      try {
        const result = await this.callSecureFunction('realmBreakthrough', {});
        
        if (result.success) {
          // 更新本地状态
          this.updateLocalPlayerData({
            cultivation_realm: result.data.newRealm,
            power: result.data.newPower
          });
          
          console.log(`境界突破成功 [${operationId}]:`, {
            oldRealm: result.data.oldRealm,
            newRealm: result.data.newRealm,
            newPower: result.data.newPower,
            cost: result.data.cost
          });
          
          // 触发境界突破事件
          this.gameStateManager.emit('realmBreakthroughSecure', result.data);
          
          return result.data;
        } else {
          throw new Error(result.error);
        }
      } finally {
        this.pendingOperations.delete('realmBreakthrough');
      }
      
    } catch (error) {
      console.error('安全境界突破失败:', error);
      
      // 显示错误提示
      if (typeof wx !== 'undefined') {
        wx.showToast({
          title: '突破失败: ' + error.message,
          icon: 'none',
          duration: 3000
        });
      }
      
      throw error;
    }
  }
  
  /**
   * 更新本地资源状态
   */
  updateLocalResources(newResources) {
    if (!this.gameStateManager.state.player) {
      this.gameStateManager.state.player = {};
    }
    
    if (!this.gameStateManager.state.player.resources) {
      this.gameStateManager.state.player.resources = {};
    }
    
    // 更新资源数据
    Object.assign(this.gameStateManager.state.player.resources, {
      xianyu: newResources.xianyu,
      lingshi: newResources.lingshi,
      swordIntent: newResources.sword_intent,
      lianlidian: newResources.lianlidian,
      spiritStone: newResources.spirit_stone || 0,
      tiangangStone: newResources.tiangang_stone || 0,
      xiuweiPoint: newResources.xiuwei_point || 0,
      arenaPoint: newResources.arena_point || 0,
      guildContribution: newResources.guild_contribution || 0
    });
    
    // 触发资源更新事件
    this.gameStateManager.emit('resourcesUpdatedSecure', {
      resources: this.gameStateManager.state.player.resources
    });
  }
  
  /**
   * 更新本地玩家数据
   */
  updateLocalPlayerData(updateData) {
    if (!this.gameStateManager.state.player) {
      this.gameStateManager.state.player = {};
    }
    
    // 更新玩家数据
    Object.assign(this.gameStateManager.state.player, updateData);
    
    // 触发玩家数据更新事件
    this.gameStateManager.emit('playerDataUpdatedSecure', {
      playerData: this.gameStateManager.state.player
    });
  }
  
  /**
   * 调用安全云函数
   */
  async callSecureFunction(action, data) {
    try {
      console.log(`调用安全云函数: ${action}`, data);
      
      const result = await wx.cloud.callFunction({
        name: 'secureGameActions',
        data: {
          action: action,
          data: data
        }
      });
      
      console.log(`安全云函数 ${action} 调用结果:`, result.result);
      
      return result.result;
    } catch (error) {
      console.error(`调用安全云函数 ${action} 失败:`, error);
      throw new Error(`网络错误: ${error.message}`);
    }
  }
  
  /**
   * 生成操作ID
   */
  generateOperationId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
  
  /**
   * 检查操作是否正在进行
   */
  isOperationPending(operation) {
    return this.pendingOperations.has(operation);
  }
  
  /**
   * 获取所有进行中的操作
   */
  getPendingOperations() {
    return Array.from(this.pendingOperations.keys());
  }
}

export default SecureGameManager; 