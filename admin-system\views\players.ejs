<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
        }
        .sidebar .nav-link {
            color: #adb5bd;
        }
        .sidebar .nav-link:hover {
            color: #fff;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #495057;
        }
        .main-content {
            padding: 20px;
        }
        .player-card {
            transition: transform 0.2s;
        }
        .player-card:hover {
            transform: translateY(-2px);
        }
        .selected-player {
            border: 2px solid #007bff;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <h5 class="text-white text-center mb-4">修仙后台</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item"><a class="nav-link <%= title === '数据总览' ? 'active' : '' %>" href="/"><i class="bi bi-speedometer2 me-2"></i>数据总览</a></li>
                        <li class="nav-item"><a class="nav-link <%= title === '数据统计' ? 'active' : '' %>" href="/dashboard"><i class="bi bi-graph-up me-2"></i>数据统计</a></li>
                        <li class="nav-item"><a class="nav-link <%= title === '玩家管理' ? 'active' : '' %>" href="/players"><i class="bi bi-people me-2"></i>玩家管理</a></li>
                        <li class="nav-item"><a class="nav-link <%= title === '玩家资源' ? 'active' : '' %>" href="/player-resources"><i class="bi bi-gem me-2"></i>玩家资源</a></li>
                        <li class="nav-item"><a class="nav-link <%= title === '装备技能' ? 'active' : '' %>" href="/player-equipment"><i class="bi bi-shield-check me-2"></i>装备技能</a></li>
                        <li class="nav-item"><a class="nav-link <%= title.includes('邮件') ? 'active' : '' %>" href="/mails"><i class="bi bi-envelope-fill me-2"></i>邮件管理</a></li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">玩家管理</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" onclick="showSendMailModal()">
                            <i class="bi bi-envelope-plus"></i>
                            发送邮件
                        </button>
                    </div>
                </div>

                <!-- 搜索栏 -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchInput" placeholder="搜索玩家 (OpenID 或昵称)">
                            <button class="btn btn-outline-secondary" type="button" onclick="searchPlayers()">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-primary" onclick="selectAllPlayers()">全选</button>
                            <button class="btn btn-outline-secondary" onclick="clearSelection()">清空选择</button>
                            <span class="badge bg-info align-self-center" id="selectedCount">已选择: 0</span>
                        </div>
                    </div>
                </div>

                <!-- 玩家列表 -->
                <div class="row" id="playersList">
                    <!-- 玩家卡片将在这里动态生成 -->
                </div>

                <!-- 分页 -->
                <nav aria-label="玩家列表分页">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- 分页按钮将在这里动态生成 -->
                    </ul>
                </nav>
            </main>
        </div>
    </div>

    <!-- 发送邮件模态框 -->
    <div class="modal fade" id="sendMailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">发送邮件</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="sendMailForm">
                        <div class="mb-3">
                            <label for="mailTitle" class="form-label">邮件标题</label>
                            <input type="text" class="form-control" id="mailTitle" required>
                        </div>
                        <div class="mb-3">
                            <label for="mailContent" class="form-label">邮件内容</label>
                            <textarea class="form-control" id="mailContent" rows="4" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="mailSender" class="form-label">发送者</label>
                            <input type="text" class="form-control" id="mailSender" value="系统管理员">
                        </div>
                        
                        <!-- 奖励设置 -->
                        <div class="mb-3">
                            <h6>奖励设置</h6>
                            <div class="row">
                                <div class="col-md-3">
                                    <label for="rewardXianyu" class="form-label">仙玉</label>
                                    <input type="number" class="form-control" id="rewardXianyu" min="0" value="0">
                                </div>
                                <div class="col-md-3">
                                    <label for="rewardLingshi" class="form-label">灵石</label>
                                    <input type="number" class="form-control" id="rewardLingshi" min="0" value="0">
                                </div>
                                <div class="col-md-3">
                                    <label for="rewardLianlidian" class="form-label">历练点</label>
                                    <input type="number" class="form-control" id="rewardLianlidian" min="0" value="0">
                                </div>
                                <div class="col-md-3">
                                    <label for="rewardSwordIntent" class="form-label">剑意</label>
                                    <input type="number" class="form-control" id="rewardSwordIntent" min="0" value="0">
                                </div>
                            </div>
                        </div>

                        <!-- 物品奖励 -->
                        <div class="mb-3">
                            <h6>物品奖励</h6>
                            <div id="itemRewards">
                                <div class="row mb-2">
                                    <div class="col-md-4">
                                        <input type="text" class="form-control" placeholder="物品名称" name="itemName">
                                    </div>
                                    <div class="col-md-2">
                                        <input type="number" class="form-control" placeholder="数量" name="itemCount" min="1" value="1">
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-control" name="itemType">
                                            <option value="material">材料</option>
                                            <option value="consumable">消耗品</option>
                                            <option value="equipment">装备</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <button type="button" class="btn btn-outline-success" onclick="addItemReward()">
                                            <i class="bi bi-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">接收者 (<span id="recipientCount">0</span> 人)</label>
                            <div class="form-text">已选择的玩家将接收此邮件</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="sendMail()">发送邮件</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 玩家详情模态框 -->
    <div class="modal fade" id="playerDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">玩家详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="playerDetailContent">
                        <!-- 详情内容将在这里动态加载 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="editPlayerFromDetail()">编辑信息</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑玩家模态框 -->
    <div class="modal fade" id="editPlayerModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑玩家信息</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editPlayerForm">
                        <input type="hidden" id="editPlayerId">
                        
                        <!-- 基础信息 -->
                        <h6 class="mb-3">基础信息</h6>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="editNickname" class="form-label">昵称</label>
                                <input type="text" class="form-control" id="editNickname" maxlength="20">
                            </div>
                            <div class="col-md-6">
                                <label for="editServerName" class="form-label">服务器</label>
                                <input type="text" class="form-control" id="editServerName">
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="editLevel" class="form-label">等级</label>
                                <input type="number" class="form-control" id="editLevel" min="1" max="1000">
                            </div>
                            <div class="col-md-4">
                                <label for="editVipLevel" class="form-label">VIP等级</label>
                                <input type="number" class="form-control" id="editVipLevel" min="0" max="20">
                            </div>
                            <div class="col-md-4">
                                <label for="editCultivationRealm" class="form-label">修炼境界</label>
                                <input type="text" class="form-control" id="editCultivationRealm">
                            </div>
                        </div>

                        <!-- 资源信息 -->
                        <h6 class="mb-3">资源信息</h6>
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label for="editXianyu" class="form-label">仙玉</label>
                                <input type="number" class="form-control" id="editXianyu" min="0">
                            </div>
                            <div class="col-md-3">
                                <label for="editLingshi" class="form-label">灵石</label>
                                <input type="number" class="form-control" id="editLingshi" min="0">
                            </div>
                            <div class="col-md-3">
                                <label for="editLianlidian" class="form-label">历练点</label>
                                <input type="number" class="form-control" id="editLianlidian" min="0">
                            </div>
                            <div class="col-md-3">
                                <label for="editSwordIntent" class="form-label">剑意</label>
                                <input type="number" class="form-control" id="editSwordIntent" min="0">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="editArenaScore" class="form-label">竞技场积分</label>
                                <input type="number" class="form-control" id="editArenaScore" min="0">
                            </div>
                            <div class="col-md-6">
                                <label for="editBattlePower" class="form-label">战斗力</label>
                                <input type="number" class="form-control" id="editBattlePower" min="0">
                            </div>
                        </div>

                        <!-- 状态管理 -->
                        <h6 class="mb-3">状态管理</h6>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="editIsBanned">
                                <label class="form-check-label" for="editIsBanned">
                                    封禁账号
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="savePlayerChanges()">保存修改</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let players = [];
        let selectedPlayers = new Set();
        let currentPage = 1;
        let totalPages = 1;

        // 加载玩家列表
        async function loadPlayers(page = 1, search = '') {
            try {
                const response = await fetch(`/api/players?page=${page}&limit=12&search=${encodeURIComponent(search)}`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });
                const data = await response.json();
                
                if (data.success) {
                    players = data.data;
                    currentPage = data.page;
                    totalPages = Math.ceil(data.total / data.limit);
                    
                    renderPlayers();
                    renderPagination();
                } else {
                    console.error('加载玩家列表失败:', data.error);
                }
            } catch (error) {
                console.error('加载玩家列表失败:', error);
            }
        }

        // 渲染玩家列表
        function renderPlayers() {
            const container = document.getElementById('playersList');
            container.innerHTML = '';

            players.forEach(player => {
                const isSelected = selectedPlayers.has(player._id);
                const card = document.createElement('div');
                card.className = 'col-md-4 mb-3';
                card.innerHTML = `
                    <div class="card player-card ${isSelected ? 'selected-player' : ''}" onclick="togglePlayerSelection('${player._id}')">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start">
                                <h6 class="card-title">${player.nickname || '未设置昵称'}</h6>
                                <input type="checkbox" ${isSelected ? 'checked' : ''} onclick="event.stopPropagation()">
                            </div>
                            <p class="card-text">
                                <small class="text-muted">OpenID: ${player._openid.substring(0, 10)}...</small><br>
                                <small class="text-muted">服务器: ${player.server_name || '未知'}</small><br>
                                <small class="text-muted">VIP等级: ${player.vip_level || 0}</small><br>
                                <small class="text-muted">仙玉: ${player.xianyu || 0}</small>
                            </p>
                        </div>
                        <div class="card-footer">
                            <div class="btn-group w-100" role="group">
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); viewPlayerDetail('${player._id}')">
                                    <i class="bi bi-eye"></i> 查看
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-success" onclick="event.stopPropagation(); editPlayer('${player._id}')">
                                    <i class="bi bi-pencil"></i> 编辑
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                container.appendChild(card);
            });

            updateSelectedCount();
        }

        // 渲染分页
        function renderPagination() {
            const container = document.getElementById('pagination');
            container.innerHTML = '';

            // 上一页
            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
            prevLi.innerHTML = `<a class="page-link" href="#" onclick="loadPlayers(${currentPage - 1})">上一页</a>`;
            container.appendChild(prevLi);

            // 页码
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                const li = document.createElement('li');
                li.className = `page-item ${i === currentPage ? 'active' : ''}`;
                li.innerHTML = `<a class="page-link" href="#" onclick="loadPlayers(${i})">${i}</a>`;
                container.appendChild(li);
            }

            // 下一页
            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
            nextLi.innerHTML = `<a class="page-link" href="#" onclick="loadPlayers(${currentPage + 1})">下一页</a>`;
            container.appendChild(nextLi);
        }

        // 切换玩家选择状态
        function togglePlayerSelection(playerId) {
            if (selectedPlayers.has(playerId)) {
                selectedPlayers.delete(playerId);
            } else {
                selectedPlayers.add(playerId);
            }
            renderPlayers();
        }

        // 全选玩家
        function selectAllPlayers() {
            players.forEach(player => selectedPlayers.add(player._id));
            renderPlayers();
        }

        // 清空选择
        function clearSelection() {
            selectedPlayers.clear();
            renderPlayers();
        }

        // 更新选择计数
        function updateSelectedCount() {
            document.getElementById('selectedCount').textContent = `已选择: ${selectedPlayers.size}`;
            document.getElementById('recipientCount').textContent = selectedPlayers.size;
        }

        // 搜索玩家
        function searchPlayers() {
            const search = document.getElementById('searchInput').value;
            loadPlayers(1, search);
        }

        // 显示发送邮件模态框
        function showSendMailModal() {
            if (selectedPlayers.size === 0) {
                alert('请先选择要发送邮件的玩家');
                return;
            }
            
            const modal = new bootstrap.Modal(document.getElementById('sendMailModal'));
            modal.show();
        }

        // 添加物品奖励行
        function addItemReward() {
            const container = document.getElementById('itemRewards');
            const newRow = document.createElement('div');
            newRow.className = 'row mb-2';
            newRow.innerHTML = `
                <div class="col-md-4">
                    <input type="text" class="form-control" placeholder="物品名称" name="itemName">
                </div>
                <div class="col-md-2">
                    <input type="number" class="form-control" placeholder="数量" name="itemCount" min="1" value="1">
                </div>
                <div class="col-md-3">
                    <select class="form-control" name="itemType">
                        <option value="material">材料</option>
                        <option value="consumable">消耗品</option>
                        <option value="equipment">装备</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="button" class="btn btn-outline-danger" onclick="this.parentElement.parentElement.remove()">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            container.appendChild(newRow);
        }

        // 发送邮件
        async function sendMail() {
            const title = document.getElementById('mailTitle').value;
            const content = document.getElementById('mailContent').value;
            const sender = document.getElementById('mailSender').value;

            if (!title || !content) {
                alert('请填写邮件标题和内容');
                return;
            }

            if (selectedPlayers.size === 0) {
                alert('请选择接收邮件的玩家');
                return;
            }

            // 收集奖励信息
            const rewards = {
                xianyu: parseInt(document.getElementById('rewardXianyu').value) || 0,
                lingshi: parseInt(document.getElementById('rewardLingshi').value) || 0,
                lianlidian: parseInt(document.getElementById('rewardLianlidian').value) || 0,
                sword_intent: parseInt(document.getElementById('rewardSwordIntent').value) || 0,
                items: []
            };

            // 收集物品奖励
            const itemRows = document.querySelectorAll('#itemRewards .row');
            itemRows.forEach(row => {
                const name = row.querySelector('input[name="itemName"]').value;
                const count = parseInt(row.querySelector('input[name="itemCount"]').value) || 1;
                const type = row.querySelector('select[name="itemType"]').value;
                
                if (name) {
                    rewards.items.push({ name, count, type });
                }
            });

            // 准备接收者列表
            const recipients = players
                .filter(player => selectedPlayers.has(player._id))
                .map(player => ({
                    openid: player._openid,
                    nickname: player.nickname
                }));

            try {
                const response = await fetch('/api/send-mail', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify({
                        recipients,
                        title,
                        content,
                        sender,
                        rewards
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    alert('邮件发送成功！');
                    const modal = bootstrap.Modal.getInstance(document.getElementById('sendMailModal'));
                    modal.hide();
                    
                    // 重置表单
                    document.getElementById('sendMailForm').reset();
                    clearSelection();
                } else {
                    alert('邮件发送失败: ' + result.error);
                }
            } catch (error) {
                console.error('发送邮件失败:', error);
                alert('邮件发送失败，请稍后重试');
            }
        }

        // 查看玩家详情
        async function viewPlayerDetail(playerId) {
            try {
                const response = await fetch(`/api/players/${playerId}`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });
                const data = await response.json();
                
                if (data.success) {
                    const player = data.data;
                    const resources = player.resources || {};
                    
                    const detailHtml = `
                        <div class="row">
                            <div class="col-md-6">
                                <h6>基础信息</h6>
                                <table class="table table-sm">
                                    <tr><td>昵称</td><td>${player.nickname || '未设置'}</td></tr>
                                    <tr><td>OpenID</td><td>${player._openid}</td></tr>
                                    <tr><td>等级</td><td>${player.level || 0}</td></tr>
                                    <tr><td>修炼境界</td><td>${player.cultivation_realm || '未知'}</td></tr>
                                    <tr><td>服务器</td><td>${player.server_name || '未知'}</td></tr>
                                    <tr><td>VIP等级</td><td>${player.vip_level || 0}</td></tr>
                                    <tr><td>账号状态</td><td>${player.is_banned ? '<span class="badge bg-danger">已封禁</span>' : '<span class="badge bg-success">正常</span>'}</td></tr>
                                    <tr><td>创建时间</td><td>${new Date(player.createdAt).toLocaleString()}</td></tr>
                                    <tr><td>最后登录</td><td>${player.last_login_time ? new Date(player.last_login_time).toLocaleString() : '从未登录'}</td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>资源信息</h6>
                                <table class="table table-sm">
                                    <tr><td>仙玉</td><td>${resources.xianyu || 0}</td></tr>
                                    <tr><td>灵石</td><td>${resources.lingshi || 0}</td></tr>
                                    <tr><td>历练点</td><td>${resources.lianlidian || 0}</td></tr>
                                    <tr><td>剑意</td><td>${resources.sword_intent || 0}</td></tr>
                                    <tr><td>竞技场积分</td><td>${resources.arena_score || 0}</td></tr>
                                    <tr><td>战斗力</td><td>${resources.battle_power || 0}</td></tr>
                                    <tr><td>修炼进度</td><td>${resources.cultivation_progress || 0}%</td></tr>
                                </table>
                            </div>
                        </div>
                    `;
                    
                    document.getElementById('playerDetailContent').innerHTML = detailHtml;
                    
                    // 存储当前查看的玩家ID，用于编辑
                    window.currentViewingPlayerId = playerId;
                    
                    const modal = new bootstrap.Modal(document.getElementById('playerDetailModal'));
                    modal.show();
                } else {
                    alert('获取玩家详情失败: ' + data.error);
                }
            } catch (error) {
                console.error('获取玩家详情失败:', error);
                alert('获取玩家详情失败，请稍后重试');
            }
        }

        // 从详情页面进入编辑
        function editPlayerFromDetail() {
            if (window.currentViewingPlayerId) {
                editPlayer(window.currentViewingPlayerId);
                // 关闭详情模态框
                const detailModal = bootstrap.Modal.getInstance(document.getElementById('playerDetailModal'));
                detailModal.hide();
            }
        }

        // 编辑玩家信息
        async function editPlayer(playerId) {
            try {
                const response = await fetch(`/api/players/${playerId}`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });
                const data = await response.json();
                
                if (data.success) {
                    const player = data.data;
                    const resources = player.resources || {};
                    
                    // 填充表单数据
                    document.getElementById('editPlayerId').value = playerId;
                    document.getElementById('editNickname').value = player.nickname || '';
                    document.getElementById('editServerName').value = player.server_name || '';
                    document.getElementById('editLevel').value = player.level || 1;
                    document.getElementById('editVipLevel').value = player.vip_level || 0;
                    document.getElementById('editCultivationRealm').value = player.cultivation_realm || '';
                    document.getElementById('editXianyu').value = resources.xianyu || 0;
                    document.getElementById('editLingshi').value = resources.lingshi || 0;
                    document.getElementById('editLianlidian').value = resources.lianlidian || 0;
                    document.getElementById('editSwordIntent').value = resources.sword_intent || 0;
                    document.getElementById('editArenaScore').value = resources.arena_score || 0;
                    document.getElementById('editBattlePower').value = resources.battle_power || 0;
                    document.getElementById('editIsBanned').checked = player.is_banned || false;
                    
                    const modal = new bootstrap.Modal(document.getElementById('editPlayerModal'));
                    modal.show();
                } else {
                    alert('获取玩家信息失败: ' + data.error);
                }
            } catch (error) {
                console.error('获取玩家信息失败:', error);
                alert('获取玩家信息失败，请稍后重试');
            }
        }

        // 保存玩家修改
        async function savePlayerChanges() {
            try {
                const playerId = document.getElementById('editPlayerId').value;
                
                // 准备基础信息数据
                const playerData = {
                    nickname: document.getElementById('editNickname').value,
                    server_name: document.getElementById('editServerName').value,
                    level: parseInt(document.getElementById('editLevel').value),
                    vip_level: parseInt(document.getElementById('editVipLevel').value),
                    cultivation_realm: document.getElementById('editCultivationRealm').value,
                    is_banned: document.getElementById('editIsBanned').checked
                };
                
                // 准备资源数据
                const resourceData = {
                    xianyu: parseInt(document.getElementById('editXianyu').value),
                    lingshi: parseInt(document.getElementById('editLingshi').value),
                    lianlidian: parseInt(document.getElementById('editLianlidian').value),
                    sword_intent: parseInt(document.getElementById('editSwordIntent').value),
                    arena_score: parseInt(document.getElementById('editArenaScore').value),
                    battle_power: parseInt(document.getElementById('editBattlePower').value)
                };
                
                // 并行更新基础信息和资源信息
                const [playerResponse, resourceResponse] = await Promise.all([
                    fetch(`/api/players/${playerId}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${localStorage.getItem('token')}`
                        },
                        body: JSON.stringify(playerData)
                    }),
                    fetch(`/api/players/${playerId}/resources`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${localStorage.getItem('token')}`
                        },
                        body: JSON.stringify(resourceData)
                    })
                ]);
                
                const playerResult = await playerResponse.json();
                const resourceResult = await resourceResponse.json();
                
                if (playerResult.success && resourceResult.success) {
                    alert('玩家信息更新成功！');
                    
                    // 关闭编辑模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('editPlayerModal'));
                    modal.hide();
                    
                    // 刷新玩家列表
                    loadPlayers(currentPage);
                } else {
                    const errors = [];
                    if (!playerResult.success) errors.push('基础信息：' + playerResult.error);
                    if (!resourceResult.success) errors.push('资源信息：' + resourceResult.error);
                    alert('更新失败：\n' + errors.join('\n'));
                }
            } catch (error) {
                console.error('保存玩家信息失败:', error);
                alert('保存失败，请稍后重试');
            }
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否有token
            const token = localStorage.getItem('token');
            if (!token) {
                window.location.href = '/login';
                return;
            }
            
            loadPlayers();
            
            // 搜索框回车事件
            document.getElementById('searchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchPlayers();
                }
            });
        });
    </script>
</body>
</html>
