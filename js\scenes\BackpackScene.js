/**
 * 背包场景类
 * 管理玩家的物品和装备
 */
import BaseScene from './BaseScene';
import Button from '../ui/Button';
import game from '../../game';
import AppContext from '../utils/AppContext';
import TitleBar from '../ui/TitleBar';

class BackpackScene extends BaseScene {
  constructor(ctx, screenWidth, screenHeight, sceneManager,resources) {
    super(ctx, screenWidth, screenHeight, sceneManager);

    // 场景资源
    this.resources = resources || {};

    // 当前选中的底部导航项
    this.selectedTabIndex = 4; // 背包对应的导航索引

    // 背包类别
    this.categories = [
      { id: 'all', name: '全部' },
      { id: 'consumable', name: '消耗品' },
      { id: 'material', name: '材料' },
      { id: 'equipment', name: '装备' }
    ];

    // 当前选中的类别
    this.selectedCategory = this.categories[0];

    // 页面状态
    this.items = [];
    this.equipments = [];
    this.displayItems = [];
    this.currentPage = 0;
    this.itemsPerPage = 12;

    // 顶部导航栏
    this.titleBar = null;
  }

  // 初始化UI
  initUI() {
    // 获取资源

    // 创建顶部标题栏
    const headerHeight = 80;
    this.titleBar = new TitleBar(
      this.ctx,
      0,
      0,
      this.screenWidth,
      headerHeight,
      this.resources
    );
    this.addUIElement(this.titleBar);

    // 底部导航栏按钮
    const tabBarHeight = 60;
    const tabBarY = this.screenHeight - tabBarHeight;
    const tabButtonWidth = this.screenWidth / 5;

    // 创建分类按钮
    const categoryButtonWidth = this.screenWidth / this.categories.length;
    const categoryButtonHeight = 40;
    const categoryButtonY = 80; // 顶部导航栏下方

    this.categoryButtons = this.categories.map((category, index) => {
      const button = new Button(
        this.ctx,
        index * categoryButtonWidth,
        categoryButtonY,
        categoryButtonWidth,
        categoryButtonHeight,
        category.name,
        null,
        null,
        () => {
          console.log(`点击分类按钮: ${category.name}, id: ${category.id}`);
          this.selectCategory(category);
        }
      );

      this.addUIElement(button);
      return button;
    });

    // 创建翻页按钮
    const pageButtonWidth = 100;
    const pageButtonHeight = 40;
    const pageButtonY = this.screenHeight - tabBarHeight - pageButtonHeight - 10;

    // 上一页按钮
    this.prevPageButton = new Button(
      this.ctx,
      10,
      pageButtonY,
      pageButtonWidth,
      pageButtonHeight,
      '上一页',
      null,
      null,
      () => {
        this.prevPage();
      }
    );

    this.addUIElement(this.prevPageButton);

    // 下一页按钮
    this.nextPageButton = new Button(
      this.ctx,
      this.screenWidth - pageButtonWidth - 10,
      pageButtonY,
      pageButtonWidth,
      pageButtonHeight,
      '下一页',
      null,
      null,
      () => {
        this.nextPage();
      }
    );

    this.addUIElement(this.nextPageButton);
  }

  // 底部导航栏选中回调
  onTabSelected(index) {
    // 如果点击的是当前选中的项，不做处理
    if (this.selectedTabIndex === index) {
      return;
    }

    // 更新选中的索引
    this.selectedTabIndex = index;

    // 根据索引切换场景
    switch (index) {
      case 0:
        // 主页
        this.sceneManager.showScene('main');
        break;
      case 1:
        // 角色页面 - 直接进入女剑仙角色详情页
        this.sceneManager.showScene('characterDetail', { characterId: 1 });
        break;
      case 2:
        // 洞府页面
        this.sceneManager.showScene('dongfu');
        break;
      case 3:
        // 试炼页面
        this.sceneManager.showScene('trial');
        break;
      case 4:
        // 背包页面，已经在背包页面，不需要切换
        break;
    }
  }

  // 选择分类
  selectCategory(category) {
    console.log(`选择分类: ${category.name}, id: ${category.id}`);
    this.selectedCategory = category;
    this.currentPage = 0;
    this.updateDisplayItems();
  }

  // 上一页
  prevPage() {
    if (this.currentPage > 0) {
      this.currentPage--;
      this.updateDisplayItems();
    }
  }

  // 下一页
  nextPage() {
    // 计算总物品数，避免调用filterItemsByCategory
    let totalItems = 0;
    if (this.selectedCategory.id === 'all') {
      totalItems = (this.items || []).length + (this.equipments || []).length;
    } else if (this.selectedCategory.id === 'equipment') {
      const equipmentsFromArray = this.equipments || [];
      const equipmentsFromItems = (this.items || []).filter(item => this.isEquipment(item));
      totalItems = equipmentsFromArray.length + equipmentsFromItems.length;
    } else {
      totalItems = (this.items || []).filter(item => item.type === this.selectedCategory.id).length;
    }
    
    const totalPages = Math.ceil(totalItems / this.itemsPerPage);
    if (this.currentPage < totalPages - 1) {
      this.currentPage++;
      this.updateDisplayItems();
    }
  }

  // 按类别筛选物品
  filterItemsByCategory() {
    let filteredItems = [];

    if (this.selectedCategory.id === 'all') {
      // 全部物品
      filteredItems = [...this.items, ...this.equipments];
      console.log('筛选全部物品和装备:', filteredItems.length);
    } else if (this.selectedCategory.id === 'equipment') {
      // 仅装备
      // 1. 从装备专用数组中获取装备
      const equipmentsFromArray = this.equipments;

      // 2. 从物品数组中筛选出装备类型的物品
      const equipmentsFromItems = this.items.filter(item => this.isEquipment(item));

      // 3. 合并两个数组
      filteredItems = [...equipmentsFromArray, ...equipmentsFromItems];

      console.log('筛选仅装备:', filteredItems.length);
      console.log('- 来自equipments数组:', equipmentsFromArray.length);
      console.log('- 来自items数组的装备:', equipmentsFromItems.length);

      // 输出装备详情进行调试
      if (filteredItems.length > 0) {
        console.log('装备列表中的第一件装备:', JSON.stringify(filteredItems[0]));

        // 打印所有装备以检查
        console.log('所有装备详情:', filteredItems.map(equip =>
          `${equip.name} (${equip.type}, ${equip.quality})`
        ).join(', '));
      } else {
        console.log('没有找到任何装备，请检查GameStateManager中的getEquipments方法和forgeScene锻造方法');
      }
    } else {
      // 按类型筛选消耗品和材料
      filteredItems = this.items.filter(item => item.type === this.selectedCategory.id);
      console.log(`筛选${this.selectedCategory.id}类别:`, filteredItems.length);
    }

    return filteredItems;
  }

  // 更新显示的物品列表
  updateDisplayItems() {
    // 不再重新获取数据，使用已缓存的数据
    // 确保数据存在
    if (!this.items) this.items = [];
    if (!this.equipments) this.equipments = [];

    console.log('更新显示物品 - 当前分类:', this.selectedCategory.name);
    console.log('物品数量:', this.items.length, '装备数量:', this.equipments.length);

    const filteredItems = this.filterItemsByCategory();
    const startIndex = this.currentPage * this.itemsPerPage;
    const endIndex = Math.min(startIndex + this.itemsPerPage, filteredItems.length);

    this.displayItems = filteredItems.slice(startIndex, endIndex);
    console.log(`当前页面(${this.currentPage + 1})显示物品数量:`, this.displayItems.length);

    // 更新翻页按钮状态
    this.prevPageButton.isEnabled = this.currentPage > 0;

    const totalPages = Math.ceil(filteredItems.length / this.itemsPerPage);
    this.nextPageButton.isEnabled = this.currentPage < totalPages - 1;
  }

  // 场景显示时的回调
  onShow(params) {
    console.log('BackpackScene.onShow() 打开背包页面');

    // 清空UI元素
    this.clearUIElements();

    // 初始化UI（现在在onShow中调用，而不是构造函数中）
    this.initUI();

    // 更新选中的导航项
    this.selectedTabIndex = 4;

    // 只在第一次显示或者没有数据时获取物品和装备
    if (!this.items || !this.equipments || (this.items.length === 0 && this.equipments.length === 0)) {
      try {
        this.items = game.gameStateManager ? (game.gameStateManager.getItems() || []) : [];
        this.equipments = game.gameStateManager ? (game.gameStateManager.getEquipments() || []) : [];
      } catch (error) {
        console.error('获取物品数据失败:', error);
        this.items = [];
        this.equipments = [];
      }
    }

    // 如果没有物品，添加一些测试数据
    if (this.items.length === 0 && this.equipments.length === 0) {
      this.items = [
        { id: 1, name: '回血丹', type: 'consumable', quality: 1, count: 5, description: '恢复100点生命值' },
        { id: 2, name: '灵石', type: 'material', quality: 1, count: 1000, description: '修炼必备的基础材料' },
        { id: 3, name: '铁矿石', type: 'material', quality: 1, count: 50, description: '用于锻造装备的基础材料' },
        { id: 4, name: '聚气丹', type: 'consumable', quality: 2, count: 3, description: '增加修炼速度的丹药' }
      ];
      
      this.equipments = [
        { 
          id: 101, 
          name: '青锋剑', 
          type: 'weapon', 
          quality: 2, 
          level: 1,
          attributes: { attack: 50, critRate: 0.1 },
          description: '一把锋利的青色长剑'
        },
        { 
          id: 102, 
          name: '布衣', 
          type: 'armor', 
          quality: 1, 
          level: 1,
          attributes: { hp: 100, defense: 20 },
          description: '简单的布制衣服'
        },
        { 
          id: 103, 
          name: '护心镜', 
          type: 'accessory', 
          quality: 3, 
          level: 2,
          attributes: { hp: 200, defense: 50, daoRule: 10 },
          description: '能够保护心脏的神奇镜子'
        }
      ];
    }

    // 记录日志，用于调试
    console.log('背包页面获取到物品数量:', this.items.length);
    console.log('背包页面获取到装备数量:', this.equipments.length);

    // 确保默认选择第一个分类（全部）
    this.selectedCategory = this.categories[0];
    this.currentPage = 0;

    // 如果传入了指定分类，则选择该分类
    if (params && params.category) {
      const categoryToSelect = this.categories.find(c => c.id === params.category);
      if (categoryToSelect) {
        console.log('选中指定分类:', categoryToSelect.name);
        this.selectedCategory = categoryToSelect;
      }
    }

    // 更新显示的物品
    this.updateDisplayItems();

    // 设置为可见
    this.visible = true;
  }

  // 场景隐藏时的回调
  onHide() {
    console.log('BackpackScene.onHide() 关闭背包页面');

    // 清空UI元素
    this.clearUIElements();

    // 设置为不可见
    this.visible = false;
  }

  // 处理触摸结束事件
  handleTouchEnd(x, y) {
    // 检查底部导航栏点击
    const tabBarHeight = 80;
    const tabBarY = this.screenHeight - tabBarHeight;
    
    if (y >= tabBarY) {
      const tabWidth = this.screenWidth / 5;
      const tabIndex = Math.floor(x / tabWidth);
      
      if (tabIndex >= 0 && tabIndex < 5) {
        this.onTabSelected(tabIndex);
        return true; // 表示事件已处理
      }
    }

    // 检查分类按钮点击
    const headerHeight = 120; // 包含安全区域的总高度
    const categoryHeight = 50;
    const categoryY = headerHeight + 10;
    
    if (y >= categoryY && y <= categoryY + categoryHeight) {
      const categoryWidth = this.screenWidth / this.categories.length;
      const categoryIndex = Math.floor(x / categoryWidth);
      
      if (categoryIndex >= 0 && categoryIndex < this.categories.length) {
        this.selectCategory(this.categories[categoryIndex]);
        return true; // 表示事件已处理
      }
    }

    // 调用父类的触摸处理
    return super.handleTouchEnd(x, y);
  }

  // 子类实现的触摸开始事件处理
  handleTouchStart(x, y) {
    // 检查是否点击了物品
    const item = this.getItemAtPosition(x, y);
    if (item) {
      this.showItemDetail(item);
      return true;
    }

    return false;
  }

  // 获取指定位置的物品
  getItemAtPosition(x, y) {
    // 顶部导航栏和分类按钮的高度
    const headerHeight = 120;

    // 底部导航栏高度
    const tabBarHeight = 60;

    // 翻页按钮高度
    const pageButtonHeight = 40;

    // 可用高度
    const availableHeight = this.screenHeight - headerHeight - tabBarHeight - pageButtonHeight - 20;

    // 物品格子尺寸
    const gridSize = Math.min(this.screenWidth / 4, availableHeight / 3);

    // 计算行列
    const row = Math.floor((y - headerHeight) / gridSize);
    const col = Math.floor(x / gridSize);

    // 计算索引
    const index = row * 4 + col;

    // 检查索引是否有效
    if (index >= 0 && index < this.displayItems.length) {
      return this.displayItems[index];
    }

    return null;
  }

  // 显示物品详情
  showItemDetail(item) {
    console.log('显示物品详情:', item.name);

    // 构建显示的详情内容
    let title = item.name;
    let content = '';

    // 显示装备信息
    if (this.isEquipment(item)) {
      // 显示品质和类型
      const qualityNames = {
        'common': '普通',
        'uncommon': '优秀',
        'rare': '精良',
        'epic': '史诗',
        'legendary': '传说',
        0: '普通',
        1: '优秀',
        2: '精良',
        3: '史诗',
        4: '传说'
      };

      const typeName = this.getEquipmentTypeName(item.type);
      const qualityName = qualityNames[item.quality] || '未知';

      content += `品质: ${qualityName}\n`;
      content += `类型: ${typeName}\n`;
      content += `等级: ${item.level || 1}\n\n`;

      // 显示装备属性
      content += `【装备属性】\n`;
      if (item.attributes) {
        for (const [attr, value] of Object.entries(item.attributes)) {
          switch(attr) {
            case 'hp':
              content += `生命值: +${value}\n`;
              break;
            case 'attack':
              content += `攻击力: +${value}\n`;
              break;
            case 'defense':
              content += `防御力: +${value}\n`;
              break;
            case 'speed':
              content += `速度: +${value}\n`;
              break;
            case 'critRate':
              content += `暴击率: +${(value * 100).toFixed(1)}%\n`;
              break;
            case 'critDamage':
              content += `暴击伤害: +${(value * 100).toFixed(1)}%\n`;
              break;
            case 'penetration':
              content += `穿透: +${value}\n`;
              break;
            case 'daoRule':
              content += `道韵: +${value}\n`;
              break;
            default:
              content += `${attr}: +${value}\n`;
          }
        }
      } else {
        content += "无属性加成\n";
      }

      // 显示装备描述
      if (item.description) {
        content += `\n【描述】\n${item.description}\n`;
      }
    }
    // 显示普通物品信息
    else {
      content += `类型: ${item.type || '普通物品'}\n`;
      if (item.count && item.count > 1) {
        content += `数量: ${item.count}\n`;
      }

      if (item.description) {
        content += `\n【描述】\n${item.description}\n`;
      }

      if (item.effect) {
        content += `\n【效果】\n`;
        for (const [effect, value] of Object.entries(item.effect)) {
          content += `${effect}: ${value}\n`;
        }
      }
    }

    // 显示物品详情对话框
    this.showDialog(title, content);
  }

  // 显示对话框
  showDialog(title, message) {
    // 设置对话框状态
    this.dialogTitle = title;
    this.dialogMessage = message;
    this.showingDialog = true;

    // 备份现有UI元素
    this.backupUIElements = [...this.uiElements];

    // 清除当前UI
    this.clearUIElements();

    // 创建确定按钮
    const okButton = new Button(
      this.ctx,
      this.screenWidth / 2 - 50,
      this.screenHeight - 100,
      100,
      40,
      '确定',
      null,
      null,
      () => {
        // 关闭对话框
        this.showingDialog = false;
        this.dialogTitle = null;
        this.dialogMessage = null;

        // 恢复UI元素
        this.uiElements = this.backupUIElements;
        this.backupUIElements = null;
      }
    );

    // 添加确定按钮
    this.addUIElement(okButton);
  }

  // 子类实现的绘制逻辑
  drawScene() {
    // 绘制背景
    this.drawBackground();

    // 如果显示对话框，绘制对话框
    if (this.showingDialog && this.dialogTitle && this.dialogMessage) {
      this.drawDialog();
      return; // 对话框显示时不绘制其他内容
    }

    // 绘制顶部导航栏
    this.drawHeader();

    // 绘制分类按钮
    this.drawCategories();

    // 绘制物品列表
    this.drawItems();

    // 绘制底部导航栏
    this.drawTabBar();
  }

  // 绘制背景
  drawBackground() {
    // 如果有背景图资源，使用背景图
    if (this.resources && this.resources.mainBg) {
      try {
        // 确保背景图覆盖整个屏幕，包括底部导航栏区域
        const bgWidth = this.screenWidth;
        const bgHeight = this.screenHeight; // 使用屏幕高度确保完全覆盖

        this.ctx.drawImage(
          this.resources.mainBg,
          0,
          0,
          bgWidth,
          bgHeight
        );
      } catch (error) {
        console.error('绘制背景图失败', error);
        this.drawDefaultBackground();
      }
    } else {
      // 如果没有背景图资源，使用渐变色背景
      this.drawDefaultBackground();
    }
  }

  // 绘制默认背景
  drawDefaultBackground() {
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.screenHeight);
    gradient.addColorStop(0, '#1a2a6c');
    gradient.addColorStop(0.5, '#b21f1f');
    gradient.addColorStop(1, '#fdbb2d');

    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);
  }

  // 绘制顶部导航栏
  drawHeader() {
    const headerHeight = 80;
    const safeAreaHeight = 40;

    // 绘制安全区域
    this.ctx.fillStyle = '#000000';
    this.ctx.fillRect(0, 0, this.screenWidth, safeAreaHeight);

    // 绘制顶部导航栏背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, safeAreaHeight, this.screenWidth, headerHeight - safeAreaHeight);

    // 绘制背包标题
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.fillText('背包', this.screenWidth / 2, safeAreaHeight + 30);

    // 绘制玩家信息
    try {
      const player = game.gameStateManager ? game.gameStateManager.getPlayer() : null;
      if (player) {
        // 绘制玩家昵称和等级
        this.ctx.font = '16px Arial';
        this.ctx.textAlign = 'left';
        this.ctx.fillText(`${player.nickname} Lv.${player.level}`, 20, safeAreaHeight + 60);

        // 绘制资源信息
        this.ctx.textAlign = 'right';
        this.ctx.fillText(`灵石: ${player.resources.lingshi || 0}`, this.screenWidth - 20, safeAreaHeight + 60);
      }
    } catch (error) {
      console.error('背包场景获取玩家信息失败:', error);
    }
  }

  // 绘制分类按钮
  drawCategories() {
    const headerHeight = 120; // 包含安全区域的总高度
    const categoryHeight = 50;
    const categoryY = headerHeight + 10;
    const categoryWidth = this.screenWidth / this.categories.length;

    // 绘制分类按钮背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    this.ctx.fillRect(0, categoryY, this.screenWidth, categoryHeight);

    // 绘制每个分类按钮
    for (let i = 0; i < this.categories.length; i++) {
      const category = this.categories[i];
      const categoryX = i * categoryWidth;
      const isSelected = this.selectedCategory.id === category.id;

      // 绘制按钮背景
      if (isSelected) {
        this.ctx.fillStyle = 'rgba(255, 215, 0, 0.3)';
        this.ctx.fillRect(categoryX + 2, categoryY + 2, categoryWidth - 4, categoryHeight - 4);
      }

      // 绘制分类文字
      this.ctx.font = '18px Arial';
      this.ctx.fillStyle = isSelected ? '#FFD700' : '#ffffff';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(category.name, categoryX + categoryWidth / 2, categoryY + 30);
    }
  }

  // 绘制物品列表
  drawItems() {
    const headerHeight = 120; // 包含安全区域的总高度
    const categoryHeight = 50;
    const itemAreaY = headerHeight + categoryHeight + 20;
    const itemAreaHeight = this.screenHeight - itemAreaY - 100; // 留出底部导航栏和翻页按钮空间

    // 直接使用已经处理好的displayItems，避免重复筛选
    const pageItems = this.displayItems || [];

    if (pageItems.length === 0) {
      // 没有物品时显示提示
      this.ctx.font = '18px Arial';
      this.ctx.fillStyle = '#cccccc';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('暂无物品', this.screenWidth / 2, itemAreaY + 100);
      return;
    }

    // 计算网格布局
    const itemsPerRow = 4;
    const itemSize = 80;
    const itemMargin = 10;
    const startX = (this.screenWidth - (itemsPerRow * itemSize + (itemsPerRow - 1) * itemMargin)) / 2;

    // 绘制物品
    pageItems.forEach((item, index) => {
      const row = Math.floor(index / itemsPerRow);
      const col = index % itemsPerRow;
      const x = startX + col * (itemSize + itemMargin);
      const y = itemAreaY + row * (itemSize + itemMargin);

      // 绘制物品背景
      this.ctx.fillStyle = this.getItemQualityColor(item.quality || 1);
      this.ctx.fillRect(x, y, itemSize, itemSize);

      // 绘制物品边框
      this.ctx.strokeStyle = '#ffffff';
      this.ctx.lineWidth = 2;
      this.ctx.strokeRect(x, y, itemSize, itemSize);

      // 绘制物品图标（简单的文字表示）
      this.ctx.font = '12px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(item.name.substring(0, 4), x + itemSize / 2, y + itemSize / 2 - 10);

      // 绘制物品数量（如果有）
      if (item.count && item.count > 1) {
        this.ctx.font = '10px Arial';
        this.ctx.fillStyle = '#ffff00';
        this.ctx.textAlign = 'right';
        this.ctx.fillText(`x${item.count}`, x + itemSize - 5, y + itemSize - 5);
      }

      // 绘制物品等级（如果有）
      if (item.level && item.level > 0) {
        this.ctx.font = '10px Arial';
        this.ctx.fillStyle = '#00ff00';
        this.ctx.textAlign = 'left';
        this.ctx.fillText(`+${item.level}`, x + 5, y + 15);
      }
    });

    // 绘制页码信息 - 计算总页数需要重新筛选，但不输出日志
    let totalItems = 0;
    if (this.selectedCategory.id === 'all') {
      totalItems = (this.items || []).length + (this.equipments || []).length;
    } else if (this.selectedCategory.id === 'equipment') {
      const equipmentsFromArray = this.equipments || [];
      const equipmentsFromItems = (this.items || []).filter(item => this.isEquipment(item));
      totalItems = equipmentsFromArray.length + equipmentsFromItems.length;
    } else {
      totalItems = (this.items || []).filter(item => item.type === this.selectedCategory.id).length;
    }
    
    const totalPages = Math.ceil(totalItems / this.itemsPerPage);
    if (totalPages > 1) {
      this.ctx.font = '14px Arial';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(`${this.currentPage + 1} / ${totalPages}`, this.screenWidth / 2, this.screenHeight - 50);
    }
  }

  // 根据品质获取颜色
  getItemQualityColor(quality) {
    switch (quality) {
      case 1: return '#808080'; // 普通 - 灰色
      case 2: return '#0080ff'; // 稀有 - 蓝色
      case 3: return '#8000ff'; // 史诗 - 紫色
      case 4: return '#ff8000'; // 传说 - 橙色
      case 5: return '#ff0080'; // 神话 - 粉色
      default: return '#808080';
    }
  }

  // 判断是否为装备
  isEquipment(item) {
    return item.type === 'weapon' || item.type === 'armor' || item.type === 'accessory' || item.attributes;
  }

  // 获取装备类型名称
  getEquipmentTypeName(type) {
    const typeNames = {
      'weapon': '武器',
      'armor': '护甲', 
      'accessory': '饰品',
      'helmet': '头盔',
      'boots': '靴子',
      'gloves': '手套'
    };
    return typeNames[type] || '未知';
  }

  // 绘制底部导航栏
  drawTabBar() {
    const tabBarHeight = 80;
    const tabBarY = this.screenHeight - tabBarHeight;
    const tabWidth = this.screenWidth / 5;

    // 导航栏数据
    const tabData = [
      { icon: '🏠', text: '主页', index: 0 },
      { icon: '👤', text: '角色', index: 1 },
      { icon: '🏔️', text: '洞府', index: 2 },
      { icon: '⚔️', text: '试炼', index: 3 },
      { icon: '🎒', text: '背包', index: 4 }
    ];

    // 绘制每个导航按钮
    for (let i = 0; i < tabData.length; i++) {
      const tab = tabData[i];
      const tabX = i * tabWidth;
      const centerX = tabX + tabWidth / 2;
      const isSelected = this.selectedTabIndex === i;

      // 绘制按钮背景（圆角矩形）
      const buttonRadius = 25;
      const buttonY = tabBarY + 10;
      const buttonHeight = 60;

      // 使用BaseScene的drawRoundRect方法
      const fillStyle = isSelected ? 'rgba(255, 215, 0, 0.3)' : 'rgba(255, 255, 255, 0.1)';
      this.drawRoundRect(tabX + 5, buttonY, tabWidth - 10, buttonHeight, buttonRadius, fillStyle);

      // 绘制图标
      const iconSize = isSelected ? 32 : 28;
      this.ctx.font = `${iconSize}px Arial`;
      this.ctx.textAlign = 'center';
      this.ctx.fillText(tab.icon, centerX, tabBarY + 35);

      // 绘制文字
      this.ctx.font = '12px Arial';
      this.ctx.fillStyle = isSelected ? '#FFD700' : '#FFFFFF';
      this.ctx.fillText(tab.text, centerX, tabBarY + 55);
    }
  }

  // 绘制对话框
  drawDialog() {
    // 绘制半透明背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.fillRect(0, 0, this.screenWidth, this.screenHeight);

    // 对话框尺寸
    const dialogWidth = this.screenWidth * 0.8;
    const dialogHeight = this.screenHeight * 0.7;
    const dialogX = (this.screenWidth - dialogWidth) / 2;
    const dialogY = (this.screenHeight - dialogHeight) / 2;

    // 绘制对话框背景
    this.ctx.fillStyle = '#2c3e50';
    this.ctx.fillRect(dialogX, dialogY, dialogWidth, dialogHeight);

    // 绘制对话框边框
    this.ctx.strokeStyle = '#ffffff';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(dialogX, dialogY, dialogWidth, dialogHeight);

    // 绘制标题
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'top';
    this.ctx.fillText(this.dialogTitle, this.screenWidth / 2, dialogY + 20);

    // 绘制分隔线
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
    this.ctx.lineWidth = 1;
    this.ctx.beginPath();
    this.ctx.moveTo(dialogX + 20, dialogY + 60);
    this.ctx.lineTo(dialogX + dialogWidth - 20, dialogY + 60);
    this.ctx.stroke();

    // 绘制内容
    this.ctx.font = '16px Arial';
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.textBaseline = 'top';

    // 多行文本渲染
    const lines = this.dialogMessage.split('\n');
    const lineHeight = 20;
    let y = dialogY + 80;

    for (let i = 0; i < lines.length; i++) {
      if (y + lineHeight > dialogY + dialogHeight - 60) break; // 防止超出对话框
      this.ctx.fillText(lines[i], dialogX + 30, y);
      y += lineHeight;
    }
  }
}

export default BackpackScene;